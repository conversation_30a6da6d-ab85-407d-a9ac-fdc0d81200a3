import React, { useState } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import {
  Plus,
  Trash2,
  TrendingUp,
  TrendingDown
} from 'lucide-react';

interface PortfolioHolding {
  ticker: string;
  company_name: string;
  shares: number;
  average_cost: number;
  current_price: number;
  market_value: number;
  unrealized_gain_loss: number;
  unrealized_gain_loss_percent: number;
  sector: string;
}

interface HoldingFormData {
  ticker: string;
  shares: string;
  price: string;
}

interface HoldingsManagerProps {
  holdings: PortfolioHolding[];
  onAddHolding: (ticker: string, shares: number, price: number) => void;
  onUpdateHolding: (ticker: string, shares: number, price: number) => void;
  onRemoveHolding: (ticker: string) => void;
  userTier: 'Basic' | 'Pro';
}

export const HoldingsManager: React.FC<HoldingsManagerProps> = ({
  holdings,
  onAddHolding,
  onUpdateHolding,
  onRemoveHolding,
  userTier
}) => {
  const [showAddForm, setShowAddForm] = useState(false);
  const [formData, setFormData] = useState<HoldingFormData>({
    ticker: '',
    shares: '',
    price: ''
  });

  const handleSubmit = () => {
    const ticker = formData.ticker.toUpperCase().trim();
    const shares = parseFloat(formData.shares);
    const price = parseFloat(formData.price);

    if (!ticker || shares <= 0 || price <= 0) {
      return;
    }

    onAddHolding(ticker, shares, price);
    setShowAddForm(false);
    setFormData({ ticker: '', shares: '', price: '' });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  };

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white">Your Holdings</h3>
          <Button
            onClick={() => setShowAddForm(!showAddForm)}
            className="bg-blue-600 hover:bg-blue-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Holding
          </Button>
        </div>

        {/* Simple Add Form */}
        {showAddForm && (
          <div className="mb-6 p-4 bg-white/5 rounded-lg">
            <h4 className="text-white font-medium mb-4">Add New Holding</h4>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Input
                placeholder="Ticker (e.g., AAPL)"
                value={formData.ticker}
                onChange={(e) => setFormData({...formData, ticker: e.target.value.toUpperCase()})}
                className="text-white"
              />
              <Input
                type="number"
                placeholder="Shares"
                value={formData.shares}
                onChange={(e) => setFormData({...formData, shares: e.target.value})}
                className="text-white"
              />
              <Input
                type="number"
                step="0.01"
                placeholder="Price per share"
                value={formData.price}
                onChange={(e) => setFormData({...formData, price: e.target.value})}
                className="text-white"
              />
              <Button
                onClick={handleSubmit}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                Add
              </Button>
            </div>
          </div>
        )}

        {/* Holdings List */}
        <div className="space-y-3">
          {holdings.map((holding) => (
            <div key={holding.ticker} className="flex items-center justify-between p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold">{holding.ticker}</span>
                </div>
                <div>
                  <p className="text-white font-medium">{holding.company_name}</p>
                  <p className="text-gray-400 text-sm">
                    {holding.shares} shares @ {formatCurrency(holding.average_cost)}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white font-semibold">{formatCurrency(holding.market_value)}</p>
                <div className="flex items-center justify-end space-x-1">
                  {holding.unrealized_gain_loss >= 0 ? (
                    <TrendingUp className="h-4 w-4 text-green-400" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-400" />
                  )}
                  <p className={`text-sm ${
                    holding.unrealized_gain_loss >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {formatCurrency(holding.unrealized_gain_loss)} ({formatPercent(holding.unrealized_gain_loss_percent)})
                  </p>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => onRemoveHolding(holding.ticker)}
                  className="mt-2 border-red-500 text-red-400 hover:bg-red-900/20"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Remove
                </Button>
              </div>
            </div>
          ))}
        </div>

        {holdings.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 mb-2">No holdings yet</div>
            <p className="text-gray-500 text-sm">
              Add your first investment to start tracking your portfolio
            </p>
          </div>
        )}
      </Card>
    </div>
  );
};
