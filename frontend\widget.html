<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>FinanceGPT Widget</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html, body {
      height: 100%;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
        'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
        sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
      overflow: hidden;
    }
    
    #finance-gpt-widget-root {
      height: 100vh;
      width: 100vw;
      overflow: hidden;
    }
    
    .standalone-widget {
      height: 100%;
      width: 100%;
    }
    
    /* Loading spinner */
    .loading-spinner {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      color: white;
    }
    
    .spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #ffffff20;
      border-top: 4px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    /* Compact mode styles */
    .compact .finance-gpt-widget {
      font-size: 14px;
    }
    
    .compact .finance-gpt-widget h1 {
      font-size: 1.5rem;
    }
    
    .compact .finance-gpt-widget h2 {
      font-size: 1.25rem;
    }
    
    .compact .finance-gpt-widget h3 {
      font-size: 1.1rem;
    }
    
    /* Error state */
    .error-container {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 100vh;
      color: white;
      text-align: center;
      padding: 20px;
    }
    
    .error-icon {
      font-size: 48px;
      margin-bottom: 16px;
      color: #ef4444;
    }
    
    .error-title {
      font-size: 24px;
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .error-message {
      font-size: 16px;
      color: #9ca3af;
      max-width: 400px;
    }
  </style>
</head>
<body>
  <div id="finance-gpt-widget-root">
    <!-- Loading state -->
    <div class="loading-spinner" id="loading">
      <div>
        <div class="spinner"></div>
        <p style="margin-top: 16px; color: #9ca3af;">Loading FinanceGPT...</p>
      </div>
    </div>
  </div>

  <script>
    // Error handling
    window.addEventListener('error', function(event) {
      console.error('Widget error:', event.error);
      showError('Failed to load FinanceGPT widget', event.error.message);
    });

    window.addEventListener('unhandledrejection', function(event) {
      console.error('Unhandled promise rejection:', event.reason);
      showError('Widget initialization failed', event.reason);
    });

    function showError(title, message) {
      const root = document.getElementById('finance-gpt-widget-root');
      root.innerHTML = `
        <div class="error-container">
          <div class="error-icon">⚠️</div>
          <div class="error-title">${title}</div>
          <div class="error-message">${message}</div>
        </div>
      `;
    }

    // Hide loading spinner when widget loads
    function hideLoading() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    }

    // Configuration validation
    function validateConfig() {
      const urlParams = new URLSearchParams(window.location.search);
      const configParam = urlParams.get('config');
      
      if (configParam) {
        try {
          JSON.parse(atob(configParam));
          return true;
        } catch (error) {
          showError('Invalid Configuration', 'The widget configuration is malformed.');
          return false;
        }
      }
      return true;
    }

    // Initialize only if configuration is valid
    if (validateConfig()) {
      // Widget will be loaded by the React script
      setTimeout(hideLoading, 2000); // Fallback to hide loading
    }
  </script>
</body>
</html>
