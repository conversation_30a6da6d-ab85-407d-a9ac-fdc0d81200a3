from dataclasses import dataclass
import os
from langchain_google_genai import ChatGoogleGenerativeAI

@dataclass
class ApiKeys:
    gemini_api_key: str
    google_search_key: str
    google_search_cse_id: str
    stocks_api_key: str

@dataclass
class Config:
    langgraph_url: str
    gemini_model: ChatGoogleGenerativeAI
    api_keys: <PERSON><PERSON><PERSON><PERSON><PERSON>

def get_config() -> Config:
    gemini_api_key = os.getenv("GEMINI_API_KEY")
    google_search_key = os.getenv("GOOGLE_SEARCH_API_KEY", "")
    google_search_cse_id = os.getenv("GOOGLE_SEARCH_ENGINE_ID", "")
    stocks_api_key = os.getenv("STOCK_API_KEY", "")

    if not gemini_api_key:
        # For development, provide a more helpful error message
        raise ValueError(
            "GEMINI_API_KEY is required. Please:\n"
            "1. Get an API key from https://ai.google.dev/\n"
            "2. Set it as an environment variable: GEMINI_API_KEY=your_key_here\n"
            "3. Or create a .env file in the backend directory"
        )
    if not google_search_key or not google_search_cse_id:
        print("Warning: GOOGLE_SEARCH_API_KEY or GOOGLE_SEARCH_ENGINE_ID not set, web search disabled")
    if not stocks_api_key:
        print("Warning: STOCK_API_KEY not set, stock data disabled")
        
    api_keys = ApiKeys(
        gemini_api_key=gemini_api_key,
        google_search_key=google_search_key,
        google_search_cse_id=google_search_cse_id,
        stocks_api_key=stocks_api_key
    )
    
    return Config(
        langgraph_url=os.getenv("LANGGRAPH_URL", "http://localhost:2024"),
        gemini_model=ChatGoogleGenerativeAI(
            model="gemini-1.5-pro",
            google_api_key=gemini_api_key,
            temperature=0.7
        ),
        api_keys=api_keys
    )