<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FinanceGPT Widget - Basic Integration</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .widget-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .widget-title {
            background: #3b82f6;
            color: white;
            padding: 15px 20px;
            margin: 0;
            font-size: 18px;
        }
        
        .controls {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
        }
        
        .control-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        select, button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        button {
            background: #3b82f6;
            color: white;
            border: none;
            cursor: pointer;
            margin-right: 10px;
        }
        
        button:hover {
            background: #2563eb;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>FinanceGPT Widget Integration Examples</h1>
            <p>Demonstrating different integration methods and configurations</p>
        </div>

        <!-- Chat Widget Example -->
        <div class="widget-container">
            <h2 class="widget-title">Chat Widget (Script Tag Integration)</h2>
            <div id="chat-widget" style="height: 500px;"></div>
            <div class="controls">
                <div class="control-group">
                    <label for="chat-mode">Mode:</label>
                    <select id="chat-mode">
                        <option value="chat">Chat Only</option>
                        <option value="portfolio">Portfolio Only</option>
                        <option value="analytics">Analytics Only</option>
                        <option value="full">Full Widget</option>
                    </select>
                </div>
                <button onclick="updateChatWidget()">Update Widget</button>
                <button onclick="destroyChatWidget()">Destroy Widget</button>
                <div id="chat-status" class="status" style="display: none;"></div>
            </div>
        </div>

        <!-- Iframe Widget Example -->
        <div class="widget-container">
            <h2 class="widget-title">Portfolio Widget (Iframe Integration)</h2>
            <div id="iframe-widget" style="height: 600px;"></div>
            <div class="controls">
                <div class="control-group">
                    <label for="user-tier">User Tier:</label>
                    <select id="user-tier">
                        <option value="free">Free</option>
                        <option value="pro">Pro</option>
                    </select>
                </div>
                <button onclick="updateIframeWidget()">Update User Tier</button>
                <button onclick="destroyIframeWidget()">Destroy Widget</button>
                <div id="iframe-status" class="status" style="display: none;"></div>
            </div>
        </div>

        <!-- Event Log -->
        <div class="widget-container">
            <h2 class="widget-title">Event Log</h2>
            <div id="event-log" style="height: 200px; overflow-y: auto; padding: 15px; background: #f8f9fa; font-family: monospace; font-size: 12px;"></div>
            <div class="controls">
                <button onclick="clearEventLog()">Clear Log</button>
            </div>
        </div>
    </div>

    <!-- Load FinanceGPT Widget Script -->
    <script src="../dist-widget/finance-gpt-widget.js"></script>
    
    <script>
        let chatWidget = null;
        let iframeWidget = null;

        // Event logging
        function logEvent(message) {
            const log = document.getElementById('event-log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}\n`;
            log.scrollTop = log.scrollHeight;
        }

        function showStatus(elementId, message, isError = false) {
            const status = document.getElementById(elementId);
            status.textContent = message;
            status.className = `status ${isError ? 'error' : 'success'}`;
            status.style.display = 'block';
            setTimeout(() => {
                status.style.display = 'none';
            }, 3000);
        }

        function clearEventLog() {
            document.getElementById('event-log').innerHTML = '';
        }

        // Initialize Chat Widget
        function initializeChatWidget() {
            try {
                const mode = document.getElementById('chat-mode').value;
                
                chatWidget = FinanceGPT.init({
                    containerId: 'chat-widget',
                    config: {
                        mode: mode,
                        height: '100%',
                        user: {
                            id: 'demo-user',
                            name: 'Demo User',
                            tier: 'free',
                            tokens: 500
                        },
                        styling: {
                            borderRadius: '0px'
                        },
                        onUserAction: (action, data) => {
                            logEvent(`Chat Widget - User Action: ${action}`);
                        },
                        onPlanChange: (newPlan) => {
                            logEvent(`Chat Widget - Plan Changed: ${newPlan}`);
                        },
                        onTokenUsage: (tokensUsed) => {
                            logEvent(`Chat Widget - Tokens Used: ${tokensUsed}`);
                        }
                    },
                    onReady: () => {
                        logEvent('Chat Widget - Ready');
                        showStatus('chat-status', 'Widget initialized successfully');
                    },
                    onError: (error) => {
                        logEvent(`Chat Widget - Error: ${error.message}`);
                        showStatus('chat-status', `Error: ${error.message}`, true);
                    }
                });
            } catch (error) {
                logEvent(`Chat Widget - Initialization Error: ${error.message}`);
                showStatus('chat-status', `Initialization failed: ${error.message}`, true);
            }
        }

        function updateChatWidget() {
            if (chatWidget) {
                destroyChatWidget();
            }
            initializeChatWidget();
        }

        function destroyChatWidget() {
            if (chatWidget) {
                chatWidget.destroy();
                chatWidget = null;
                logEvent('Chat Widget - Destroyed');
                showStatus('chat-status', 'Widget destroyed');
            }
        }

        // Initialize Iframe Widget
        function initializeIframeWidget() {
            try {
                const userTier = document.getElementById('user-tier').value;
                
                iframeWidget = FinanceGPT.initIframe({
                    containerId: 'iframe-widget',
                    widgetUrl: '../widget.html',
                    config: {
                        mode: 'portfolio',
                        height: '100%',
                        user: {
                            id: 'demo-user-iframe',
                            name: 'Demo User',
                            tier: userTier,
                            tokens: userTier === 'pro' ? 10000 : 500
                        }
                    },
                    onMessage: (data) => {
                        logEvent(`Iframe Widget - Message: ${JSON.stringify(data)}`);
                    },
                    onReady: () => {
                        logEvent('Iframe Widget - Ready');
                        showStatus('iframe-status', 'Iframe widget initialized successfully');
                    }
                });
            } catch (error) {
                logEvent(`Iframe Widget - Initialization Error: ${error.message}`);
                showStatus('iframe-status', `Initialization failed: ${error.message}`, true);
            }
        }

        function updateIframeWidget() {
            if (iframeWidget) {
                const userTier = document.getElementById('user-tier').value;
                iframeWidget.updateConfig({
                    user: {
                        tier: userTier,
                        tokens: userTier === 'pro' ? 10000 : 500
                    }
                });
                logEvent(`Iframe Widget - Updated user tier to: ${userTier}`);
                showStatus('iframe-status', `User tier updated to ${userTier}`);
            }
        }

        function destroyIframeWidget() {
            if (iframeWidget) {
                iframeWidget.destroy();
                iframeWidget = null;
                logEvent('Iframe Widget - Destroyed');
                showStatus('iframe-status', 'Iframe widget destroyed');
            }
        }

        // Initialize widgets on page load
        document.addEventListener('DOMContentLoaded', () => {
            logEvent('Page loaded - Initializing widgets...');
            initializeChatWidget();
            initializeIframeWidget();
        });

        // Cleanup on page unload
        window.addEventListener('beforeunload', () => {
            destroyChatWidget();
            destroyIframeWidget();
        });
    </script>
</body>
</html>
