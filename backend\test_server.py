from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
import json

app = FastAPI(title="FinanceGPT Pro API")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

class AnalysisRequest(BaseModel):
    query: str
    thread_id: Optional[str] = None
    analysis_type: Optional[str] = None
    user_id: Optional[str] = None

class Message(BaseModel):
    role: str
    content: str

@app.get("/")
async def root():
    return {
        "message": "🚀 FinanceGPT Pro API is running!",
        "version": "2.0.0",
        "status": "connected"
    }

@app.post("/analyze")
async def analyze_stock(request: AnalysisRequest):
    """
    Analyze a stock and return professional analysis
    """
    try:
        query = request.query.upper()

        # Detect ticker symbol
        import re
        ticker_match = re.search(r'\b([A-Z]{1,5})\b', query)
        ticker = ticker_match.group(1) if ticker_match else "AAPL"

        # Generate conversational analysis based on query type
        if "price" in query.lower() or "current" in query.lower():
            content = f"""Hey! 👋 Let me check {ticker}'s current price for you.

## 💰 {ticker} Stock Price Right Now

**Current Price**: $175.43 📈
**Today's Change**: +$2.15 (+1.24%) - Looking good!
**Volume**: 45.2M shares traded today

**Quick Stats:**
• **Market Cap**: $2.89T (That's huge! 🚀)
• **P/E Ratio**: 28.5 (Pretty reasonable)
• **52-Week Range**: $164.08 - $199.62

**My Take**: {ticker} is having a decent day with a +1.24% gain. The volume is solid, showing good investor interest.

Want me to dive deeper into the technicals or check the sentiment around {ticker}? Just ask! 😊"""

        elif "buy" in query.lower() or "recommend" in query.lower() or "should i" in query.lower():
            content = f"""Great question! 🤔 Let me analyze {ticker} for you...

## 🎯 My Investment Take on {ticker}

**My Recommendation**: 👍 **BUY**
**Confidence**: 75% (Pretty confident!)

**Here's why I like {ticker} right now:**
• 💪 **Strong fundamentals** - The company's financials look solid
• 📈 **Positive momentum** - Technical indicators are showing bullish signals
• 😊 **Good vibes** - Market sentiment is positive (68% bullish)
• 💰 **Fair valuation** - Not too expensive at current levels

**Risk Check**: Medium risk - It's not a guaranteed win, but the odds look favorable.

**My honest advice**: {ticker} seems like a solid pick for a diversified portfolio. The fundamentals are strong and the technicals are aligned. Just remember to never put all your eggs in one basket! 🥚

Want me to explain any of these factors in more detail? I'm here to help! 😊"""

        elif "sentiment" in query.lower() or "feeling" in query.lower() or "mood" in query.lower():
            content = f"""Let me check what people are saying about {ticker}! 🕵️‍♂️

## 📊 The Vibe Check for {ticker}

**Overall Mood**: 😊 **Pretty Positive!** (Score: 72/100)

**What the internet is saying:**
• 📰 **News**: 68% positive, 22% neutral, 10% negative
• 🐦 **Twitter**: 15.4K mentions today (↑12% from yesterday!)
• 📱 **Reddit**: 2,847 discussions - mostly bullish chatter
• 📺 **Financial Media**: Analysts are feeling optimistic

**Hot Headlines I'm Seeing:**
• "Strong quarterly earnings beat expectations" 📈
• "Analysts upgrade price targets" 🎯
• "Innovation pipeline looks promising" 🚀

**My Read**: The sentiment around {ticker} is definitely leaning positive! People seem excited about the recent developments. Social media buzz is up, and the financial press is saying good things.

Of course, sentiment can change quickly, so keep an eye on it! Want me to explain what's driving this positive sentiment? 🤓"""

        else:
            # Handle greetings and general queries
            if any(word in query.lower() for word in ['hello', 'hi', 'hey', 'good morning', 'good afternoon']):
                content = f"""Hey there! 👋 Great to see you!

I'm your AI financial advisor, and I'm here to help you navigate the markets! 📈

**What I can help you with:**
• 📊 **Stock Analysis** - Get detailed insights on any stock
• 💰 **Price Checks** - Real-time price data and trends
• 🎯 **Investment Advice** - Buy/sell recommendations
• 📰 **Market Sentiment** - What people are saying
• 📈 **Technical Analysis** - Charts, indicators, and patterns

**Just ask me something like:**
• "What's Apple's price?"
• "Should I buy Tesla?"
• "How's the sentiment on Microsoft?"
• "Analyze Amazon stock"

What would you like to know about the markets today? 😊"""

            else:
                # Default comprehensive analysis
                content = f"""Hey! Let me give you the full rundown on {ticker}! 📊

# 🚀 Complete {ticker} Analysis

## 💰 The Numbers Right Now
**Current Price**: $175.43 (Not bad!)
**Today's Volume**: 45.2M shares (Pretty active!)
**Market Cap**: $2.89T (That's massive! 🐋)

## 📈 Technical Picture
**RSI**: 58.2 - Right in the sweet spot (not overbought/oversold)
**MACD**: Just had a bullish crossover! 📈
**Moving Averages**: Price is above both 50-day ($172.45) and 200-day ($168.90) - that's bullish!

## 😊 What People Are Saying
**Sentiment Score**: 72/100 (Pretty positive!)
• News coverage: 68% positive
• Social media: Bullish vibes
• Analysts: 12 Buy, 3 Hold, 1 Sell (mostly bullish!)

## 🎯 My Bottom Line
**Recommendation**: 👍 **BUY**
**Price Target**: $195 (about 11% upside!)
**Risk Level**: Medium (not too risky, not too safe)

**The Story**: {ticker} is looking solid right now. Good technicals, positive sentiment, and the fundamentals seem healthy. It's not a guaranteed winner, but the odds look favorable!

Want me to dive deeper into any specific aspect? I'm here to help! 🤓"""

        return {
            "thread_id": "demo-thread",
            "messages": [
                {
                    "role": "assistant",
                    "content": content
                }
            ],
            "status": "success"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8124)
