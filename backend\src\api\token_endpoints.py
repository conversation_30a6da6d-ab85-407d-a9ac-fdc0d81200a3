"""
Token and Subscription Management API Endpoints
Provides REST API for token purchases, subscription management, and usage tracking
"""

from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import BaseModel, Field
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging

from ..models.token_models import (
    token_manager,
    AnalysisType,
    SubscriptionTier,
    TokenPricing
)
from ..services.subscription_service import subscription_service, BillingCycle
from ..middleware.token_middleware import get_current_user, token_utils

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/tokens", tags=["tokens"])

# Pydantic models for request/response
class TokenPurchaseRequest(BaseModel):
    package_id: str = Field(..., description="Token package ID (starter, professional, enterprise, unlimited)")
    payment_method: Optional[str] = Field("demo", description="Payment method (demo for testing)")

class SubscriptionRequest(BaseModel):
    tier: str = Field(..., description="Subscription tier")
    billing_cycle: str = Field("monthly", description="Billing cycle (monthly/yearly)")

class TokenUsageRequest(BaseModel):
    analysis_type: str = Field(..., description="Type of analysis to perform")

# Token Management Endpoints
@router.get("/status")
async def get_token_status(user = Depends(get_current_user)):
    """Get current token status for the user"""
    try:
        status = token_utils.get_user_token_status(user.user_id)
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"Error getting token status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get token status")

@router.get("/pricing")
async def get_pricing_info():
    """Get current pricing information"""
    try:
        pricing = token_utils.get_pricing_info()
        return {
            "success": True,
            "data": pricing
        }
    except Exception as e:
        logger.error(f"Error getting pricing info: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get pricing information")

@router.post("/purchase")
async def purchase_tokens(
    request: TokenPurchaseRequest,
    user = Depends(get_current_user)
):
    """Purchase token package"""
    try:
        # Validate package ID
        valid_packages = ["starter", "professional", "enterprise", "unlimited"]
        if request.package_id not in valid_packages:
            raise HTTPException(status_code=400, detail="Invalid package ID")
        
        # Process purchase (in demo mode, always succeeds)
        if request.payment_method == "demo":
            result = token_manager.purchase_tokens(user.user_id, request.package_id)
            
            if result["success"]:
                return {
                    "success": True,
                    "message": "Tokens purchased successfully",
                    "data": result
                }
            else:
                raise HTTPException(status_code=400, detail=result["error"])
        else:
            # In production, integrate with payment processor (Stripe, PayPal, etc.)
            raise HTTPException(status_code=501, detail="Payment processing not implemented")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error purchasing tokens: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to purchase tokens")

@router.post("/estimate-cost")
async def estimate_analysis_cost(analysis_types: List[str]):
    """Estimate token cost for multiple analysis types"""
    try:
        costs = token_utils.estimate_analysis_cost(analysis_types)
        return {
            "success": True,
            "data": costs
        }
    except Exception as e:
        logger.error(f"Error estimating costs: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to estimate costs")

@router.get("/usage/analytics")
async def get_usage_analytics(
    days: int = 30,
    user = Depends(get_current_user)
):
    """Get usage analytics for the user"""
    try:
        if days < 1 or days > 365:
            raise HTTPException(status_code=400, detail="Days must be between 1 and 365")
        
        analytics = token_manager.get_user_analytics(user.user_id, days)
        return {
            "success": True,
            "data": analytics
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting usage analytics: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get usage analytics")

@router.get("/transactions")
async def get_token_transactions(
    limit: int = 50,
    user = Depends(get_current_user)
):
    """Get token transaction history"""
    try:
        if limit < 1 or limit > 1000:
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 1000")
        
        # Filter transactions for the user
        user_transactions = [
            {
                "transaction_id": t.transaction_id,
                "transaction_type": t.transaction_type,
                "token_amount": t.token_amount,
                "analysis_type": t.analysis_type.value if t.analysis_type else None,
                "description": t.description,
                "timestamp": t.timestamp.isoformat(),
                "metadata": t.metadata
            }
            for t in token_manager.transactions
            if t.user_id == user.user_id
        ]
        
        # Sort by timestamp (newest first) and limit
        user_transactions.sort(key=lambda x: x["timestamp"], reverse=True)
        user_transactions = user_transactions[:limit]
        
        return {
            "success": True,
            "data": {
                "transactions": user_transactions,
                "total_count": len(user_transactions)
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting transactions: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get transactions")

# Subscription Management Endpoints
@router.get("/subscription/plans")
async def get_subscription_plans():
    """Get available subscription plans"""
    try:
        plans = subscription_service.get_available_plans()
        return {
            "success": True,
            "data": plans
        }
    except Exception as e:
        logger.error(f"Error getting subscription plans: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get subscription plans")

@router.get("/subscription/status")
async def get_subscription_status(user = Depends(get_current_user)):
    """Get current subscription status"""
    try:
        status = subscription_service.get_subscription_status(user.user_id)
        return {
            "success": True,
            "data": status
        }
    except Exception as e:
        logger.error(f"Error getting subscription status: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get subscription status")

@router.post("/subscription/subscribe")
async def subscribe_to_plan(
    request: SubscriptionRequest,
    user = Depends(get_current_user)
):
    """Subscribe to a new plan"""
    try:
        # Validate tier
        try:
            tier = SubscriptionTier(request.tier)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid subscription tier")
        
        # Validate billing cycle
        try:
            billing_cycle = BillingCycle(request.billing_cycle)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid billing cycle")
        
        result = subscription_service.subscribe_user(user.user_id, tier, billing_cycle)
        
        if result["success"]:
            return {
                "success": True,
                "message": "Subscription created successfully",
                "data": result["subscription"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error subscribing user: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create subscription")

@router.post("/subscription/upgrade")
async def upgrade_subscription(
    request: SubscriptionRequest,
    user = Depends(get_current_user)
):
    """Upgrade to a higher tier"""
    try:
        # Validate tier
        try:
            tier = SubscriptionTier(request.tier)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid subscription tier")
        
        result = subscription_service.upgrade_subscription(user.user_id, tier)
        
        if result["success"]:
            return {
                "success": True,
                "message": "Subscription upgraded successfully",
                "data": result["upgrade"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error upgrading subscription: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to upgrade subscription")

@router.post("/subscription/cancel")
async def cancel_subscription(
    immediate: bool = False,
    user = Depends(get_current_user)
):
    """Cancel subscription"""
    try:
        result = subscription_service.cancel_subscription(user.user_id, immediate)
        
        if result["success"]:
            return {
                "success": True,
                "message": "Subscription cancelled successfully",
                "data": result["cancellation"]
            }
        else:
            raise HTTPException(status_code=400, detail=result["error"])
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error cancelling subscription: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to cancel subscription")

@router.get("/subscription/recommendations")
async def get_subscription_recommendations(user = Depends(get_current_user)):
    """Get subscription recommendations based on usage"""
    try:
        recommendations = subscription_service.get_usage_recommendations(user.user_id)
        return {
            "success": True,
            "data": recommendations
        }
    except Exception as e:
        logger.error(f"Error getting recommendations: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get recommendations")

@router.get("/subscription/history")
async def get_subscription_history(user = Depends(get_current_user)):
    """Get subscription history"""
    try:
        history = subscription_service.get_subscription_history(user.user_id)
        return {
            "success": True,
            "data": {
                "history": history,
                "total_count": len(history)
            }
        }
    except Exception as e:
        logger.error(f"Error getting subscription history: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get subscription history")

# Admin endpoints (would require admin authentication in production)
@router.post("/admin/billing-cycle")
async def process_billing_cycle():
    """Process monthly billing cycle (admin only)"""
    try:
        result = subscription_service.process_billing_cycle()
        return {
            "success": True,
            "message": "Billing cycle processed",
            "data": result
        }
    except Exception as e:
        logger.error(f"Error processing billing cycle: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to process billing cycle")

@router.get("/admin/stats")
async def get_admin_stats():
    """Get admin statistics (admin only)"""
    try:
        total_users = len(token_manager.users)
        total_transactions = len(token_manager.transactions)
        
        # Calculate tier distribution
        tier_distribution = {}
        for user in token_manager.users.values():
            tier = user.subscription_tier.value
            tier_distribution[tier] = tier_distribution.get(tier, 0) + 1
        
        # Calculate total tokens in circulation
        total_tokens = sum(user.current_tokens for user in token_manager.users.values())
        
        return {
            "success": True,
            "data": {
                "total_users": total_users,
                "total_transactions": total_transactions,
                "total_tokens_in_circulation": total_tokens,
                "tier_distribution": tier_distribution,
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        logger.error(f"Error getting admin stats: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to get admin statistics")
