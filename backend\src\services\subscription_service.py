"""
Subscription Management Service for FinanceGPT Pro
Handles subscription tiers, upgrades, downgrades, and billing
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
from enum import Enum

from ..models.token_models import (
    token_manager,
    SubscriptionTier,
    SUBSCRIPTION_TIERS,
    UserProfile,
    TokenTransaction
)

logger = logging.getLogger(__name__)

class BillingCycle(Enum):
    MONTHLY = "monthly"
    YEARLY = "yearly"

class SubscriptionStatus(Enum):
    ACTIVE = "active"
    CANCELLED = "cancelled"
    EXPIRED = "expired"
    PENDING = "pending"
    SUSPENDED = "suspended"

class SubscriptionService:
    """Service for managing user subscriptions"""
    
    def __init__(self):
        self.subscription_history = {}
        self.billing_events = []
    
    def get_available_plans(self) -> Dict[str, Any]:
        """Get all available subscription plans"""
        plans = {}
        
        for tier, config in SUBSCRIPTION_TIERS.items():
            plans[tier.value] = {
                "name": config.name,
                "description": config.description,
                "monthly_price": config.price_monthly,
                "yearly_price": config.price_monthly * 10,  # 2 months free
                "monthly_tokens": config.monthly_tokens,
                "daily_limit": config.daily_limit,
                "features": config.features,
                "recommended": tier == SubscriptionTier.PROFESSIONAL
            }
        
        return plans
    
    def subscribe_user(self, user_id: str, tier: SubscriptionTier, 
                      billing_cycle: BillingCycle = BillingCycle.MONTHLY) -> Dict[str, Any]:
        """Subscribe user to a new plan"""
        try:
            user = token_manager.get_user(user_id)
            if not user:
                return {"success": False, "error": "User not found"}
            
            config = SUBSCRIPTION_TIERS[tier]
            
            # Calculate subscription period
            if billing_cycle == BillingCycle.MONTHLY:
                end_date = datetime.now() + timedelta(days=30)
                price = config.price_monthly
            else:  # YEARLY
                end_date = datetime.now() + timedelta(days=365)
                price = config.price_monthly * 10  # 2 months free
            
            # Update user subscription
            old_tier = user.subscription_tier
            user.subscription_tier = tier
            user.subscription_start = datetime.now()
            user.subscription_end = end_date
            
            # Add monthly tokens if upgrading
            if tier != old_tier:
                token_bonus = config.monthly_tokens
                user.add_tokens(token_bonus)
                
                # Record token bonus transaction
                transaction = TokenTransaction(
                    user_id=user_id,
                    transaction_type="bonus",
                    token_amount=token_bonus,
                    description=f"Subscription upgrade bonus: {tier.value}",
                    metadata={
                        "old_tier": old_tier.value,
                        "new_tier": tier.value,
                        "billing_cycle": billing_cycle.value
                    }
                )
                token_manager.transactions.append(transaction)
            
            # Record subscription change
            self._record_subscription_event(user_id, "subscription_created", {
                "tier": tier.value,
                "billing_cycle": billing_cycle.value,
                "price": price,
                "start_date": user.subscription_start.isoformat(),
                "end_date": user.subscription_end.isoformat()
            })
            
            logger.info(f"User {user_id} subscribed to {tier.value} plan")
            
            return {
                "success": True,
                "subscription": {
                    "tier": tier.value,
                    "name": config.name,
                    "billing_cycle": billing_cycle.value,
                    "price": price,
                    "start_date": user.subscription_start.isoformat(),
                    "end_date": user.subscription_end.isoformat(),
                    "tokens_added": token_bonus if tier != old_tier else 0
                }
            }
            
        except Exception as e:
            logger.error(f"Subscription error for user {user_id}: {str(e)}")
            return {"success": False, "error": str(e)}
    
    def upgrade_subscription(self, user_id: str, new_tier: SubscriptionTier) -> Dict[str, Any]:
        """Upgrade user to a higher tier"""
        user = token_manager.get_user(user_id)
        if not user:
            return {"success": False, "error": "User not found"}
        
        current_tier = user.subscription_tier
        
        # Check if it's actually an upgrade
        tier_hierarchy = [
            SubscriptionTier.FREE,
            SubscriptionTier.PROFESSIONAL,
            SubscriptionTier.ENTERPRISE,
            SubscriptionTier.UNLIMITED
        ]
        
        current_index = tier_hierarchy.index(current_tier)
        new_index = tier_hierarchy.index(new_tier)
        
        if new_index <= current_index:
            return {"success": False, "error": "Not an upgrade"}
        
        # Calculate prorated pricing
        days_remaining = (user.subscription_end - datetime.now()).days
        proration_factor = max(0, days_remaining / 30)
        
        old_config = SUBSCRIPTION_TIERS[current_tier]
        new_config = SUBSCRIPTION_TIERS[new_tier]
        
        price_difference = new_config.price_monthly - old_config.price_monthly
        prorated_price = price_difference * proration_factor
        
        # Update subscription
        user.subscription_tier = new_tier
        
        # Add token difference
        token_difference = new_config.monthly_tokens - old_config.monthly_tokens
        if token_difference > 0:
            user.add_tokens(int(token_difference * proration_factor))
        
        # Record upgrade
        self._record_subscription_event(user_id, "subscription_upgraded", {
            "old_tier": current_tier.value,
            "new_tier": new_tier.value,
            "prorated_price": prorated_price,
            "tokens_added": int(token_difference * proration_factor)
        })
        
        return {
            "success": True,
            "upgrade": {
                "old_tier": current_tier.value,
                "new_tier": new_tier.value,
                "prorated_price": prorated_price,
                "tokens_added": int(token_difference * proration_factor)
            }
        }
    
    def cancel_subscription(self, user_id: str, immediate: bool = False) -> Dict[str, Any]:
        """Cancel user subscription"""
        user = token_manager.get_user(user_id)
        if not user:
            return {"success": False, "error": "User not found"}
        
        if immediate:
            # Immediate cancellation - downgrade to free tier
            user.subscription_tier = SubscriptionTier.FREE
            user.subscription_end = datetime.now()
        else:
            # Cancel at end of billing period
            self._record_subscription_event(user_id, "subscription_cancelled", {
                "tier": user.subscription_tier.value,
                "cancellation_date": datetime.now().isoformat(),
                "end_date": user.subscription_end.isoformat(),
                "immediate": immediate
            })
        
        return {
            "success": True,
            "cancellation": {
                "immediate": immediate,
                "end_date": user.subscription_end.isoformat(),
                "message": "Subscription cancelled" if immediate else "Subscription will end at the end of billing period"
            }
        }
    
    def renew_subscription(self, user_id: str) -> Dict[str, Any]:
        """Renew user subscription"""
        user = token_manager.get_user(user_id)
        if not user:
            return {"success": False, "error": "User not found"}
        
        config = SUBSCRIPTION_TIERS[user.subscription_tier]
        
        # Extend subscription by 30 days
        user.subscription_end = user.subscription_end + timedelta(days=30)
        
        # Add monthly tokens
        user.add_tokens(config.monthly_tokens)
        
        # Record renewal
        self._record_subscription_event(user_id, "subscription_renewed", {
            "tier": user.subscription_tier.value,
            "new_end_date": user.subscription_end.isoformat(),
            "tokens_added": config.monthly_tokens
        })
        
        return {
            "success": True,
            "renewal": {
                "tier": user.subscription_tier.value,
                "new_end_date": user.subscription_end.isoformat(),
                "tokens_added": config.monthly_tokens
            }
        }
    
    def get_subscription_status(self, user_id: str) -> Dict[str, Any]:
        """Get detailed subscription status"""
        user = token_manager.get_user(user_id)
        if not user:
            return {"error": "User not found"}
        
        config = SUBSCRIPTION_TIERS[user.subscription_tier]
        days_remaining = (user.subscription_end - datetime.now()).days
        
        # Determine status
        if datetime.now() > user.subscription_end:
            status = SubscriptionStatus.EXPIRED
        elif days_remaining <= 7:
            status = SubscriptionStatus.PENDING  # Renewal needed
        else:
            status = SubscriptionStatus.ACTIVE
        
        return {
            "user_id": user_id,
            "tier": user.subscription_tier.value,
            "name": config.name,
            "status": status.value,
            "start_date": user.subscription_start.isoformat(),
            "end_date": user.subscription_end.isoformat(),
            "days_remaining": max(0, days_remaining),
            "monthly_tokens": config.monthly_tokens,
            "daily_limit": config.daily_limit,
            "features": config.features,
            "current_tokens": user.current_tokens,
            "daily_usage": user.daily_usage,
            "renewal_needed": days_remaining <= 7,
            "can_upgrade": user.subscription_tier != SubscriptionTier.UNLIMITED
        }
    
    def get_usage_recommendations(self, user_id: str) -> Dict[str, Any]:
        """Get subscription recommendations based on usage"""
        user = token_manager.get_user(user_id)
        if not user:
            return {"error": "User not found"}
        
        # Get usage analytics
        analytics = token_manager.get_user_analytics(user_id, days=30)
        avg_daily_usage = analytics["average_daily_usage"]
        
        config = SUBSCRIPTION_TIERS[user.subscription_tier]
        
        recommendations = []
        
        # Check if user is hitting limits
        if user.daily_usage > config.daily_limit * 0.8:
            recommendations.append({
                "type": "upgrade",
                "reason": "Approaching daily limits",
                "suggestion": "Consider upgrading for higher daily limits"
            })
        
        # Check if user is underutilizing
        if avg_daily_usage < config.daily_limit * 0.2 and user.subscription_tier != SubscriptionTier.FREE:
            recommendations.append({
                "type": "downgrade",
                "reason": "Low usage detected",
                "suggestion": "Consider downgrading to save costs"
            })
        
        # Check token efficiency
        if user.current_tokens < config.monthly_tokens * 0.1:
            recommendations.append({
                "type": "token_purchase",
                "reason": "Low token balance",
                "suggestion": "Purchase additional tokens or upgrade subscription"
            })
        
        return {
            "user_id": user_id,
            "current_tier": user.subscription_tier.value,
            "usage_stats": {
                "avg_daily_usage": avg_daily_usage,
                "daily_limit": config.daily_limit,
                "utilization_rate": (avg_daily_usage / config.daily_limit) * 100
            },
            "recommendations": recommendations
        }
    
    def _record_subscription_event(self, user_id: str, event_type: str, metadata: Dict):
        """Record subscription event for audit trail"""
        event = {
            "user_id": user_id,
            "event_type": event_type,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata
        }
        
        if user_id not in self.subscription_history:
            self.subscription_history[user_id] = []
        
        self.subscription_history[user_id].append(event)
        self.billing_events.append(event)
    
    def get_subscription_history(self, user_id: str) -> List[Dict]:
        """Get subscription history for a user"""
        return self.subscription_history.get(user_id, [])
    
    def process_billing_cycle(self):
        """Process monthly billing cycle (would be called by a scheduler)"""
        current_time = datetime.now()
        processed_users = []
        
        for user_id, user in token_manager.users.items():
            # Check if subscription needs renewal
            if current_time >= user.subscription_end and user.subscription_tier != SubscriptionTier.FREE:
                # Auto-renew if user has active subscription
                if user.subscription_tier in [SubscriptionTier.PROFESSIONAL, SubscriptionTier.ENTERPRISE, SubscriptionTier.UNLIMITED]:
                    renewal_result = self.renew_subscription(user_id)
                    if renewal_result["success"]:
                        processed_users.append({
                            "user_id": user_id,
                            "action": "renewed",
                            "tier": user.subscription_tier.value
                        })
                else:
                    # Downgrade to free tier
                    user.subscription_tier = SubscriptionTier.FREE
                    user.subscription_end = current_time + timedelta(days=365)  # Free tier doesn't expire
                    processed_users.append({
                        "user_id": user_id,
                        "action": "downgraded_to_free",
                        "reason": "subscription_expired"
                    })
        
        return {
            "processed_count": len(processed_users),
            "processed_users": processed_users,
            "timestamp": current_time.isoformat()
        }

# Global subscription service instance
subscription_service = SubscriptionService()
