from langchain.tools import tool
from pydantic import BaseModel
import requests
import logging
from functools import lru_cache
from typing import Dict, Any, Optional
from .configuration import get_config
from ..agent.technical_indicators import TechnicalAnalyzer
from ..agent.sentiment_analyzer import SentimentAnalyzer
from ..agent.financial_analyzer import FinancialAnalyzer
from ..utils.error_handler import handle_errors, error_handler, validate_ticker, sanitize_user_input, api_circuit_breakers
from ..services.educational_service import educational_service, EducationLevel
from ..services.advanced_analytics_service import advanced_analytics_service

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SearchQuery(BaseModel):
    query: str

@tool
def google_search(query: str) -> dict:
    """Search the web using Google Custom Search API."""
    # Sanitize user input
    query = sanitize_user_input(query, max_length=500)
    if not query:
        return {"error": "Invalid or empty search query"}

    # Check API configuration
    api_key = get_config().api_keys.google_search_key
    cse_id = get_config().api_keys.google_search_cse_id
    if not api_key or not cse_id:
        logger.error("Google Search API key or CSE ID not configured")
        return {"error": "Google Search API not configured. Check environment variables."}

    # Fallback data for search failures
    fallback_data = {
        "items": [],
        "searchInformation": {"totalResults": "0"},
        "error": "Search service temporarily unavailable"
    }

    try:
        def perform_search():
            url = f"https://www.googleapis.com/customsearch/v1?key={api_key}&cx={cse_id}&q={query}"
            response = requests.get(url, timeout=15)
            response.raise_for_status()
            return response.json()

        # Use circuit breaker for API calls
        result = api_circuit_breakers["google_search"].call(perform_search)
        return result

    except Exception as e:
        # Use centralized error handling
        error_result = error_handler.handle_error(
            e,
            f"google_search({query[:50]}...)",
            fallback_data
        )

        # Return fallback data with error information
        fallback_data["error"] = error_result["error"]["message"]
        fallback_data["error_type"] = error_result["error"]["error_type"]
        return fallback_data

@lru_cache(maxsize=128)
@tool
def get_stock_data(ticker: str) -> Dict[str, Any]:
    """Fetch comprehensive real-time stock data and basic metrics."""
    # Validate ticker input
    if not validate_ticker(ticker):
        return {"error": f"Invalid ticker symbol: {ticker}"}

    ticker = ticker.upper().strip()

    # Default fallback data
    fallback_data = {
        "ticker": ticker,
        "current_price": None,
        "volume": None,
        "market_cap": "N/A",
        "pe_ratio": "N/A",
        "52_week_high": "N/A",
        "52_week_low": "N/A",
        "company_name": ticker,
        "sector": "Unknown",
        "data_source": "fallback"
    }

    try:
        # Use circuit breaker for API calls
        def fetch_data():
            financial_analyzer = FinancialAnalyzer(ticker)
            fundamental_data = financial_analyzer.get_fundamental_data()

            # Get current price from technical analyzer
            technical_analyzer = TechnicalAnalyzer(ticker)
            current_price = "N/A"
            volume = "N/A"

            try:
                if technical_analyzer.fetch_stock_data() is not None:
                    current_price = technical_analyzer.data['close'].iloc[-1]
                    volume = technical_analyzer.data['volume'].iloc[-1]
            except (KeyError, IndexError, AttributeError) as e:
                logger.warning(f"Could not extract price/volume for {ticker}: {str(e)}")

            return {
                "ticker": ticker,
                "current_price": float(current_price) if current_price != "N/A" else None,
                "volume": float(volume) if volume != "N/A" else None,
                "market_cap": fundamental_data.get("market_cap"),
                "pe_ratio": fundamental_data.get("pe_ratio"),
                "52_week_high": fundamental_data.get("52_week_high"),
                "52_week_low": fundamental_data.get("52_week_low"),
                "company_name": fundamental_data.get("company_name", ticker),
                "sector": fundamental_data.get("sector", "Unknown"),
                "data_source": "live"
            }

        # Try to get data with circuit breaker protection
        result = api_circuit_breakers["alpha_vantage"].call(fetch_data)
        return result

    except Exception as e:
        # Use centralized error handling
        error_result = error_handler.handle_error(
            e,
            f"get_stock_data({ticker})",
            fallback_data
        )

        # Return fallback data with error information
        fallback_data["error"] = error_result["error"]["message"]
        fallback_data["error_type"] = error_result["error"]["error_type"]
        return fallback_data

@tool
def analyze_sentiment(ticker: str) -> str:
    """Analyze comprehensive market sentiment for a stock using news and social media."""
    # Validate ticker input
    if not validate_ticker(ticker):
        return f"❌ Invalid ticker symbol: {ticker}"

    ticker = ticker.upper().strip()

    # Fallback response for errors
    fallback_response = f"""📊 **Sentiment Analysis for {ticker}**

**Overall Sentiment**: Neutral (Score: 0.0)
Analysis temporarily unavailable due to service limitations.

**News Sentiment**: N/A
**Social Media Sentiment**: N/A
**Risk Assessment**: Medium risk level

**Disclaimer**: Sentiment analysis is based on limited data and should not be considered as financial advice."""

    try:
        def perform_sentiment_analysis():
            sentiment_analyzer = SentimentAnalyzer(ticker)
            comprehensive_sentiment = sentiment_analyzer.get_comprehensive_sentiment()

            if "error" in comprehensive_sentiment:
                raise Exception(comprehensive_sentiment['error'])

            # Format the response for user-friendly display
            overall_score = comprehensive_sentiment.get("overall_sentiment_score", 0.0)
            category = comprehensive_sentiment.get("sentiment_category", "Neutral")
            interpretation = comprehensive_sentiment.get("interpretation", "")

            news_data = comprehensive_sentiment.get("news_sentiment", {})
            social_data = comprehensive_sentiment.get("social_sentiment", {})
            risk_data = comprehensive_sentiment.get("risk_assessment", {})

            response = f"""📊 **Sentiment Analysis for {ticker}**

**Overall Sentiment**: {category} (Score: {overall_score})
{interpretation}

**News Sentiment**: Score {news_data.get('sentiment_score', 'N/A')} from {news_data.get('article_count', 0)} articles
**Social Media Sentiment**: Score {social_data.get('sentiment_score', 'N/A')} from {social_data.get('post_count', 0)} posts

**Risk Assessment**: {risk_data.get('risk_level', 'Unknown')} risk level
"""

            if news_data.get('positive_factors'):
                response += f"**Positive Factors**: {', '.join(news_data['positive_factors'][:3])}\n"

            if news_data.get('negative_factors'):
                response += f"**Negative Factors**: {', '.join(news_data['negative_factors'][:3])}\n"

            response += "\n**Disclaimer**: Sentiment analysis is based on limited data and should not be considered as financial advice."

            return response

        # Use circuit breaker for Gemini API calls
        result = api_circuit_breakers["gemini"].call(perform_sentiment_analysis)
        return result

    except Exception as e:
        # Use centralized error handling
        error_result = error_handler.handle_error(
            e,
            f"analyze_sentiment({ticker})",
            fallback_response
        )

        # Return fallback response with error note
        if "rate limit" in str(e).lower() or "quota" in str(e).lower():
            return fallback_response + f"\n\n⚠️ Note: {error_result['error']['message']}"
        else:
            return fallback_response

@lru_cache(maxsize=128)
@tool
def get_technical_analysis(ticker: str) -> str:
    """Provide comprehensive technical analysis with multiple indicators."""
    try:
        technical_analyzer = TechnicalAnalyzer(ticker)
        analysis = technical_analyzer.get_comprehensive_analysis()

        if "error" in analysis:
            return f"Technical analysis failed: {analysis['error']}"

        # Format the response for user-friendly display
        rsi = analysis.get('rsi')
        macd_data = analysis.get('macd', {})
        ma_data = analysis.get('moving_averages', {})
        bb_data = analysis.get('bollinger_bands', {})
        interpretations = analysis.get('interpretation', {})

        response = f"""📈 **Technical Analysis for {ticker}**

**Current Price**: ${ma_data.get('current_price', 'N/A')}

**RSI (14-day)**: {rsi if rsi else 'N/A'}
{interpretations.get('rsi', '')}

**MACD**: {macd_data.get('macd', 'N/A')} | Signal: {macd_data.get('signal', 'N/A')}
{interpretations.get('macd', '')}

**Moving Averages**:
- 20-day SMA: ${ma_data.get('sma_20', 'N/A')}
- 50-day SMA: ${ma_data.get('sma_50', 'N/A')}
- 200-day SMA: ${ma_data.get('sma_200', 'N/A')}
{interpretations.get('trend', '')}

**Bollinger Bands**:
- Upper: ${bb_data.get('upper_band', 'N/A')}
- Middle: ${bb_data.get('middle_band', 'N/A')}
- Lower: ${bb_data.get('lower_band', 'N/A')}
{interpretations.get('bollinger', '')}

**Disclaimer**: Technical analysis is based on historical data and should not be considered as trading advice. Always consult with a qualified financial advisor.
"""

        return response

    except Exception as e:
        logger.error(f"Error in technical analysis for {ticker}: {str(e)}")
        return f"Technical analysis failed: {str(e)}"

@tool
def get_fundamental_analysis(ticker: str) -> str:
    """Provide comprehensive fundamental analysis including financial health and valuation."""
    try:
        financial_analyzer = FinancialAnalyzer(ticker)
        health_analysis = financial_analyzer.analyze_financial_health()
        valuation_analysis = financial_analyzer.get_valuation_analysis()

        if "error" in health_analysis:
            return f"Fundamental analysis failed: {health_analysis['error']}"

        fundamental_data = health_analysis.get('fundamental_data', {})

        response = f"""💼 **Fundamental Analysis for {ticker}**

**Company**: {fundamental_data.get('company_name', 'N/A')}
**Sector**: {fundamental_data.get('sector', 'N/A')} | **Industry**: {fundamental_data.get('industry', 'N/A')}

**Financial Health**: {health_analysis.get('health_rating', 'N/A')} (Score: {health_analysis.get('health_score', 0)}/100)

**Key Metrics**:
- Market Cap: ${fundamental_data.get('market_cap', 'N/A'):,} if fundamental_data.get('market_cap') else 'N/A'
- P/E Ratio: {fundamental_data.get('pe_ratio', 'N/A')}
- PEG Ratio: {fundamental_data.get('peg_ratio', 'N/A')}
- Price-to-Book: {fundamental_data.get('price_to_book', 'N/A')}
- Dividend Yield: {fundamental_data.get('dividend_yield', 'N/A')}%

**Profitability**:
- Profit Margin: {fundamental_data.get('profit_margin', 'N/A')}%
- Operating Margin: {fundamental_data.get('operating_margin', 'N/A')}%
- ROE: {fundamental_data.get('return_on_equity', 'N/A')}%
- ROA: {fundamental_data.get('return_on_assets', 'N/A')}%

**Financial Position**:
- Debt-to-Equity: {fundamental_data.get('debt_to_equity', 'N/A')}
- Current Ratio: {fundamental_data.get('current_ratio', 'N/A')}
- Beta: {fundamental_data.get('beta', 'N/A')}

**Valuation Assessment**: {valuation_analysis.get('overall_valuation', 'N/A')}

**Strengths**: {', '.join(health_analysis.get('positive_factors', [])[:3])}
**Concerns**: {', '.join(health_analysis.get('warning_factors', [])[:3])}

**Disclaimer**: This analysis is for informational purposes only and should not be considered as financial advice.
"""

        return response

    except Exception as e:
        logger.error(f"Error in fundamental analysis for {ticker}: {str(e)}")
        return f"Fundamental analysis failed: {str(e)}"

@tool
def get_investment_recommendation(ticker: str) -> str:
    """Provide comprehensive investment recommendation based on all analysis factors."""
    try:
        financial_analyzer = FinancialAnalyzer(ticker)
        recommendation_data = financial_analyzer.get_investment_recommendation()

        if "error" in recommendation_data:
            return f"Investment recommendation failed: {recommendation_data['error']}"

        recommendation = recommendation_data.get('recommendation', 'Hold')
        confidence = recommendation_data.get('confidence_score', 0)
        factors = recommendation_data.get('key_factors', [])

        # Get additional context
        technical_analyzer = TechnicalAnalyzer(ticker)
        tech_analysis = technical_analyzer.get_comprehensive_analysis()

        sentiment_analyzer = SentimentAnalyzer(ticker)
        sentiment_data = sentiment_analyzer.get_comprehensive_sentiment()

        response = f"""🎯 **Investment Recommendation for {ticker}**

**Recommendation**: {recommendation}
**Confidence Level**: {confidence}%

**Key Factors**:
{chr(10).join([f"• {factor}" for factor in factors[:5]])}

**Technical Outlook**: {tech_analysis.get('interpretation', {}).get('trend', 'Mixed signals')}
**Market Sentiment**: {sentiment_data.get('sentiment_category', 'Neutral')} ({sentiment_data.get('overall_sentiment_score', 0.0)})

**Risk Assessment**: {sentiment_data.get('risk_assessment', {}).get('risk_level', 'Medium')} risk level

**Summary**: Based on comprehensive analysis combining fundamental health, technical indicators, and market sentiment, {ticker} receives a {recommendation} recommendation with {confidence}% confidence.

**Important Disclaimer**: This recommendation is generated by AI analysis and is for informational purposes only. It should not be considered as personalized financial advice. Always conduct your own research and consult with a qualified financial advisor before making investment decisions. Past performance does not guarantee future results.
"""

        return response

    except Exception as e:
        logger.error(f"Error generating investment recommendation for {ticker}: {str(e)}")
        return f"Investment recommendation failed: {str(e)}"

@tool
def get_educational_content(topic: str = "basics", level: str = "beginner") -> str:
    """Get educational content about investing and finance topics."""
    try:
        # Validate level
        level_map = {
            "beginner": EducationLevel.BEGINNER,
            "intermediate": EducationLevel.INTERMEDIATE,
            "advanced": EducationLevel.ADVANCED
        }

        education_level = level_map.get(level.lower(), EducationLevel.BEGINNER)

        # Search for content
        if topic.lower() in ["basics", "fundamentals", "stocks"]:
            content = educational_service.get_content_by_id("basics_001")
        elif topic.lower() in ["technical", "charts", "indicators"]:
            content = educational_service.get_content_by_id("technical_001")
        elif topic.lower() in ["portfolio", "diversification", "allocation"]:
            content = educational_service.get_content_by_id("portfolio_001")
        elif topic.lower() in ["glossary", "terms", "definitions"]:
            content = educational_service.get_content_by_id("glossary_001")
        else:
            # Search by topic
            search_results = educational_service.search_content(topic)
            content = search_results[0] if search_results else None

        if not content:
            # Get recommended content for level
            recommended = educational_service.get_recommended_content(education_level)
            content = recommended[0] if recommended else None

        if content:
            response = f"""📚 **{content['title']}**

**Level**: {content['level'].title()}
**Duration**: {content['duration_minutes']} minutes
**Category**: {content['category'].title()}

{content['description']}

---

{content['content'][:1500]}...

**Tags**: {', '.join(content['tags'])}

💡 **Tip**: This is educational content only. Always consult with a qualified financial advisor for personalized advice.
"""
            return response
        else:
            return f"📚 Educational content for '{topic}' not found. Try topics like: basics, technical analysis, portfolio management, or glossary."

    except Exception as e:
        logger.error(f"Error getting educational content: {str(e)}")
        return f"Educational content request failed: {str(e)}"

@tool
def get_advanced_analytics(ticker: str, analysis_type: str = "momentum") -> str:
    """Get advanced analytics including correlation, volatility, momentum, and risk metrics."""
    try:
        # Validate ticker
        if not validate_ticker(ticker):
            return f"❌ Invalid ticker symbol: {ticker}"

        ticker = ticker.upper().strip()

        # Get mock price data (in production, use real data)
        import random
        base_price = 150.0
        price_data = []
        for i in range(100):  # 100 days of data
            price = base_price + random.uniform(-20, 20)
            price_data.append(price)
            base_price = price * (1 + random.uniform(-0.02, 0.02))  # Small daily changes

        analysis_type = analysis_type.lower()

        if analysis_type == "momentum":
            analysis = advanced_analytics_service.analyze_momentum(ticker, price_data)
            response = f"""📈 **Momentum Analysis for {ticker}**

**Trend Strength**: {analysis.trend_strength}
**Overall Momentum Score**: {analysis.momentum_score:.4f}

**Timeframe Analysis**:
- Short-term (10 days): {analysis.short_term_momentum:.2%}
- Medium-term (30 days): {analysis.medium_term_momentum:.2%}
- Long-term (60 days): {analysis.long_term_momentum:.2%}

**Interpretation**:
{analysis.trend_strength} indicates the current price momentum direction and strength.
"""

        elif analysis_type == "volatility":
            analysis = advanced_analytics_service.analyze_volatility(ticker, price_data)
            response = f"""📊 **Volatility Analysis for {ticker}**

**Risk Level**: {analysis.risk_level}
**Current Volatility**: {analysis.current_volatility:.2%} (annualized)
**Historical Volatility**: {analysis.historical_volatility:.2%} (annualized)
**Volatility Percentile**: {analysis.volatility_percentile:.1f}%

**Interpretation**:
Current volatility is at the {analysis.volatility_percentile:.0f}th percentile of historical levels.
Risk level is classified as {analysis.risk_level}.
"""

        elif analysis_type == "risk":
            risk_metrics = advanced_analytics_service.calculate_risk_metrics(ticker, price_data)
            response = f"""⚠️ **Risk Metrics for {ticker}**

**Volatility**: {risk_metrics['volatility']:.2%} (annualized)
**Value at Risk (95%)**: {risk_metrics['value_at_risk_95']:.2%}
**Maximum Drawdown**: {risk_metrics['max_drawdown']:.2%}
**Sharpe Ratio**: {risk_metrics['sharpe_ratio']:.3f}
**Sortino Ratio**: {risk_metrics['sortino_ratio']:.3f}

**Interpretation**:
These metrics help assess the risk-return profile of the investment.
Higher Sharpe and Sortino ratios indicate better risk-adjusted returns.
"""

        else:
            return f"❌ Unknown analysis type: {analysis_type}. Available types: momentum, volatility, risk"

        response += "\n\n**Disclaimer**: Advanced analytics are for informational purposes only and should not be considered as investment advice."
        return response

    except Exception as e:
        logger.error(f"Error in advanced analytics: {str(e)}")
        return f"Advanced analytics failed: {str(e)}"