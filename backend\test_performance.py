#!/usr/bin/env python3
"""
Performance Optimization Test Suite
Tests the performance improvements and optimizations
"""

import sys
import os
import time
import asyncio
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.performance_optimizer import (
    PerformanceCache, RequestBatcher, PerformanceMonitor,
    cached, timed, PerformanceProfiler, get_performance_summary,
    performance_cache, performance_monitor
)
from agent.technical_indicators import TechnicalAnalyzer
from agent.tools_and_schemas import get_stock_data, analyze_sentiment

def test_caching_system():
    """Test the caching system performance"""
    print("🧪 Testing Caching System")
    print("=" * 50)
    
    cache = PerformanceCache(default_ttl=60)
    
    # Test cache operations
    start_time = time.time()
    
    # Set some test data
    for i in range(1000):
        cache.set(f"key_{i}", f"value_{i}")
    
    set_time = time.time() - start_time
    
    # Get data (should be fast)
    start_time = time.time()
    hits = 0
    for i in range(1000):
        if cache.get(f"key_{i}") is not None:
            hits += 1
    
    get_time = time.time() - start_time
    
    print(f"✅ Cache SET operations: {set_time:.4f}s for 1000 items")
    print(f"✅ Cache GET operations: {get_time:.4f}s for 1000 items")
    print(f"✅ Cache hit rate: {hits}/1000 ({hits/10:.1f}%)")
    
    # Test cache stats
    stats = cache.get_stats()
    print(f"✅ Cache stats: {stats['total_keys']} keys, {stats['total_accesses']} accesses")
    
    return set_time < 0.1 and get_time < 0.05 and hits == 1000

def test_performance_monitoring():
    """Test performance monitoring system"""
    print("\n🧪 Testing Performance Monitoring")
    print("=" * 50)
    
    monitor = PerformanceMonitor()
    
    # Simulate some function calls
    for i in range(10):
        duration = 0.1 + (i * 0.01)  # Increasing duration
        success = i < 8  # 2 failures
        monitor.record_call("test_function", duration, success)
    
    # Get performance report
    report = monitor.get_performance_report()
    
    print(f"✅ Total functions monitored: {report['summary']['total_functions']}")
    print(f"✅ Total calls recorded: {report['summary']['total_calls']}")
    print(f"✅ Total errors recorded: {report['summary']['total_errors']}")
    
    # Check function stats
    func_stats = report['function_stats']['test_function']
    print(f"✅ Average call time: {func_stats['avg_time']:.4f}s")
    print(f"✅ Min/Max time: {func_stats['min_time']:.4f}s / {func_stats['max_time']:.4f}s")
    
    return (report['summary']['total_calls'] == 10 and 
            report['summary']['total_errors'] == 2 and
            func_stats['call_count'] == 10)

def test_cached_decorator():
    """Test the cached decorator performance"""
    print("\n🧪 Testing Cached Decorator")
    print("=" * 50)
    
    call_count = 0
    
    @cached(ttl=60)
    def expensive_function(x):
        nonlocal call_count
        call_count += 1
        time.sleep(0.1)  # Simulate expensive operation
        return x * 2
    
    # First call should be slow
    start_time = time.time()
    result1 = expensive_function(5)
    first_call_time = time.time() - start_time
    
    # Second call should be fast (cached)
    start_time = time.time()
    result2 = expensive_function(5)
    second_call_time = time.time() - start_time
    
    print(f"✅ First call time: {first_call_time:.4f}s")
    print(f"✅ Second call time: {second_call_time:.4f}s")
    print(f"✅ Speedup: {first_call_time/second_call_time:.1f}x")
    print(f"✅ Function called {call_count} times (should be 1)")
    
    return (result1 == result2 == 10 and 
            call_count == 1 and 
            second_call_time < first_call_time / 10)

def test_profiler_context():
    """Test the performance profiler context manager"""
    print("\n🧪 Testing Performance Profiler")
    print("=" * 50)
    
    # Test profiler
    with PerformanceProfiler("test_operation"):
        time.sleep(0.1)  # Simulate work
    
    # Check if it was recorded
    report = performance_monitor.get_performance_report()
    
    if "test_operation" in report['function_stats']:
        stats = report['function_stats']['test_operation']
        print(f"✅ Profiler recorded operation: {stats['call_count']} calls")
        print(f"✅ Average duration: {stats['avg_time']:.4f}s")
        return stats['call_count'] >= 1 and stats['avg_time'] >= 0.1
    else:
        print("❌ Profiler failed to record operation")
        return False

def test_technical_analyzer_performance():
    """Test technical analyzer performance optimizations"""
    print("\n🧪 Testing Technical Analyzer Performance")
    print("=" * 50)
    
    tickers = ["AAPL", "MSFT", "GOOGL"]
    
    # Test multiple calls to see caching effect
    total_time = 0
    for i in range(2):  # Run twice to test caching
        start_time = time.time()
        
        for ticker in tickers:
            analyzer = TechnicalAnalyzer(ticker)
            analysis = analyzer.get_comprehensive_analysis()
            
            if "error" not in analysis:
                print(f"✅ Analysis completed for {ticker}")
            else:
                print(f"⚠️ Analysis failed for {ticker}: {analysis['error']}")
        
        iteration_time = time.time() - start_time
        total_time += iteration_time
        print(f"✅ Iteration {i+1} time: {iteration_time:.2f}s")
    
    avg_time = total_time / 2
    print(f"✅ Average iteration time: {avg_time:.2f}s")
    
    return avg_time < 10.0  # Should complete within 10 seconds

def test_concurrent_requests():
    """Test concurrent request handling"""
    print("\n🧪 Testing Concurrent Request Performance")
    print("=" * 50)
    
    tickers = ["AAPL", "MSFT", "GOOGL", "TSLA", "AMZN"]
    
    def get_stock_analysis(ticker):
        """Get stock analysis for a ticker"""
        try:
            data = get_stock_data(ticker)
            return f"{ticker}: {'✅' if 'error' not in data else '❌'}"
        except Exception as e:
            return f"{ticker}: ❌ {str(e)}"
    
    # Test sequential execution
    start_time = time.time()
    sequential_results = []
    for ticker in tickers:
        result = get_stock_analysis(ticker)
        sequential_results.append(result)
    sequential_time = time.time() - start_time
    
    # Test concurrent execution
    start_time = time.time()
    with ThreadPoolExecutor(max_workers=3) as executor:
        futures = [executor.submit(get_stock_analysis, ticker) for ticker in tickers]
        concurrent_results = [future.result() for future in as_completed(futures)]
    concurrent_time = time.time() - start_time
    
    print(f"✅ Sequential execution: {sequential_time:.2f}s")
    print(f"✅ Concurrent execution: {concurrent_time:.2f}s")
    
    if sequential_time > 0:
        speedup = sequential_time / concurrent_time
        print(f"✅ Speedup: {speedup:.1f}x")
        return speedup > 1.2  # At least 20% improvement
    
    return True

def main():
    """Run comprehensive performance tests"""
    print("🚀 PERFORMANCE OPTIMIZATION TEST SUITE")
    print("=" * 80)
    print("Testing performance improvements and optimizations")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_functions = [
        test_caching_system,
        test_performance_monitoring,
        test_cached_decorator,
        test_profiler_context,
        test_technical_analyzer_performance,
        test_concurrent_requests,
    ]
    
    results = []
    for test_func in test_functions:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {str(e)}")
            results.append(False)
    
    # Get performance summary
    print("\n" + "=" * 80)
    print("📊 PERFORMANCE SUMMARY")
    print("=" * 80)
    
    summary = get_performance_summary()
    cache_stats = summary['cache_stats']
    perf_report = summary['performance_report']
    
    print(f"🗄️ Cache Statistics:")
    print(f"   Total keys: {cache_stats['total_keys']}")
    print(f"   Total accesses: {cache_stats['total_accesses']}")
    
    print(f"\n⏱️ Performance Statistics:")
    print(f"   Functions monitored: {perf_report['summary']['total_functions']}")
    print(f"   Total calls: {perf_report['summary']['total_calls']}")
    print(f"   Total errors: {perf_report['summary']['total_errors']}")
    
    if perf_report['slowest_functions']:
        print(f"\n🐌 Slowest Functions:")
        for func_name, avg_time in perf_report['slowest_functions'][:3]:
            print(f"   {func_name}: {avg_time:.4f}s avg")
    
    # Test results summary
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    print(f"\n📈 TEST RESULTS:")
    print(f"✅ Passed: {passed}/{total} test suites")
    print(f"📊 Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 EXCELLENT! Performance optimizations are working well!")
    elif success_rate >= 60:
        print("👍 GOOD! Performance optimizations show improvement.")
    else:
        print("⚠️ NEEDS WORK! Performance optimizations need attention.")
    
    print("=" * 80)
    return success_rate >= 80

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
