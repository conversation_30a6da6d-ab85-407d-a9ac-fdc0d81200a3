# FinanceGPT Widget Integration Guide

This guide explains how to integrate the FinanceGPT widget into your web application using various methods.

## Table of Contents

1. [Integration Methods](#integration-methods)
2. [Configuration Options](#configuration-options)
3. [Implementation Examples](#implementation-examples)
4. [API Reference](#api-reference)
5. [Styling & Theming](#styling--theming)
6. [Security Considerations](#security-considerations)

## Integration Methods

### 1. Direct React Component Integration

Best for React applications where you want full control and seamless integration.

```tsx
import { FinanceGPTWidget } from './path/to/FinanceGPTWidget';

function MyApp() {
  return (
    <div>
      <h1>My Financial Dashboard</h1>
      <FinanceGPTWidget
        config={{
          mode: 'chat',
          height: '500px',
          user: {
            id: 'user123',
            name: '<PERSON>',
            tier: 'pro'
          },
          onUserAction: (action, data) => {
            console.log('User action:', action, data);
          }
        }}
      />
    </div>
  );
}
```

### 2. Script Tag Integration

Perfect for any website - no build process required.

```html
<!DOCTYPE html>
<html>
<head>
  <title>My Website</title>
</head>
<body>
  <div id="finance-widget"></div>
  
  <script src="https://your-domain.com/finance-gpt-widget.js"></script>
  <script>
    // Initialize chat-only widget
    const widget = FinanceGPT.presets.chatOnly('finance-widget');
    
    // Or custom configuration
    const customWidget = FinanceGPT.init({
      containerId: 'finance-widget',
      config: {
        mode: 'full',
        height: '600px',
        features: {
          chat: true,
          portfolio: true,
          analytics: false
        }
      }
    });
  </script>
</body>
</html>
```

### 3. Iframe Integration

Ideal for maximum isolation and security.

```html
<div id="finance-widget-container"></div>

<script>
  const iframeWidget = FinanceGPT.initIframe({
    containerId: 'finance-widget-container',
    widgetUrl: 'https://your-domain.com/widget.html',
    config: {
      mode: 'portfolio',
      height: '700px',
      user: {
        id: 'user123',
        tier: 'pro'
      }
    },
    onMessage: (data) => {
      console.log('Widget message:', data);
    }
  });
</script>
```

## Configuration Options

### Core Configuration

```typescript
interface FinanceGPTWidgetConfig {
  // Widget mode
  mode: 'chat' | 'portfolio' | 'analytics' | 'full';
  
  // Theme
  theme?: 'dark' | 'light' | 'auto';
  
  // API settings
  apiEndpoint?: string;
  apiKey?: string;
  
  // Feature toggles
  features?: {
    chat?: boolean;
    portfolio?: boolean;
    analytics?: boolean;
    planSwitching?: boolean;
  };
  
  // Styling
  styling?: {
    primaryColor?: string;
    backgroundColor?: string;
    borderRadius?: string;
    fontFamily?: string;
    compact?: boolean;
  };
  
  // User data
  user?: {
    id?: string;
    name?: string;
    tier?: 'free' | 'pro';
    tokens?: number;
  };
  
  // Dimensions
  height?: string;
  width?: string;
  maxHeight?: string;
  maxWidth?: string;
  
  // Event callbacks
  onUserAction?: (action: string, data: any) => void;
  onPlanChange?: (newPlan: string) => void;
  onTokenUsage?: (tokensUsed: number) => void;
}
```

### Widget Modes

- **`chat`**: AI chatbot interface only
- **`portfolio`**: Portfolio tracking and management
- **`analytics`**: Advanced analytics tools
- **`full`**: Complete widget with all features and tabs

## Implementation Examples

### Example 1: E-commerce Integration

```javascript
// Add financial analysis to product pages
const productWidget = FinanceGPT.init({
  containerId: 'stock-analysis',
  config: {
    mode: 'chat',
    height: '400px',
    styling: {
      primaryColor: '#your-brand-color',
      compact: true
    },
    features: {
      chat: true,
      portfolio: false,
      analytics: false,
      planSwitching: false
    },
    onUserAction: (action, data) => {
      // Track user interactions
      analytics.track('finance_widget_action', {
        action,
        data,
        page: 'product'
      });
    }
  }
});
```

### Example 2: Financial Dashboard

```javascript
// Full-featured widget for financial platforms
const dashboardWidget = FinanceGPT.init({
  containerId: 'main-widget',
  config: {
    mode: 'full',
    height: '800px',
    user: {
      id: getCurrentUserId(),
      name: getCurrentUserName(),
      tier: getUserTier(),
      tokens: getUserTokens()
    },
    apiEndpoint: 'https://your-api.com',
    apiKey: 'your-api-key',
    onPlanChange: (newPlan) => {
      // Handle plan changes in your system
      updateUserPlan(newPlan);
    },
    onTokenUsage: (tokensUsed) => {
      // Update user's token balance
      updateUserTokens(tokensUsed);
    }
  }
});
```

### Example 3: Mobile App Integration

```javascript
// Responsive widget for mobile
const mobileWidget = FinanceGPT.init({
  containerId: 'mobile-widget',
  config: {
    mode: 'chat',
    height: '100vh',
    width: '100%',
    styling: {
      compact: true,
      borderRadius: '0px'
    },
    features: {
      chat: true,
      portfolio: false,
      analytics: false
    }
  }
});
```

## API Reference

### Global Methods

```javascript
// Initialize widget with custom config
FinanceGPT.init(options)

// Initialize iframe widget
FinanceGPT.initIframe(options)

// Preset configurations
FinanceGPT.presets.chatOnly(containerId)
FinanceGPT.presets.portfolioOnly(containerId)
FinanceGPT.presets.analyticsOnly(containerId)
FinanceGPT.presets.fullWidget(containerId)
```

### Widget Instance Methods

```javascript
// Update configuration
widget.updateConfig(newConfig)

// Destroy widget
widget.destroy()

// Get instance by container ID
FinanceGPT.getInstance(containerId)
```

### Event Callbacks

```javascript
config: {
  // User performed an action
  onUserAction: (action, data) => {
    // Actions: 'analysis_request', 'plan_view', 'token_usage', etc.
  },
  
  // User changed subscription plan
  onPlanChange: (newPlan) => {
    // newPlan: 'free', 'trial', 'pro'
  },
  
  // Tokens were consumed
  onTokenUsage: (tokensUsed) => {
    // Update user's token balance
  }
}
```

## Styling & Theming

### Custom Colors

```javascript
config: {
  styling: {
    primaryColor: '#your-brand-color',
    backgroundColor: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    borderRadius: '8px',
    fontFamily: 'Your Custom Font, sans-serif'
  }
}
```

### CSS Custom Properties

```css
.finance-gpt-widget {
  --primary-color: #3b82f6;
  --background-color: #1a1a2e;
  --text-color: #ffffff;
  --border-radius: 12px;
}
```

### Responsive Design

```javascript
config: {
  height: 'clamp(400px, 50vh, 800px)',
  width: '100%',
  maxWidth: '1200px',
  styling: {
    compact: window.innerWidth < 768
  }
}
```

## Security Considerations

### Content Security Policy

```html
<meta http-equiv="Content-Security-Policy" 
      content="frame-src https://your-widget-domain.com; 
               script-src 'self' https://your-widget-domain.com;">
```

### API Key Management

```javascript
// Never expose API keys in client-side code
config: {
  apiEndpoint: 'https://your-backend.com/api/finance-proxy',
  // API key should be handled by your backend
}
```

### Origin Validation

```javascript
// For iframe integration
const iframeWidget = FinanceGPT.initIframe({
  widgetUrl: 'https://trusted-domain.com/widget.html',
  config: { /* ... */ },
  onMessage: (data) => {
    // Validate message origin
    if (event.origin !== 'https://trusted-domain.com') {
      return;
    }
    // Handle message
  }
});
```

## Build and Deployment

### Building Widget Bundle

```bash
# Build standalone widget
npm run build:widget

# Files generated:
# - dist-widget/widget.html (iframe version)
# - dist-widget/finance-gpt-widget.js (script tag version)
```

### CDN Deployment

```html
<!-- Production CDN -->
<script src="https://cdn.your-domain.com/finance-gpt-widget.js"></script>

<!-- Development -->
<script src="https://dev-cdn.your-domain.com/finance-gpt-widget.js"></script>
```

## Advanced Features

### Auto-Resize for Iframes

```javascript
// Widget automatically resizes iframe based on content
const widget = FinanceGPT.initIframe({
  containerId: 'widget-container',
  widgetUrl: 'https://your-domain.com/widget.html',
  config: {
    mode: 'chat',
    height: 'auto' // Enables auto-resize
  }
});
```

### Custom Event Handling

```javascript
// Listen for custom widget events
window.addEventListener('finance-widget-ready', (event) => {
  console.log('Widget is ready!', event.detail);
});

window.addEventListener('finance-widget-error', (event) => {
  console.error('Widget error:', event.detail);
});
```

### Dynamic Configuration Updates

```javascript
// Update widget configuration after initialization
widget.updateConfig({
  user: {
    tier: 'pro',
    tokens: 10000
  },
  features: {
    analytics: true
  }
});
```

## Troubleshooting

### Common Issues

1. **Widget not loading**: Check console for errors and verify script URL
2. **Styling conflicts**: Use iframe integration for complete isolation
3. **API errors**: Verify API endpoint and authentication
4. **Mobile issues**: Enable compact mode for better mobile experience

### Debug Mode

```javascript
config: {
  debug: true, // Enables console logging
  mode: 'chat'
}
```

## Support

For integration support, please:

1. Check the [examples](./examples/) directory
2. Review the [API documentation](./API.md)
3. Contact <NAME_EMAIL>

## License

This widget is licensed under [Your License]. See LICENSE file for details.
