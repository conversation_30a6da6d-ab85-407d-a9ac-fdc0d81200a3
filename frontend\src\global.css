@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";

:root {
  /* 🔥 SCRUMPTIOUS DESIGN SYSTEM 🔥 */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-success: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --gradient-warning: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --gradient-danger: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #3498db 100%);
  --gradient-hot: linear-gradient(135deg, #ff6b6b 0%, #feca57 50%, #48dbfb 100%);

  /* 🎨 Glass Morphism */
  --glass-bg: rgba(255, 255, 255, 0.15);
  --glass-border: rgba(255, 255, 255, 0.25);
  --glass-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);

  /* ✨ Neon Glows */
  --neon-blue: 0 0 20px rgba(59, 130, 246, 0.6);
  --neon-purple: 0 0 20px rgba(147, 51, 234, 0.6);
  --neon-green: 0 0 20px rgba(34, 197, 94, 0.6);
  --neon-pink: 0 0 20px rgba(236, 72, 153, 0.6);
  --neon-orange: 0 0 20px rgba(251, 146, 60, 0.6);
  --radius: 0.625rem;
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    border-color: var(--color-border);
    outline-color: color-mix(in oklab, var(--color-ring) 50%, transparent);
  }
  body {
    background-color: var(--color-background);
    color: var(--color-foreground);
  }
}

.animation-delay-200 { animation-delay: 0.2s; }
.animation-delay-400 { animation-delay: 0.4s; }
.animation-delay-600 { animation-delay: 0.6s; }
.animation-delay-800 { animation-delay: 0.8s; }

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}
@keyframes fadeInUp {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}
@keyframes fadeInUpSmooth {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out forwards;
}
.animate-fadeInUp {
  animation: fadeInUp 0.5s ease-out forwards;
}
.animate-fadeInUpSmooth {
  animation: fadeInUpSmooth 0.3s ease-out forwards;
}

/* Professional FinanceGPT Pro Enhancements */
.finance-gradient {
  background: linear-gradient(135deg, #1e3a8a 0%, #1e40af 25%, #3730a3 50%, #4338ca 75%, #6366f1 100%);
}

.token-glow {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.professional-card {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.analysis-loading {
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.ticker-highlight {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
}

.enterprise-shadow {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.professional-input:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  border-color: #3b82f6;
}

/* Responsive Design Enhancements */
@media (max-width: 768px) {
  .mobile-stack {
    flex-direction: column;
    gap: 1rem;
  }

  .mobile-full {
    width: 100%;
  }

  .mobile-text-center {
    text-align: center;
  }
}

/* Hide scrollbars for horizontal scroll */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}
.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}

/* Touch-friendly interactions */
.touch-manipulation {
  touch-action: manipulation;
}

/* Enhanced Responsive Utilities */
@media (max-width: 640px) {
  /* Mobile-first responsive text sizes */
  .responsive-text-xs { font-size: 0.75rem; }
  .responsive-text-sm { font-size: 0.875rem; }
  .responsive-text-base { font-size: 1rem; }
  .responsive-text-lg { font-size: 1.125rem; }
  .responsive-text-xl { font-size: 1.25rem; }
  .responsive-text-2xl { font-size: 1.5rem; }
  .responsive-text-3xl { font-size: 1.875rem; }

  /* Mobile spacing adjustments */
  .mobile-p-2 { padding: 0.5rem; }
  .mobile-p-3 { padding: 0.75rem; }
  .mobile-p-4 { padding: 1rem; }
  .mobile-px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
  .mobile-px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
  .mobile-py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
  .mobile-py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }

  /* Mobile margins */
  .mobile-m-2 { margin: 0.5rem; }
  .mobile-m-3 { margin: 0.75rem; }
  .mobile-mx-2 { margin-left: 0.5rem; margin-right: 0.5rem; }
  .mobile-my-2 { margin-top: 0.5rem; margin-bottom: 0.5rem; }

  /* Mobile flex utilities */
  .mobile-flex-col { flex-direction: column; }
  .mobile-flex-row { flex-direction: row; }
  .mobile-items-start { align-items: flex-start; }
  .mobile-items-center { align-items: center; }
  .mobile-justify-start { justify-content: flex-start; }
  .mobile-justify-center { justify-content: center; }
  .mobile-justify-between { justify-content: space-between; }

  /* Mobile grid utilities */
  .mobile-grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
  .mobile-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }

  /* Mobile width utilities */
  .mobile-w-full { width: 100%; }
  .mobile-w-auto { width: auto; }

  /* Mobile display utilities */
  .mobile-hidden { display: none; }
  .mobile-block { display: block; }
  .mobile-inline-block { display: inline-block; }
  .mobile-flex { display: flex; }
  .mobile-grid { display: grid; }
}

/* Tablet responsive utilities */
@media (min-width: 641px) and (max-width: 1024px) {
  .tablet-grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .tablet-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .tablet-flex-row { flex-direction: row; }
  .tablet-flex-col { flex-direction: column; }
}

/* Desktop responsive utilities */
@media (min-width: 1025px) {
  .desktop-grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .desktop-grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .desktop-flex-row { flex-direction: row; }
}

/* Responsive container utilities */
.responsive-container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: 1rem;
  padding-right: 1rem;
}

@media (min-width: 640px) {
  .responsive-container {
    max-width: 640px;
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
}

@media (min-width: 768px) {
  .responsive-container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .responsive-container {
    max-width: 1024px;
    padding-left: 2rem;
    padding-right: 2rem;
  }
}

@media (min-width: 1280px) {
  .responsive-container {
    max-width: 1280px;
  }
}

/* Responsive card utilities */
.responsive-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 12px;
  padding: 1rem;
  transition: all 0.3s ease;
}

@media (min-width: 640px) {
  .responsive-card {
    border-radius: 16px;
    padding: 1.5rem;
  }
}

/* Responsive button utilities */
.responsive-button {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  border-radius: 8px;
  transition: all 0.2s ease;
  touch-action: manipulation;
  min-height: 44px; /* iOS touch target minimum */
}

@media (min-width: 640px) {
  .responsive-button {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
    border-radius: 12px;
  }
}

/* Professional Typography */
.finance-heading {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 700;
  letter-spacing: -0.025em;
}

.finance-body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
}

@layer components {
  /* Status Indicators */
  .status-excellent { @apply bg-green-100 text-green-800 border-green-200; }
  .status-good { @apply bg-blue-100 text-blue-800 border-blue-200; }
  .status-warning { @apply bg-yellow-100 text-yellow-800 border-yellow-200; }
  .status-critical { @apply bg-red-100 text-red-800 border-red-200; }
}

/* 🔥🔥🔥 ABSOLUTELY SCRUMPTIOUS DESIGN SYSTEM 🔥🔥🔥 */

/* 🎯 Sexy Dark Background */
body {
  background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
  background-attachment: fixed;
  min-height: 100vh;
  overflow-x: hidden;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(139, 92, 246, 0.08) 0%, transparent 50%);
  pointer-events: none;
  z-index: -1;
}

/* 🎨 Clean Glass Cards */
.glass-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  border-radius: 16px;
  transition: all 0.3s ease;
}

.glass-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* 🌟 Clean Buttons */
.btn-gradient-primary {
  background: var(--gradient-primary);
  border: none;
  color: white;
  font-weight: 600;
  border-radius: 12px;
  padding: 12px 24px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(102, 126, 234, 0.2);
}

.btn-gradient-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.btn-gradient-hot {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  color: white;
  font-weight: 600;
  border-radius: 12px;
  padding: 12px 24px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
}

.btn-gradient-hot:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

/* 💎 Subtle Accent Text */
.neon-text-blue {
  color: #60a5fa;
  font-weight: 600;
}

.neon-text-purple {
  color: #a855f7;
  font-weight: 600;
}

.neon-text-green {
  color: #22c55e;
  font-weight: 600;
}

.neon-text-pink {
  color: #ec4899;
  font-weight: 600;
}

.neon-text-orange {
  color: #fb923c;
  font-weight: 600;
}

/* 🔥 Subtle Gradient Text */
.gradient-text {
  background: linear-gradient(135deg, #60a5fa 0%, #a855f7 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

.gradient-text-hot {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-weight: 700;
}

/* ✨ Clean Effects */
.shimmer {
  transition: all 0.3s ease;
}

/* 🌊 Subtle Hover Effects */
.float-animation {
  transition: transform 0.3s ease;
}

.float-animation:hover {
  transform: translateY(-2px);
}

/* 🎯 Subtle Glows */
.pulse-glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.2);
}

.pulse-glow-purple {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.2);
}

.pulse-glow-green {
  box-shadow: 0 0 20px rgba(34, 197, 94, 0.2);
}

/* 🔥 Clean Card Styles */
.hot-card {
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(16px);
  -webkit-backdrop-filter: blur(16px);
  border: 1px solid rgba(255, 255, 255, 0.12);
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  position: relative;
}

.hot-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* 💫 Clean Styling */
.sparkle {
  position: relative;
}

/* 🎨 Custom Scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--gradient-primary);
  border-radius: 10px;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gradient-hot);
}

/* 🌟 Special Effects for Finance Elements */
.price-up {
  color: #22c55e;
  text-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
  animation: price-bounce 0.6s ease-out;
}

.price-down {
  color: #ef4444;
  text-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
  animation: price-bounce 0.6s ease-out;
}

@keyframes price-bounce {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* 🔥 Hot Navigation */
.hot-nav-tab {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.hot-nav-tab::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 0;
  height: 3px;
  background: var(--gradient-hot);
  transition: width 0.3s ease;
}

.hot-nav-tab.active::before,
.hot-nav-tab:hover::before {
  width: 100%;
}

.hot-nav-tab.active {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  backdrop-filter: blur(10px);
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}