import React, { useState, useRef, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { EnhancedAnalysisResults } from './EnhancedAnalysisResults';
import { marketDataService } from '../services/marketDataService';
import {
  Zap,
  TrendingUp,
  BarChart3,
  PieChart,
  Brain,
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
  Sparkles,
  RefreshCw,
  DollarSign
} from 'lucide-react';

interface CombinedAnalysisProps {
  ticker: string;
  userTier: 'Basic' | 'Pro';
  userTokens: number;
  onTokenDeduct: (amount: number) => void;
}

interface AnalysisProgress {
  step: string;
  progress: number;
  status: 'pending' | 'active' | 'complete' | 'error';
}

export const CombinedAnalysis: React.FC<CombinedAnalysisProps> = ({
  ticker,
  userTier,
  userTokens,
  onTokenDeduct
}) => {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResult, setAnalysisResult] = useState<any>(null);
  const [progress, setProgress] = useState<AnalysisProgress[]>([]);
  const [error, setError] = useState<string | null>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  const COMBINED_ANALYSIS_COST = 75; // Higher token cost for comprehensive analysis

  const analysisSteps: AnalysisProgress[] = [
    { step: 'Fetching Real-time Data', progress: 0, status: 'pending' },
    { step: 'Technical Analysis', progress: 0, status: 'pending' },
    { step: 'Fundamental Analysis', progress: 0, status: 'pending' },
    { step: 'News Sentiment Analysis', progress: 0, status: 'pending' },
    { step: 'Analyst Ratings Review', progress: 0, status: 'pending' },
    { step: 'AI Risk Assessment', progress: 0, status: 'pending' },
    { step: 'Generating Recommendations', progress: 0, status: 'pending' }
  ];

  useEffect(() => {
    if (analysisResult && resultsRef.current) {
      const scrollToResults = () => {
        resultsRef.current?.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
          inline: 'nearest'
        });
      };

      setTimeout(scrollToResults, 300);
      setTimeout(scrollToResults, 600);
      setTimeout(scrollToResults, 1000);
    }
  }, [analysisResult]);

  const updateProgress = (stepIndex: number, progress: number, status: AnalysisProgress['status']) => {
    setProgress(prev => prev.map((step, index) =>
      index === stepIndex ? { ...step, progress, status } : step
    ));
  };

  const generateComprehensiveAnalysis = async () => {
    try {
      // Get comprehensive market data
      const data = await marketDataService.getComprehensiveAnalysis(ticker);

      // Generate AI-powered analysis conclusion
      const technicalSignal = data.technical.rsi > 70 ? 'OVERBOUGHT' :
                             data.technical.rsi < 30 ? 'OVERSOLD' : 'NEUTRAL';

      const fundamentalScore = (
        (data.fundamental.returnOnEquity > 15 ? 1 : 0) +
        (data.fundamental.debtToEquity < 0.5 ? 1 : 0) +
        (data.fundamental.currentRatio > 1.5 ? 1 : 0) +
        (data.fundamental.profitMargin > 10 ? 1 : 0) +
        (data.fundamental.earningsGrowth > 0 ? 1 : 0)
      );

      const newsScore = data.news.reduce((acc, item) => {
        return acc + (item.sentiment === 'positive' ? 1 :
                     item.sentiment === 'negative' ? -1 : 0);
      }, 0) / data.news.length;

      const analystScore = data.ratings.reduce((acc, rating) => {
        const scores = { 'Strong Buy': 2, 'Buy': 1, 'Hold': 0, 'Sell': -1, 'Strong Sell': -2 };
        return acc + scores[rating.rating];
      }, 0) / data.ratings.length;

      const overallScore = (fundamentalScore * 0.3) + (newsScore * 0.2) + (analystScore * 0.3) +
                          (technicalSignal === 'OVERSOLD' ? 0.2 : technicalSignal === 'OVERBOUGHT' ? -0.2 : 0);

      const recommendation = overallScore > 1 ? 'BUY' : overallScore < -1 ? 'SELL' : 'HOLD';
      const confidence = Math.min(95, Math.max(60, 75 + Math.abs(overallScore) * 10));
      const riskLevel = data.stock.beta > 1.5 ? 'HIGH' : data.stock.beta < 0.8 ? 'LOW' : 'MEDIUM';

      // Calculate target price based on multiple factors
      const avgAnalystTarget = data.ratings.reduce((acc, r) => acc + r.targetPrice, 0) / data.ratings.length;
      const technicalTarget = data.stock.currentPrice * (1 + (overallScore * 0.1));
      const targetPrice = (avgAnalystTarget + technicalTarget) / 2;

      const analysisData = {
        ticker: data.stock.ticker,
        currentPrice: data.stock.currentPrice,
        priceChange: data.stock.priceChange,
        priceChangePercent: data.stock.priceChangePercent,
        volume: data.stock.volume,
        marketCap: data.stock.marketCap,
        peRatio: data.stock.peRatio,
        dividend: data.stock.dividend,
        high52Week: data.stock.high52Week,
        low52Week: data.stock.low52Week,
        recommendation,
        confidence: Math.round(confidence),
        riskLevel,
        targetPrice: Math.round(targetPrice * 100) / 100,
        analysisType: 'Combined Analysis',
        timestamp: new Date().toISOString()
      };

      const rawAnalysis = `
🔍 COMPREHENSIVE ANALYSIS REPORT FOR ${ticker}
═══════════════════════════════════════════════

📊 EXECUTIVE SUMMARY
Current Price: $${data.stock.currentPrice}
Recommendation: ${recommendation} (${Math.round(confidence)}% confidence)
Target Price: $${targetPrice.toFixed(2)}
Risk Level: ${riskLevel}

📈 TECHNICAL ANALYSIS
RSI (14): ${data.technical.rsi.toFixed(2)} - ${technicalSignal}
MACD: ${data.technical.macd.macd.toFixed(3)} (Signal: ${data.technical.macd.signal.toFixed(3)})
Moving Averages:
  • SMA 20: $${data.technical.sma20.toFixed(2)}
  • SMA 50: $${data.technical.sma50.toFixed(2)}
  • SMA 200: $${data.technical.sma200.toFixed(2)}
Bollinger Bands: $${data.technical.bollingerBands.lower.toFixed(2)} - $${data.technical.bollingerBands.upper.toFixed(2)}

💰 FUNDAMENTAL ANALYSIS
Financial Health Score: ${fundamentalScore}/5
Key Metrics:
  • P/E Ratio: ${data.stock.peRatio}
  • ROE: ${data.fundamental.returnOnEquity.toFixed(2)}%
  • Debt/Equity: ${data.fundamental.debtToEquity.toFixed(2)}
  • Current Ratio: ${data.fundamental.currentRatio.toFixed(2)}
  • Profit Margin: ${data.fundamental.profitMargin.toFixed(2)}%
  • Revenue Growth: ${data.fundamental.revenueGrowth.toFixed(2)}%

📰 NEWS SENTIMENT ANALYSIS
Overall Sentiment: ${newsScore > 0.2 ? 'POSITIVE' : newsScore < -0.2 ? 'NEGATIVE' : 'NEUTRAL'}
Recent Headlines:
${data.news.slice(0, 3).map(item => `  • ${item.title} (${item.sentiment.toUpperCase()})`).join('\n')}

🏦 ANALYST CONSENSUS
Average Rating: ${analystScore > 0.5 ? 'BUY' : analystScore < -0.5 ? 'SELL' : 'HOLD'}
Average Target: $${(data.ratings.reduce((acc, r) => acc + r.targetPrice, 0) / data.ratings.length).toFixed(2)}
Recent Ratings:
${data.ratings.slice(0, 3).map(rating => `  • ${rating.firm}: ${rating.rating} ($${rating.targetPrice})`).join('\n')}

🎯 INVESTMENT THESIS
${recommendation === 'BUY' ?
  `Strong buy signal based on favorable technical indicators, solid fundamentals, and positive market sentiment. The stock shows good value at current levels with upside potential to $${targetPrice.toFixed(2)}.` :
  recommendation === 'SELL' ?
  `Sell recommendation due to concerning technical signals and/or weak fundamentals. Consider taking profits or avoiding new positions until conditions improve.` :
  `Hold recommendation. The stock is fairly valued at current levels. Monitor for better entry/exit opportunities based on market conditions and company developments.`
}

⚠️ RISK FACTORS
• Beta: ${data.stock.beta.toFixed(2)} (${riskLevel} volatility)
• 52-week range: $${data.stock.low52Week} - $${data.stock.high52Week}
• Market conditions and sector rotation risks
• Company-specific execution risks

🔮 CONCLUSION
Based on our comprehensive analysis combining technical indicators, fundamental metrics, news sentiment, and analyst opinions, ${ticker} receives a ${recommendation} rating with ${Math.round(confidence)}% confidence.

Key catalysts to watch:
• Earnings announcements and guidance updates
• Sector performance and market sentiment shifts
• Technical breakouts above/below key support/resistance levels
• Changes in analyst coverage and institutional ownership

⚠️ DISCLAIMER: This analysis is for informational purposes only and should not be considered as investment advice. Always consult with a qualified financial advisor before making investment decisions.
      `;

      return { analysisData, rawAnalysis };
    } catch (error) {
      throw new Error('Failed to generate comprehensive analysis');
    }
  };

  const handleCombinedAnalysis = async () => {
    if (userTokens < COMBINED_ANALYSIS_COST) {
      setError(`Insufficient tokens. Combined analysis requires ${COMBINED_ANALYSIS_COST} tokens.`);
      return;
    }

    if (!ticker || ticker.trim() === '') {
      setError('Please enter a valid stock ticker symbol.');
      return;
    }

    setIsAnalyzing(true);
    setError(null);
    setAnalysisResult(null);
    setProgress([...analysisSteps]);

    try {
      // Simulate analysis steps with progress updates
      for (let i = 0; i < analysisSteps.length; i++) {
        updateProgress(i, 0, 'active');

        // Simulate processing time
        const stepDuration = 800 + Math.random() * 400; // 800-1200ms per step
        const progressInterval = setInterval(() => {
          setProgress(prev => {
            const newProgress = [...prev];
            if (newProgress[i].progress < 100) {
              newProgress[i].progress = Math.min(100, newProgress[i].progress + 10);
            }
            return newProgress;
          });
        }, stepDuration / 10);

        await new Promise(resolve => setTimeout(resolve, stepDuration));
        clearInterval(progressInterval);

        updateProgress(i, 100, 'complete');
      }

      // Generate the actual analysis
      const { analysisData, rawAnalysis } = await generateComprehensiveAnalysis();

      setAnalysisResult({ data: analysisData, rawAnalysis });
      onTokenDeduct(COMBINED_ANALYSIS_COST);

    } catch (error) {
      setError(error instanceof Error ? error.message : 'Analysis failed');
      // Mark current step as error
      setProgress(prev => prev.map(step =>
        step.status === 'active' ? { ...step, status: 'error' } : step
      ));
    } finally {
      setIsAnalyzing(false);
    }
  };

  const getStepIcon = (status: AnalysisProgress['status']) => {
    switch (status) {
      case 'complete': return <CheckCircle className="h-4 w-4 text-green-400" />;
      case 'active': return <RefreshCw className="h-4 w-4 text-blue-400 animate-spin" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-400" />;
      default: return <Clock className="h-4 w-4 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      {/* Combined Analysis Button */}
      <Card className="p-6 bg-gradient-to-r from-purple-900/20 to-blue-900/20 border-purple-500/30">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg">
              <Zap className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-bold text-white flex items-center">
                Combined Analysis
                <Badge className="ml-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white">
                  PRO
                </Badge>
              </h3>
              <p className="text-gray-300 text-sm">
                Comprehensive technical + fundamental + sentiment analysis
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-purple-400 flex items-center">
              <DollarSign className="h-5 w-5" />
              {COMBINED_ANALYSIS_COST}
            </div>
            <div className="text-xs text-gray-400">tokens</div>
          </div>
        </div>

        <div className="mt-4 flex items-center justify-between">
          <div className="flex items-center space-x-4 text-sm text-gray-300">
            <div className="flex items-center space-x-1">
              <BarChart3 className="h-4 w-4" />
              <span>Technical</span>
            </div>
            <div className="flex items-center space-x-1">
              <PieChart className="h-4 w-4" />
              <span>Fundamental</span>
            </div>
            <div className="flex items-center space-x-1">
              <Brain className="h-4 w-4" />
              <span>AI Insights</span>
            </div>
            <div className="flex items-center space-x-1">
              <Target className="h-4 w-4" />
              <span>Price Target</span>
            </div>
          </div>

          <Button
            onClick={handleCombinedAnalysis}
            disabled={isAnalyzing || userTokens < COMBINED_ANALYSIS_COST || userTier === 'Basic'}
            className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white px-6 py-2"
          >
            {isAnalyzing ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Start Combined Analysis
              </>
            )}
          </Button>
        </div>

        {userTier === 'Basic' && (
          <div className="mt-4 p-3 bg-orange-900/20 border border-orange-500/30 rounded-lg">
            <p className="text-orange-300 text-sm">
              🔒 Combined Analysis is a Pro feature. Upgrade to access comprehensive multi-factor analysis.
            </p>
          </div>
        )}
      </Card>

      {/* Progress Display */}
      {isAnalyzing && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
            <Brain className="h-5 w-5 mr-2 text-purple-400" />
            Analysis in Progress
          </h3>
          <div className="space-y-3">
            {progress.map((step, index) => (
              <div key={index} className="flex items-center space-x-3">
                {getStepIcon(step.status)}
                <div className="flex-1">
                  <div className="flex items-center justify-between mb-1">
                    <span className={`text-sm ${
                      step.status === 'complete' ? 'text-green-400' :
                      step.status === 'active' ? 'text-blue-400' :
                      step.status === 'error' ? 'text-red-400' :
                      'text-gray-400'
                    }`}>
                      {step.step}
                    </span>
                    <span className="text-xs text-gray-500">{step.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full transition-all duration-300 ${
                        step.status === 'complete' ? 'bg-green-500' :
                        step.status === 'active' ? 'bg-blue-500' :
                        step.status === 'error' ? 'bg-red-500' :
                        'bg-gray-600'
                      }`}
                      style={{ width: `${step.progress}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Error Display */}
      {error && (
        <Card className="p-4 bg-red-900/20 border-red-500/30">
          <div className="flex items-center space-x-2">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <span className="text-red-300">{error}</span>
          </div>
        </Card>
      )}

      {/* Results */}
      {analysisResult && (
        <div ref={resultsRef}>
          <EnhancedAnalysisResults
            data={analysisResult.data}
            rawAnalysis={analysisResult.rawAnalysis}
            userTier={userTier}
          />
        </div>
      )}
    </div>
  );
};