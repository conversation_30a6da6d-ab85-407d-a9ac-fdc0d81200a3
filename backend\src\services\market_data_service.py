"""
Real-Time Market Data Service for FinanceGPT Pro
Fetches live market data from multiple sources and streams to WebSocket clients
"""

import asyncio
import aiohttp
import logging
from typing import Dict, List, Optional, Any, Set
from datetime import datetime, timedelta
import json
import random
from dataclasses import dataclass
from .websocket_service import websocket_service, PriceUpdate, MarketStatus
from ..agent.configuration import get_config

logger = logging.getLogger(__name__)

@dataclass
class DataSource:
    """Market data source configuration"""
    name: str
    url: str
    api_key: Optional[str]
    rate_limit: int  # requests per minute
    priority: int  # 1 = highest priority

class MarketDataService:
    """Service for fetching and streaming real-time market data"""
    
    def __init__(self):
        self.config = get_config()
        self.data_sources = self._initialize_data_sources()
        self.active_tickers: Set[str] = set()
        self.last_prices: Dict[str, float] = {}
        self.update_interval = 5  # seconds
        self.is_running = False
        self.session: Optional[aiohttp.ClientSession] = None
        
    def _initialize_data_sources(self) -> List[DataSource]:
        """Initialize market data sources"""
        sources = []
        
        # Alpha Vantage (Primary)
        if self.config.api_keys.stocks_api_key:
            sources.append(DataSource(
                name="alpha_vantage",
                url="https://www.alphavantage.co/query",
                api_key=self.config.api_keys.stocks_api_key,
                rate_limit=5,  # 5 requests per minute for free tier
                priority=1
            ))
        
        # Yahoo Finance (Backup - Free)
        sources.append(DataSource(
            name="yahoo_finance",
            url="https://query1.finance.yahoo.com/v8/finance/chart",
            api_key=None,
            rate_limit=100,
            priority=2
        ))
        
        # Mock data source for development
        sources.append(DataSource(
            name="mock_data",
            url="mock://localhost",
            api_key=None,
            rate_limit=1000,
            priority=3
        ))
        
        return sorted(sources, key=lambda x: x.priority)
    
    async def start(self):
        """Start the market data service"""
        if self.is_running:
            return
        
        self.is_running = True
        self.session = aiohttp.ClientSession()
        
        logger.info("Starting market data service...")
        
        # Start background tasks
        asyncio.create_task(self._price_update_loop())
        asyncio.create_task(self._market_status_loop())
        asyncio.create_task(self._cleanup_loop())
        
        logger.info("Market data service started")
    
    async def stop(self):
        """Stop the market data service"""
        self.is_running = False
        
        if self.session:
            await self.session.close()
            self.session = None
        
        logger.info("Market data service stopped")
    
    async def _price_update_loop(self):
        """Main loop for fetching and broadcasting price updates"""
        while self.is_running:
            try:
                # Get currently subscribed tickers
                subscribed_tickers = self._get_subscribed_tickers()
                
                if subscribed_tickers:
                    # Fetch price updates
                    price_updates = await self._fetch_price_updates(subscribed_tickers)
                    
                    # Broadcast updates
                    for price_update in price_updates:
                        await websocket_service.broadcast_price_update(price_update)
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                logger.error(f"Error in price update loop: {str(e)}")
                await asyncio.sleep(self.update_interval)
    
    async def _market_status_loop(self):
        """Loop for updating market status"""
        while self.is_running:
            try:
                market_status = await self._get_market_status()
                await websocket_service.broadcast_market_status(market_status)
                
                # Update every 5 minutes
                await asyncio.sleep(300)
                
            except Exception as e:
                logger.error(f"Error in market status loop: {str(e)}")
                await asyncio.sleep(300)
    
    async def _cleanup_loop(self):
        """Cleanup loop for removing inactive tickers"""
        while self.is_running:
            try:
                # Remove tickers with no subscribers
                current_tickers = self._get_subscribed_tickers()
                self.active_tickers = current_tickers
                
                # Clean up old price data
                cutoff_time = datetime.now() - timedelta(hours=1)
                # Implementation would clean up old cached data
                
                await asyncio.sleep(60)  # Run every minute
                
            except Exception as e:
                logger.error(f"Error in cleanup loop: {str(e)}")
                await asyncio.sleep(60)
    
    def _get_subscribed_tickers(self) -> Set[str]:
        """Get all currently subscribed tickers"""
        return set(websocket_service.manager.ticker_subscribers.keys())
    
    async def _fetch_price_updates(self, tickers: Set[str]) -> List[PriceUpdate]:
        """Fetch price updates for given tickers"""
        price_updates = []
        
        for ticker in tickers:
            try:
                price_data = await self._fetch_ticker_data(ticker)
                if price_data:
                    price_update = self._create_price_update(ticker, price_data)
                    price_updates.append(price_update)
            except Exception as e:
                logger.error(f"Error fetching data for {ticker}: {str(e)}")
        
        return price_updates
    
    async def _fetch_ticker_data(self, ticker: str) -> Optional[Dict[str, Any]]:
        """Fetch data for a specific ticker from available sources"""
        for source in self.data_sources:
            try:
                if source.name == "alpha_vantage":
                    return await self._fetch_from_alpha_vantage(ticker, source)
                elif source.name == "yahoo_finance":
                    return await self._fetch_from_yahoo(ticker, source)
                elif source.name == "mock_data":
                    return await self._fetch_mock_data(ticker)
            except Exception as e:
                logger.warning(f"Failed to fetch from {source.name} for {ticker}: {str(e)}")
                continue
        
        return None
    
    async def _fetch_from_alpha_vantage(self, ticker: str, source: DataSource) -> Optional[Dict[str, Any]]:
        """Fetch data from Alpha Vantage"""
        if not self.session or not source.api_key:
            return None
        
        url = f"{source.url}?function=GLOBAL_QUOTE&symbol={ticker}&apikey={source.api_key}"
        
        async with self.session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                quote = data.get("Global Quote", {})
                
                if quote:
                    return {
                        "price": float(quote.get("05. price", 0)),
                        "change": float(quote.get("09. change", 0)),
                        "change_percent": float(quote.get("10. change percent", "0%").replace("%", "")),
                        "volume": int(quote.get("06. volume", 0)),
                        "source": "alpha_vantage"
                    }
        
        return None
    
    async def _fetch_from_yahoo(self, ticker: str, source: DataSource) -> Optional[Dict[str, Any]]:
        """Fetch data from Yahoo Finance"""
        if not self.session:
            return None
        
        url = f"{source.url}/{ticker}"
        
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    result = data.get("chart", {}).get("result", [])
                    
                    if result:
                        meta = result[0].get("meta", {})
                        return {
                            "price": meta.get("regularMarketPrice", 0),
                            "change": meta.get("regularMarketPrice", 0) - meta.get("previousClose", 0),
                            "change_percent": ((meta.get("regularMarketPrice", 0) - meta.get("previousClose", 1)) / meta.get("previousClose", 1)) * 100,
                            "volume": meta.get("regularMarketVolume", 0),
                            "source": "yahoo_finance"
                        }
        except Exception as e:
            logger.warning(f"Yahoo Finance error for {ticker}: {str(e)}")
        
        return None
    
    async def _fetch_mock_data(self, ticker: str) -> Dict[str, Any]:
        """Generate mock data for development/testing"""
        # Get last price or use a base price
        base_price = self.last_prices.get(ticker, self._get_base_price(ticker))
        
        # Generate realistic price movement
        change_percent = random.uniform(-2.0, 2.0)  # ±2% change
        new_price = base_price * (1 + change_percent / 100)
        change = new_price - base_price
        
        # Update last price
        self.last_prices[ticker] = new_price
        
        return {
            "price": round(new_price, 2),
            "change": round(change, 2),
            "change_percent": round(change_percent, 2),
            "volume": random.randint(100000, 10000000),
            "source": "mock_data"
        }
    
    def _get_base_price(self, ticker: str) -> float:
        """Get base price for mock data"""
        # Common stock base prices for realistic simulation
        base_prices = {
            "AAPL": 175.0,
            "MSFT": 350.0,
            "GOOGL": 140.0,
            "AMZN": 145.0,
            "TSLA": 250.0,
            "META": 320.0,
            "NVDA": 450.0,
            "JPM": 150.0,
            "JNJ": 160.0,
            "PG": 155.0
        }
        return base_prices.get(ticker, 100.0)
    
    def _create_price_update(self, ticker: str, price_data: Dict[str, Any]) -> PriceUpdate:
        """Create PriceUpdate object from price data"""
        return PriceUpdate(
            ticker=ticker,
            price=price_data["price"],
            change=price_data["change"],
            change_percent=price_data["change_percent"],
            volume=price_data["volume"],
            timestamp=datetime.now().isoformat(),
            market_status=self._get_current_market_status()
        )
    
    async def _get_market_status(self) -> MarketStatus:
        """Get current market status"""
        now = datetime.now()
        
        # Simple market hours check (9:30 AM - 4:00 PM ET, Monday-Friday)
        # In production, this would be more sophisticated
        weekday = now.weekday()  # 0 = Monday, 6 = Sunday
        hour = now.hour
        
        if weekday < 5:  # Monday to Friday
            if 9 <= hour < 16:  # 9 AM to 4 PM (simplified)
                status = "OPEN"
            elif hour < 9:
                status = "PRE_MARKET"
            else:
                status = "AFTER_HOURS"
        else:
            status = "CLOSED"
        
        return MarketStatus(
            status=status,
            next_open=self._calculate_next_open(),
            next_close=self._calculate_next_close(),
            timezone="US/Eastern"
        )
    
    def _get_current_market_status(self) -> str:
        """Get simple market status string"""
        now = datetime.now()
        weekday = now.weekday()
        hour = now.hour
        
        if weekday < 5 and 9 <= hour < 16:
            return "OPEN"
        else:
            return "CLOSED"
    
    def _calculate_next_open(self) -> Optional[str]:
        """Calculate next market open time"""
        # Simplified calculation
        now = datetime.now()
        if now.weekday() < 5 and now.hour < 9:
            # Same day at 9 AM
            next_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        else:
            # Next business day at 9 AM
            days_ahead = 1
            if now.weekday() == 4:  # Friday
                days_ahead = 3  # Skip to Monday
            elif now.weekday() == 5:  # Saturday
                days_ahead = 2  # Skip to Monday
            
            next_open = now + timedelta(days=days_ahead)
            next_open = next_open.replace(hour=9, minute=30, second=0, microsecond=0)
        
        return next_open.isoformat()
    
    def _calculate_next_close(self) -> Optional[str]:
        """Calculate next market close time"""
        # Simplified calculation
        now = datetime.now()
        if now.weekday() < 5 and now.hour < 16:
            # Same day at 4 PM
            next_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
        else:
            # Next business day at 4 PM
            days_ahead = 1
            if now.weekday() == 4:  # Friday
                days_ahead = 3  # Skip to Monday
            elif now.weekday() == 5:  # Saturday
                days_ahead = 2  # Skip to Monday
            
            next_close = now + timedelta(days=days_ahead)
            next_close = next_close.replace(hour=16, minute=0, second=0, microsecond=0)
        
        return next_close.isoformat()
    
    async def get_current_price(self, ticker: str) -> Optional[PriceUpdate]:
        """Get current price for a ticker (on-demand)"""
        try:
            price_data = await self._fetch_ticker_data(ticker)
            if price_data:
                return self._create_price_update(ticker, price_data)
        except Exception as e:
            logger.error(f"Error getting current price for {ticker}: {str(e)}")
        
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics"""
        return {
            "active_tickers": list(self.active_tickers),
            "data_sources": [{"name": source.name, "priority": source.priority} for source in self.data_sources],
            "update_interval": self.update_interval,
            "is_running": self.is_running,
            "last_prices_count": len(self.last_prices)
        }

# Global market data service instance
market_data_service = MarketDataService()
