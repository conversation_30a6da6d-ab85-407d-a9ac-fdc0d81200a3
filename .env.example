# Required API Keys
GEMINI_API_KEY=your_gemini_api_key_here

# Optional API Keys (for enhanced functionality)
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_google_search_engine_id_here
STOCK_API_KEY=your_alpha_vantage_api_key_here

# Lang<PERSON>mith (Optional - for debugging and monitoring)
LANGSMITH_API_KEY=your_langsmith_api_key_here

# Database Configuration (automatically set by docker-compose)
REDIS_URI=redis://langgraph-redis:6379
POSTGRES_URI=****************************************************/postgres?sslmode=disable

# LangGraph Configuration
LANGGRAPH_URL=http://localhost:2024
