#!/usr/bin/env python3
"""
Advanced Features Test Suite
Tests educational content, advanced analytics, and enhanced features
"""

import sys
import os
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.educational_service import educational_service, EducationLevel, ContentType
from services.advanced_analytics_service import advanced_analytics_service
from agent.tools_and_schemas import get_educational_content, get_advanced_analytics

def test_educational_service():
    """Test educational content service"""
    print("🧪 Testing Educational Service")
    print("=" * 50)
    
    passed = 0
    total = 0
    
    # Test getting content by level
    total += 1
    beginner_content = educational_service.get_content_by_level(EducationLevel.BEGINNER)
    if beginner_content and len(beginner_content) > 0:
        print(f"✅ Found {len(beginner_content)} beginner content items")
        passed += 1
    else:
        print("❌ No beginner content found")
    
    # Test getting content by category
    total += 1
    fundamentals_content = educational_service.get_content_by_category("fundamentals")
    if fundamentals_content and len(fundamentals_content) > 0:
        print(f"✅ Found {len(fundamentals_content)} fundamentals content items")
        passed += 1
    else:
        print("❌ No fundamentals content found")
    
    # Test search functionality
    total += 1
    search_results = educational_service.search_content("stocks")
    if search_results and len(search_results) > 0:
        print(f"✅ Search found {len(search_results)} results for 'stocks'")
        passed += 1
    else:
        print("❌ Search failed to find content")
    
    # Test getting specific content
    total += 1
    specific_content = educational_service.get_content_by_id("basics_001")
    if specific_content and "title" in specific_content:
        print(f"✅ Retrieved specific content: {specific_content['title']}")
        passed += 1
    else:
        print("❌ Failed to retrieve specific content")
    
    # Test recommendations
    total += 1
    recommendations = educational_service.get_recommended_content(
        EducationLevel.BEGINNER, 
        ["investing", "basics"]
    )
    if recommendations and len(recommendations) > 0:
        print(f"✅ Got {len(recommendations)} recommendations")
        passed += 1
    else:
        print("❌ No recommendations generated")
    
    # Test learning path
    total += 1
    learning_path = educational_service.get_learning_path(EducationLevel.BEGINNER)
    if learning_path and len(learning_path) > 0:
        print(f"✅ Generated learning path with {len(learning_path)} items")
        passed += 1
    else:
        print("❌ Failed to generate learning path")
    
    print(f"Educational Service tests: {passed}/{total} passed")
    return passed == total

def test_advanced_analytics():
    """Test advanced analytics service"""
    print("\n🧪 Testing Advanced Analytics Service")
    print("=" * 50)
    
    passed = 0
    total = 0
    
    # Generate mock price data
    import random
    price_data1 = [100 + random.uniform(-10, 10) for _ in range(100)]
    price_data2 = [150 + random.uniform(-15, 15) for _ in range(100)]
    
    # Test correlation analysis
    total += 1
    try:
        correlation = advanced_analytics_service.analyze_correlation(
            "AAPL", "MSFT", price_data1, price_data2
        )
        if hasattr(correlation, 'correlation_coefficient'):
            print(f"✅ Correlation analysis: {correlation.correlation_coefficient:.3f} ({correlation.strength})")
            passed += 1
        else:
            print("❌ Correlation analysis failed")
    except Exception as e:
        print(f"❌ Correlation analysis error: {str(e)}")
    
    # Test volatility analysis
    total += 1
    try:
        volatility = advanced_analytics_service.analyze_volatility("AAPL", price_data1)
        if hasattr(volatility, 'current_volatility'):
            print(f"✅ Volatility analysis: {volatility.current_volatility:.2%} ({volatility.risk_level})")
            passed += 1
        else:
            print("❌ Volatility analysis failed")
    except Exception as e:
        print(f"❌ Volatility analysis error: {str(e)}")
    
    # Test momentum analysis
    total += 1
    try:
        momentum = advanced_analytics_service.analyze_momentum("AAPL", price_data1)
        if hasattr(momentum, 'momentum_score'):
            print(f"✅ Momentum analysis: {momentum.momentum_score:.4f} ({momentum.trend_strength})")
            passed += 1
        else:
            print("❌ Momentum analysis failed")
    except Exception as e:
        print(f"❌ Momentum analysis error: {str(e)}")
    
    # Test sector analysis
    total += 1
    try:
        sector_analysis = advanced_analytics_service.analyze_sector_performance(
            ["AAPL", "MSFT", "GOOGL", "TSLA"]
        )
        if sector_analysis and len(sector_analysis) > 0:
            print(f"✅ Sector analysis: {len(sector_analysis)} sectors analyzed")
            passed += 1
        else:
            print("❌ Sector analysis failed")
    except Exception as e:
        print(f"❌ Sector analysis error: {str(e)}")
    
    # Test risk metrics
    total += 1
    try:
        risk_metrics = advanced_analytics_service.calculate_risk_metrics("AAPL", price_data1)
        if "volatility" in risk_metrics and "sharpe_ratio" in risk_metrics:
            print(f"✅ Risk metrics: Volatility {risk_metrics['volatility']:.2%}, Sharpe {risk_metrics['sharpe_ratio']:.3f}")
            passed += 1
        else:
            print("❌ Risk metrics calculation failed")
    except Exception as e:
        print(f"❌ Risk metrics error: {str(e)}")
    
    print(f"Advanced Analytics tests: {passed}/{total} passed")
    return passed == total

def test_educational_tool():
    """Test educational content tool"""
    print("\n🧪 Testing Educational Content Tool")
    print("=" * 50)
    
    passed = 0
    total = 0
    
    # Test basic content retrieval
    total += 1
    try:
        content = get_educational_content.invoke({"topic": "basics", "level": "beginner"})
        if "Stock Market Basics" in content and len(content) > 100:
            print("✅ Educational tool returned basics content")
            passed += 1
        else:
            print("❌ Educational tool failed to return proper content")
    except Exception as e:
        print(f"❌ Educational tool error: {str(e)}")

    # Test technical content
    total += 1
    try:
        content = get_educational_content.invoke({"topic": "technical", "level": "intermediate"})
        if "Technical Analysis" in content and len(content) > 100:
            print("✅ Educational tool returned technical content")
            passed += 1
        else:
            print("❌ Educational tool failed to return technical content")
    except Exception as e:
        print(f"❌ Educational tool technical content error: {str(e)}")

    # Test portfolio content
    total += 1
    try:
        content = get_educational_content.invoke({"topic": "portfolio", "level": "intermediate"})
        if "Portfolio" in content and "Diversification" in content:
            print("✅ Educational tool returned portfolio content")
            passed += 1
        else:
            print("❌ Educational tool failed to return portfolio content")
    except Exception as e:
        print(f"❌ Educational tool portfolio content error: {str(e)}")

    # Test glossary content
    total += 1
    try:
        content = get_educational_content.invoke({"topic": "glossary", "level": "beginner"})
        if "Glossary" in content and "Asset" in content:
            print("✅ Educational tool returned glossary content")
            passed += 1
        else:
            print("❌ Educational tool failed to return glossary content")
    except Exception as e:
        print(f"❌ Educational tool glossary content error: {str(e)}")
    
    print(f"Educational Tool tests: {passed}/{total} passed")
    return passed == total

def test_advanced_analytics_tool():
    """Test advanced analytics tool"""
    print("\n🧪 Testing Advanced Analytics Tool")
    print("=" * 50)
    
    passed = 0
    total = 0
    
    # Test momentum analysis
    total += 1
    try:
        result = get_advanced_analytics.invoke({"ticker": "AAPL", "analysis_type": "momentum"})
        if "Momentum Analysis" in result and "Trend Strength" in result:
            print("✅ Advanced analytics tool returned momentum analysis")
            passed += 1
        else:
            print("❌ Advanced analytics tool failed momentum analysis")
    except Exception as e:
        print(f"❌ Advanced analytics momentum error: {str(e)}")

    # Test volatility analysis
    total += 1
    try:
        result = get_advanced_analytics.invoke({"ticker": "MSFT", "analysis_type": "volatility"})
        if "Volatility Analysis" in result and "Risk Level" in result:
            print("✅ Advanced analytics tool returned volatility analysis")
            passed += 1
        else:
            print("❌ Advanced analytics tool failed volatility analysis")
    except Exception as e:
        print(f"❌ Advanced analytics volatility error: {str(e)}")

    # Test risk analysis
    total += 1
    try:
        result = get_advanced_analytics.invoke({"ticker": "GOOGL", "analysis_type": "risk"})
        if "Risk Metrics" in result and "Sharpe Ratio" in result:
            print("✅ Advanced analytics tool returned risk analysis")
            passed += 1
        else:
            print("❌ Advanced analytics tool failed risk analysis")
    except Exception as e:
        print(f"❌ Advanced analytics risk error: {str(e)}")

    # Test invalid analysis type
    total += 1
    try:
        result = get_advanced_analytics.invoke({"ticker": "TSLA", "analysis_type": "invalid"})
        if "Unknown analysis type" in result:
            print("✅ Advanced analytics tool handled invalid type correctly")
            passed += 1
        else:
            print("❌ Advanced analytics tool failed to handle invalid type")
    except Exception as e:
        print(f"❌ Advanced analytics invalid type error: {str(e)}")

    # Test invalid ticker
    total += 1
    try:
        result = get_advanced_analytics.invoke({"ticker": "INVALID@", "analysis_type": "momentum"})
        if "Invalid ticker" in result:
            print("✅ Advanced analytics tool handled invalid ticker correctly")
            passed += 1
        else:
            print("❌ Advanced analytics tool failed to handle invalid ticker")
    except Exception as e:
        print(f"❌ Advanced analytics invalid ticker error: {str(e)}")
    
    print(f"Advanced Analytics Tool tests: {passed}/{total} passed")
    return passed == total

def main():
    """Run comprehensive advanced features tests"""
    print("🚀 ADVANCED FEATURES TEST SUITE")
    print("=" * 80)
    print("Testing educational content, advanced analytics, and enhanced features")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_functions = [
        test_educational_service,
        test_advanced_analytics,
        test_educational_tool,
        test_advanced_analytics_tool,
    ]
    
    results = []
    for test_func in test_functions:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {str(e)}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    print("\n" + "=" * 80)
    print("📊 ADVANCED FEATURES TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Passed: {passed}/{total} test suites")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 EXCELLENT! Advanced features are working perfectly!")
        print("\n🏆 Advanced Features Implementation Complete:")
        print("   ✅ Educational Content Service")
        print("   ✅ Advanced Analytics Engine")
        print("   ✅ Educational Content Tool")
        print("   ✅ Advanced Analytics Tool")
        print("   ✅ Comprehensive Error Handling")
        print("   ✅ Performance Optimizations")
    elif success_rate >= 60:
        print("👍 GOOD! Advanced features are working well.")
    else:
        print("⚠️ NEEDS WORK! Advanced features need attention.")
    
    print("=" * 80)
    return success_rate >= 80

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
