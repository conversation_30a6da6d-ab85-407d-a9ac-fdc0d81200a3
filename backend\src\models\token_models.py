"""
Token Management Models for FinanceGPT Pro
Handles user tokens, usage tracking, and subscription management
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from enum import Enum
import json
import uuid

class SubscriptionTier(Enum):
    FREE = "free"
    PROFESSIONAL = "professional"
    ENTERPRISE = "enterprise"
    UNLIMITED = "unlimited"

class AnalysisType(Enum):
    PRICE_CHECK = "price-check"
    TECHNICAL_ANALYSIS = "technical-analysis"
    SENTIMENT_ANALYSIS = "sentiment-analysis"
    FUNDAMENTAL_ANALYSIS = "fundamental-analysis"
    INVESTMENT_RECOMMENDATION = "investment-recommendation"
    COMPREHENSIVE_ANALYSIS = "comprehensive-analysis"
    IMAGE_ANALYSIS = "image-analysis"
    CUSTOM_QUERY = "custom-query"

@dataclass
class TokenPricing:
    """Token pricing configuration for different analysis types"""
    
    # Basic Analysis (10-30 tokens)
    PRICE_CHECK: int = 10
    BASIC_SENTIMENT: int = 15
    SIMPLE_TECHNICAL: int = 20
    CUSTOM_QUERY: int = 25
    
    # Advanced Analysis (30-75 tokens)
    COMPREHENSIVE_TECHNICAL: int = 50
    FUNDAMENTAL_ANALYSIS: int = 60
    RISK_ASSESSMENT: int = 45
    INVESTMENT_RECOMMENDATION: int = 75
    
    # Premium Features (75-150 tokens)
    IMAGE_ANALYSIS: int = 100
    PORTFOLIO_OPTIMIZATION: int = 120
    MULTI_STOCK_COMPARISON: int = 80
    COMPREHENSIVE_ANALYSIS: int = 150
    
    # Enterprise Features (150+ tokens)
    API_ACCESS: int = 300
    WHITE_LABEL: int = 500

    @classmethod
    def get_cost(cls, analysis_type: AnalysisType) -> int:
        """Get token cost for a specific analysis type"""
        cost_mapping = {
            AnalysisType.PRICE_CHECK: cls.PRICE_CHECK,
            AnalysisType.TECHNICAL_ANALYSIS: cls.COMPREHENSIVE_TECHNICAL,
            AnalysisType.SENTIMENT_ANALYSIS: cls.BASIC_SENTIMENT,
            AnalysisType.FUNDAMENTAL_ANALYSIS: cls.FUNDAMENTAL_ANALYSIS,
            AnalysisType.INVESTMENT_RECOMMENDATION: cls.INVESTMENT_RECOMMENDATION,
            AnalysisType.COMPREHENSIVE_ANALYSIS: cls.COMPREHENSIVE_ANALYSIS,
            AnalysisType.IMAGE_ANALYSIS: cls.IMAGE_ANALYSIS,
            AnalysisType.CUSTOM_QUERY: cls.CUSTOM_QUERY,
        }
        return cost_mapping.get(analysis_type, cls.CUSTOM_QUERY)

@dataclass
class SubscriptionConfig:
    """Configuration for subscription tiers"""
    tier: SubscriptionTier
    monthly_tokens: int
    daily_limit: int
    features: List[str]
    price_monthly: float
    name: str
    description: str

# Subscription tier configurations
SUBSCRIPTION_TIERS = {
    SubscriptionTier.FREE: SubscriptionConfig(
        tier=SubscriptionTier.FREE,
        monthly_tokens=100,
        daily_limit=50,
        features=['basic'],
        price_monthly=0.0,
        name="Free Tier",
        description="Basic stock analysis with limited features"
    ),
    SubscriptionTier.PROFESSIONAL: SubscriptionConfig(
        tier=SubscriptionTier.PROFESSIONAL,
        monthly_tokens=5000,
        daily_limit=1000,
        features=['basic', 'advanced'],
        price_monthly=14.99,
        name="Professional",
        description="Advanced analysis with technical indicators and sentiment"
    ),
    SubscriptionTier.ENTERPRISE: SubscriptionConfig(
        tier=SubscriptionTier.ENTERPRISE,
        monthly_tokens=25000,
        daily_limit=5000,
        features=['basic', 'advanced', 'premium'],
        price_monthly=49.99,
        name="Enterprise",
        description="Full access with image analysis and portfolio optimization"
    ),
    SubscriptionTier.UNLIMITED: SubscriptionConfig(
        tier=SubscriptionTier.UNLIMITED,
        monthly_tokens=999999,
        daily_limit=10000,
        features=['all'],
        price_monthly=99.99,
        name="Unlimited",
        description="Unlimited analyses with all premium features"
    )
}

@dataclass
class UserProfile:
    """User profile with token and subscription information"""
    user_id: str
    username: str
    email: str
    current_tokens: int
    subscription_tier: SubscriptionTier
    subscription_start: datetime
    subscription_end: datetime
    total_tokens_purchased: int = 0
    total_tokens_used: int = 0
    daily_usage: int = 0
    last_usage_reset: datetime = field(default_factory=datetime.now)
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)

    def can_afford(self, token_cost: int) -> bool:
        """Check if user can afford the token cost"""
        return self.current_tokens >= token_cost

    def has_daily_limit_remaining(self, token_cost: int) -> bool:
        """Check if user has daily limit remaining"""
        config = SUBSCRIPTION_TIERS[self.subscription_tier]
        
        # Reset daily usage if it's a new day
        if self.last_usage_reset.date() < datetime.now().date():
            self.daily_usage = 0
            self.last_usage_reset = datetime.now()
        
        return (self.daily_usage + token_cost) <= config.daily_limit

    def consume_tokens(self, token_cost: int) -> bool:
        """Consume tokens if available and within limits"""
        if not self.can_afford(token_cost):
            return False
        
        if not self.has_daily_limit_remaining(token_cost):
            return False
        
        self.current_tokens -= token_cost
        self.total_tokens_used += token_cost
        self.daily_usage += token_cost
        self.updated_at = datetime.now()
        
        return True

    def add_tokens(self, token_amount: int):
        """Add tokens to user account"""
        self.current_tokens += token_amount
        self.total_tokens_purchased += token_amount
        self.updated_at = datetime.now()

    def is_subscription_active(self) -> bool:
        """Check if subscription is still active"""
        return datetime.now() < self.subscription_end

    def get_subscription_status(self) -> Dict:
        """Get detailed subscription status"""
        config = SUBSCRIPTION_TIERS[self.subscription_tier]
        days_remaining = (self.subscription_end - datetime.now()).days
        
        return {
            "tier": self.subscription_tier.value,
            "name": config.name,
            "is_active": self.is_subscription_active(),
            "days_remaining": max(0, days_remaining),
            "monthly_tokens": config.monthly_tokens,
            "daily_limit": config.daily_limit,
            "daily_usage": self.daily_usage,
            "daily_remaining": max(0, config.daily_limit - self.daily_usage),
            "features": config.features
        }

@dataclass
class TokenTransaction:
    """Record of token transactions"""
    transaction_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str = ""
    transaction_type: str = ""  # "purchase", "usage", "refund", "bonus"
    token_amount: int = 0
    analysis_type: Optional[AnalysisType] = None
    description: str = ""
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict = field(default_factory=dict)

@dataclass
class UsageAnalytics:
    """Analytics for token usage patterns"""
    user_id: str
    date: datetime
    total_tokens_used: int = 0
    analysis_breakdown: Dict[str, int] = field(default_factory=dict)
    peak_usage_hour: int = 0
    unique_tickers_analyzed: int = 0
    success_rate: float = 0.0
    
    def add_usage(self, analysis_type: AnalysisType, token_cost: int, success: bool = True):
        """Add usage data"""
        self.total_tokens_used += token_cost
        
        analysis_key = analysis_type.value
        if analysis_key not in self.analysis_breakdown:
            self.analysis_breakdown[analysis_key] = 0
        self.analysis_breakdown[analysis_key] += token_cost
        
        # Update success rate
        current_total = sum(self.analysis_breakdown.values())
        if current_total > 0:
            self.success_rate = (self.success_rate * (current_total - token_cost) + (1.0 if success else 0.0) * token_cost) / current_total

class TokenManager:
    """Main token management class"""
    
    def __init__(self):
        self.users: Dict[str, UserProfile] = {}
        self.transactions: List[TokenTransaction] = []
        self.analytics: Dict[str, UsageAnalytics] = {}
    
    def create_user(self, user_id: str, username: str, email: str, 
                   subscription_tier: SubscriptionTier = SubscriptionTier.FREE) -> UserProfile:
        """Create a new user profile"""
        config = SUBSCRIPTION_TIERS[subscription_tier]
        
        user = UserProfile(
            user_id=user_id,
            username=username,
            email=email,
            current_tokens=config.monthly_tokens,
            subscription_tier=subscription_tier,
            subscription_start=datetime.now(),
            subscription_end=datetime.now() + timedelta(days=30)
        )
        
        self.users[user_id] = user
        return user
    
    def get_user(self, user_id: str) -> Optional[UserProfile]:
        """Get user profile"""
        return self.users.get(user_id)
    
    def process_analysis_request(self, user_id: str, analysis_type: AnalysisType) -> Dict:
        """Process an analysis request and consume tokens"""
        user = self.get_user(user_id)
        if not user:
            return {"success": False, "error": "User not found"}
        
        token_cost = TokenPricing.get_cost(analysis_type)
        
        # Check if user can afford and is within limits
        if not user.can_afford(token_cost):
            return {
                "success": False, 
                "error": "Insufficient tokens",
                "required": token_cost,
                "available": user.current_tokens
            }
        
        if not user.has_daily_limit_remaining(token_cost):
            config = SUBSCRIPTION_TIERS[user.subscription_tier]
            return {
                "success": False,
                "error": "Daily limit exceeded",
                "daily_limit": config.daily_limit,
                "daily_usage": user.daily_usage
            }
        
        # Consume tokens
        if user.consume_tokens(token_cost):
            # Record transaction
            transaction = TokenTransaction(
                user_id=user_id,
                transaction_type="usage",
                token_amount=-token_cost,
                analysis_type=analysis_type,
                description=f"Analysis: {analysis_type.value}"
            )
            self.transactions.append(transaction)
            
            # Update analytics
            self._update_analytics(user_id, analysis_type, token_cost)
            
            return {
                "success": True,
                "tokens_consumed": token_cost,
                "remaining_tokens": user.current_tokens,
                "daily_remaining": SUBSCRIPTION_TIERS[user.subscription_tier].daily_limit - user.daily_usage
            }
        
        return {"success": False, "error": "Failed to consume tokens"}
    
    def purchase_tokens(self, user_id: str, package_id: str) -> Dict:
        """Process token purchase"""
        user = self.get_user(user_id)
        if not user:
            return {"success": False, "error": "User not found"}
        
        # Token packages
        packages = {
            'starter': {'tokens': 500, 'bonus': 0, 'price': 4.99},
            'professional': {'tokens': 2000, 'bonus': 200, 'price': 14.99},
            'enterprise': {'tokens': 5000, 'bonus': 1000, 'price': 29.99},
            'unlimited': {'tokens': 999999, 'bonus': 0, 'price': 99.99}
        }
        
        if package_id not in packages:
            return {"success": False, "error": "Invalid package"}
        
        package = packages[package_id]
        total_tokens = package['tokens'] + package['bonus']
        
        # Add tokens to user account
        user.add_tokens(total_tokens)
        
        # Record transaction
        transaction = TokenTransaction(
            user_id=user_id,
            transaction_type="purchase",
            token_amount=total_tokens,
            description=f"Token purchase: {package_id}",
            metadata={"package_id": package_id, "price": package['price']}
        )
        self.transactions.append(transaction)
        
        return {
            "success": True,
            "tokens_added": total_tokens,
            "new_balance": user.current_tokens,
            "package": package_id
        }
    
    def _update_analytics(self, user_id: str, analysis_type: AnalysisType, token_cost: int):
        """Update usage analytics"""
        today = datetime.now().date()
        key = f"{user_id}_{today}"
        
        if key not in self.analytics:
            self.analytics[key] = UsageAnalytics(user_id=user_id, date=datetime.now())
        
        self.analytics[key].add_usage(analysis_type, token_cost)
    
    def get_user_analytics(self, user_id: str, days: int = 30) -> Dict:
        """Get user analytics for specified days"""
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=days)
        
        total_usage = 0
        analysis_breakdown = {}
        daily_usage = []
        
        for i in range(days):
            date = start_date + timedelta(days=i)
            key = f"{user_id}_{date}"
            
            if key in self.analytics:
                analytics = self.analytics[key]
                total_usage += analytics.total_tokens_used
                daily_usage.append({
                    "date": date.isoformat(),
                    "tokens_used": analytics.total_tokens_used
                })
                
                for analysis_type, tokens in analytics.analysis_breakdown.items():
                    if analysis_type not in analysis_breakdown:
                        analysis_breakdown[analysis_type] = 0
                    analysis_breakdown[analysis_type] += tokens
            else:
                daily_usage.append({
                    "date": date.isoformat(),
                    "tokens_used": 0
                })
        
        return {
            "total_usage": total_usage,
            "analysis_breakdown": analysis_breakdown,
            "daily_usage": daily_usage,
            "average_daily_usage": total_usage / days if days > 0 else 0
        }

# Global token manager instance
token_manager = TokenManager()
