import React from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { useAuth } from '../contexts/AuthContext';
import { SUBSCRIPTION_TIERS } from '../types/subscription';

interface UsageTrackerProps {
  onUpgrade?: () => void;
}

export const UsageTracker: React.FC<UsageTrackerProps> = ({ onUpgrade }) => {
  const { user } = useAuth();
  
  if (!user) return null;

  const { subscription, usage } = user;
  const tier = SUBSCRIPTION_TIERS[subscription.tier];
  
  const dailyUsagePercent = tier.limits.dailyQueries === -1 
    ? 0 
    : (usage.dailyQueries / tier.limits.dailyQueries) * 100;
    
  const monthlyUsagePercent = (usage.monthlyTokens / tier.limits.monthlyTokens) * 100;
  
  const isNearLimit = dailyUsagePercent > 80 || monthlyUsagePercent > 80;
  const isAtLimit = dailyUsagePercent >= 100 || monthlyUsagePercent >= 100;

  const ProgressBar = ({ 
    label, 
    current, 
    max, 
    percent, 
    color = 'blue' 
  }: { 
    label: string;
    current: number;
    max: number | string;
    percent: number;
    color?: string;
  }) => (
    <div className="space-y-2">
      <div className="flex justify-between items-center">
        <span className="text-sm text-gray-300">{label}</span>
        <span className="text-sm text-white">
          {current} / {max === -1 ? '∞' : max}
        </span>
      </div>
      <div className="w-full bg-gray-700 rounded-full h-2">
        <div 
          className={`h-2 rounded-full transition-all duration-300 ${
            percent >= 100 ? 'bg-red-500' :
            percent >= 80 ? 'bg-yellow-500' :
            color === 'blue' ? 'bg-blue-500' : 'bg-green-500'
          }`}
          style={{ width: `${Math.min(percent, 100)}%` }}
        />
      </div>
      {percent >= 100 && (
        <p className="text-xs text-red-400">Limit reached</p>
      )}
    </div>
  );

  return (
    <Card className="p-4 bg-white/5 backdrop-blur-sm border border-white/20">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold text-white">Usage Overview</h3>
        <Badge 
          className={`${
            subscription.tier === 'pro' 
              ? 'bg-gradient-to-r from-blue-600 to-purple-600' 
              : 'bg-gray-600'
          } text-white`}
        >
          {tier.name}
        </Badge>
      </div>

      <div className="space-y-4">
        {/* Daily Queries */}
        <ProgressBar
          label="Daily Queries"
          current={usage.dailyQueries}
          max={tier.limits.dailyQueries}
          percent={dailyUsagePercent}
          color="blue"
        />

        {/* Monthly Tokens */}
        <ProgressBar
          label="Monthly Tokens"
          current={usage.monthlyTokens}
          max={tier.limits.monthlyTokens}
          percent={monthlyUsagePercent}
          color="green"
        />

        {/* Upgrade Prompt for Free Users */}
        {subscription.tier === 'free' && isNearLimit && (
          <div className="mt-4 p-3 bg-gradient-to-r from-blue-600/20 to-purple-600/20 rounded-lg border border-blue-500/30">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-yellow-400">⚠️</span>
              <span className="text-sm font-medium text-white">
                {isAtLimit ? 'Limit Reached!' : 'Approaching Limit'}
              </span>
            </div>
            <p className="text-xs text-gray-300 mb-3">
              {isAtLimit 
                ? 'You\'ve reached your daily limit. Upgrade to Pro for unlimited access!'
                : 'You\'re running low on queries. Upgrade to Pro for unlimited access and advanced features!'
              }
            </p>
            <Button
              size="sm"
              onClick={onUpgrade}
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            >
              Upgrade to Pro - $29.99/month
            </Button>
          </div>
        )}

        {/* Pro User Benefits */}
        {subscription.tier === 'pro' && (
          <div className="mt-4 p-3 bg-gradient-to-r from-green-600/20 to-blue-600/20 rounded-lg border border-green-500/30">
            <div className="flex items-center gap-2 mb-2">
              <span className="text-green-400">✨</span>
              <span className="text-sm font-medium text-white">Pro Benefits Active</span>
            </div>
            <div className="text-xs text-gray-300 space-y-1">
              <div>• Unlimited daily queries</div>
              <div>• Advanced technical analysis</div>
              <div>• Chart image analysis</div>
              <div>• Priority support</div>
            </div>
          </div>
        )}

        {/* Usage Stats */}
        <div className="mt-4 pt-4 border-t border-white/20">
          <div className="grid grid-cols-2 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-white">{usage.totalQueries}</div>
              <div className="text-xs text-gray-400">Total Queries</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-white">
                {Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
              </div>
              <div className="text-xs text-gray-400">Days Active</div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
};
