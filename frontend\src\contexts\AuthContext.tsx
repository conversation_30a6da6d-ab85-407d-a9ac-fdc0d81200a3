import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { 
  User, 
  AuthState, 
  LoginCredentials, 
  SignupCredentials, 
  SUBSCRIPTION_TIERS 
} from '../types/subscription';

interface AuthContextType extends AuthState {
  login: (credentials: LoginCredentials) => Promise<void>;
  signup: (credentials: SignupCredentials) => Promise<void>;
  logout: () => void;
  checkUsageLimit: (feature: string) => boolean;
  incrementUsage: (tokens: number) => void;
  resetDailyUsage: () => void;
  resetDemoUser: () => void;
  upgradeToProDemo: () => void;
  startProTrial: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [authState, setAuthState] = useState<AuthState>({
    isAuthenticated: false,
    user: null,
    loading: true,
    error: null,
  });

  // Initialize with demo user for development
  useEffect(() => {
    const initializeAuth = () => {
      const savedUser = localStorage.getItem('financeGPT_user');
      if (savedUser) {
        try {
          const user = JSON.parse(savedUser);
          setAuthState({
            isAuthenticated: true,
            user,
            loading: false,
            error: null,
          });
        } catch (error) {
          localStorage.removeItem('financeGPT_user');
          setAuthState(prev => ({ ...prev, loading: false }));
        }
      } else {
        // Create demo free user
        const demoUser: User = {
          id: 'demo-user',
          email: '<EMAIL>',
          name: 'Demo User',
          createdAt: new Date(),
          subscription: {
            tier: 'free',
            status: 'active',
            startDate: new Date(),
          },
          usage: {
            dailyQueries: 0,
            dailyLimit: SUBSCRIPTION_TIERS.free.limits.dailyQueries,
            monthlyTokens: 100, // Tokens USED (out of 500 limit, so 400 available)
            monthlyLimit: SUBSCRIPTION_TIERS.free.limits.monthlyTokens,
            lastResetDate: new Date(),
            totalQueries: 0,
          },
        };

        localStorage.setItem('financeGPT_user', JSON.stringify(demoUser));
        setAuthState({
          isAuthenticated: true,
          user: demoUser,
          loading: false,
          error: null,
        });
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      // Mock login - in real app, call API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const user: User = {
        id: 'user-' + Date.now(),
        email: credentials.email,
        name: credentials.email.split('@')[0],
        createdAt: new Date(),
        subscription: {
          tier: 'free',
          status: 'active',
          startDate: new Date(),
        },
        usage: {
          dailyQueries: 0,
          dailyLimit: SUBSCRIPTION_TIERS.free.limits.dailyQueries,
          monthlyTokens: 100, // Tokens USED (out of 500 limit, so 400 available)
          monthlyLimit: SUBSCRIPTION_TIERS.free.limits.monthlyTokens,
          lastResetDate: new Date(),
          totalQueries: 0,
        },
      };

      localStorage.setItem('financeGPT_user', JSON.stringify(user));
      setAuthState({
        isAuthenticated: true,
        user,
        loading: false,
        error: null,
      });
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: 'Login failed. Please try again.',
      }));
    }
  };

  const signup = async (credentials: SignupCredentials) => {
    setAuthState(prev => ({ ...prev, loading: true, error: null }));
    
    try {
      // Mock signup - in real app, call API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const user: User = {
        id: 'user-' + Date.now(),
        email: credentials.email,
        name: credentials.name,
        createdAt: new Date(),
        subscription: {
          tier: 'free',
          status: 'active',
          startDate: new Date(),
        },
        usage: {
          dailyQueries: 0,
          dailyLimit: SUBSCRIPTION_TIERS.free.limits.dailyQueries,
          monthlyTokens: 100, // Tokens USED (out of 500 limit, so 400 available)
          monthlyLimit: SUBSCRIPTION_TIERS.free.limits.monthlyTokens,
          lastResetDate: new Date(),
          totalQueries: 0,
        },
      };

      localStorage.setItem('financeGPT_user', JSON.stringify(user));
      setAuthState({
        isAuthenticated: true,
        user,
        loading: false,
        error: null,
      });
    } catch (error) {
      setAuthState(prev => ({
        ...prev,
        loading: false,
        error: 'Signup failed. Please try again.',
      }));
    }
  };

  const logout = () => {
    localStorage.removeItem('financeGPT_user');
    setAuthState({
      isAuthenticated: false,
      user: null,
      loading: false,
      error: null,
    });
  };

  const resetDemoUser = () => {
    localStorage.removeItem('financeGPT_user');
    // This will trigger the useEffect to create a new demo user with fresh tokens
    window.location.reload();
  };

  const upgradeToProDemo = () => {
    if (authState.user) {
      const upgradedUser = {
        ...authState.user,
        subscription: {
          tier: 'pro' as const,
          status: 'active' as const,
          startDate: new Date(),
        },
        usage: {
          ...authState.user.usage,
          monthlyLimit: SUBSCRIPTION_TIERS.pro.limits.monthlyTokens,
          dailyLimit: SUBSCRIPTION_TIERS.pro.limits.dailyQueries,
          monthlyTokens: 500, // Used tokens (out of 10,000)
        },
      };

      setAuthState(prev => ({
        ...prev,
        user: upgradedUser,
      }));

      localStorage.setItem('financeGPT_user', JSON.stringify(upgradedUser));
    }
  };

  const startProTrial = () => {
    if (authState.user) {
      const trialUser = {
        ...authState.user,
        subscription: {
          tier: 'pro' as const,
          status: 'trial' as const,
          startDate: new Date(),
          trialEndDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days from now
        },
        usage: {
          ...authState.user.usage,
          monthlyLimit: SUBSCRIPTION_TIERS.pro.limits.monthlyTokens,
          dailyLimit: SUBSCRIPTION_TIERS.pro.limits.dailyQueries,
          monthlyTokens: 0, // Fresh trial tokens
        },
      };

      setAuthState(prev => ({
        ...prev,
        user: trialUser,
      }));

      localStorage.setItem('financeGPT_user', JSON.stringify(trialUser));
    }
  };

  const checkUsageLimit = (feature: string): boolean => {
    if (!authState.user) return false;
    
    const { subscription, usage } = authState.user;
    const tier = SUBSCRIPTION_TIERS[subscription.tier];
    
    // Check daily query limit
    if (tier.limits.dailyQueries !== -1 && usage.dailyQueries >= tier.limits.dailyQueries) {
      return false;
    }
    
    // Check monthly token limit
    if (usage.monthlyTokens >= tier.limits.monthlyTokens) {
      return false;
    }
    
    // Check feature-specific limits
    if (feature === 'imageAnalysis' && !tier.features.imageAnalysis) {
      return false;
    }
    
    return true;
  };

  const incrementUsage = (tokens: number) => {
    if (!authState.user) return;
    
    const updatedUser = {
      ...authState.user,
      usage: {
        ...authState.user.usage,
        dailyQueries: authState.user.usage.dailyQueries + 1,
        monthlyTokens: authState.user.usage.monthlyTokens + tokens,
        totalQueries: authState.user.usage.totalQueries + 1,
      },
    };
    
    localStorage.setItem('financeGPT_user', JSON.stringify(updatedUser));
    setAuthState(prev => ({ ...prev, user: updatedUser }));
  };

  const resetDailyUsage = () => {
    if (!authState.user) return;
    
    const today = new Date();
    const lastReset = new Date(authState.user.usage.lastResetDate);
    
    if (today.toDateString() !== lastReset.toDateString()) {
      const updatedUser = {
        ...authState.user,
        usage: {
          ...authState.user.usage,
          dailyQueries: 0,
          lastResetDate: today,
        },
      };
      
      localStorage.setItem('financeGPT_user', JSON.stringify(updatedUser));
      setAuthState(prev => ({ ...prev, user: updatedUser }));
    }
  };

  const value: AuthContextType = {
    ...authState,
    login,
    signup,
    logout,
    checkUsageLimit,
    incrementUsage,
    resetDailyUsage,
    resetDemoUser,
    upgradeToProDemo,
    startProTrial,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
