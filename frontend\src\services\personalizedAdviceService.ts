import { UserProfile } from '../components/UserProfileModal';

export interface PersonalizedAdvice {
  recommendation: string;
  reasoning: string[];
  riskAssessment: string;
  timelineRecommendation: string;
  positionSizing: string;
  alternativeOptions: string[];
  personalizedNotes: string[];
}

export interface StockInfo {
  ticker: string;
  company: string;
  price: string;
  sector: string;
  marketCap?: string;
  peRatio?: number;
  dividend?: number;
  volatility?: string;
}

export class PersonalizedAdviceService {
  
  static generatePersonalizedAdvice(
    stockInfo: StockInfo,
    analysisType: string,
    userProfile: UserProfile | null,
    query: string
  ): PersonalizedAdvice {

    if (!userProfile) {
      return this.generateGenericAdvice(stockInfo, analysisType);
    }

    // Get current market context
    const currentMarketContext = this.getCurrentMarketContext();

    const advice: PersonalizedAdvice = {
      recommendation: '',
      reasoning: [],
      riskAssessment: '',
      timelineRecommendation: '',
      positionSizing: '',
      alternativeOptions: [],
      personalizedNotes: []
    };

    // Generate recommendation based on user profile and current market conditions
    advice.recommendation = this.getPersonalizedRecommendation(stockInfo, userProfile, analysisType, currentMarketContext);
    advice.reasoning = this.getPersonalizedReasoning(stockInfo, userProfile, analysisType, currentMarketContext);
    advice.riskAssessment = this.getPersonalizedRiskAssessment(stockInfo, userProfile, currentMarketContext);
    advice.timelineRecommendation = this.getTimelineRecommendation(stockInfo, userProfile, currentMarketContext);
    advice.positionSizing = this.getPositionSizing(stockInfo, userProfile, currentMarketContext);
    advice.alternativeOptions = this.getAlternativeOptions(stockInfo, userProfile, currentMarketContext);
    advice.personalizedNotes = this.getPersonalizedNotes(stockInfo, userProfile, currentMarketContext);

    return advice;
  }

  private static getCurrentMarketContext() {
    const now = new Date();
    const hour = now.getHours();
    const day = now.getDay();
    const isMarketOpen = day >= 1 && day <= 5 && hour >= 9 && hour < 16;

    // Simulate current market conditions
    const marketSentiment = Math.random() > 0.5 ? 'bullish' : 'bearish';
    const volatility = Math.random() > 0.7 ? 'high' : Math.random() > 0.3 ? 'moderate' : 'low';

    return {
      isMarketOpen,
      marketSentiment,
      volatility,
      currentTime: now.toLocaleString(),
      economicConditions: 'stable', // Could be dynamic
      sectorTrends: {
        'Technology': Math.random() > 0.4 ? 'positive' : 'negative',
        'Healthcare': Math.random() > 0.5 ? 'positive' : 'negative',
        'Finance': Math.random() > 0.6 ? 'positive' : 'negative',
        'Energy': Math.random() > 0.3 ? 'positive' : 'negative'
      }
    };
  }

  private static getPersonalizedRecommendation(
    stockInfo: StockInfo,
    userProfile: UserProfile,
    analysisType: string,
    marketContext: any
  ): string {
    const { riskTolerance, investmentExperience, timeHorizon, preferredSectors = [] } = userProfile;

    // Check sector alignment and current sector trends
    const sectorMatch = Array.isArray(preferredSectors) && preferredSectors.includes(stockInfo.sector);
    const sectorTrend = marketContext.sectorTrends[stockInfo.sector] || 'neutral';

    // Adjust recommendation based on current market conditions
    let baseRecommendation = '';

    // Risk-based recommendations with market context
    if (riskTolerance === 'conservative') {
      if (stockInfo.sector === 'Utilities' || stockInfo.sector === 'Consumer Goods') {
        baseRecommendation = sectorMatch ? 'STRONG BUY' : 'BUY';
      } else if (stockInfo.sector === 'Technology' && stockInfo.ticker === 'AAPL') {
        baseRecommendation = 'MODERATE BUY';
      } else {
        baseRecommendation = 'HOLD';
      }
    } else if (riskTolerance === 'moderate') {
      if (sectorMatch) {
        baseRecommendation = 'BUY';
      } else if (stockInfo.sector === 'Technology') {
        baseRecommendation = 'MODERATE BUY';
      } else {
        baseRecommendation = 'HOLD';
      }
    } else { // aggressive
      if (stockInfo.sector === 'Technology' || stockInfo.ticker === 'TSLA') {
        baseRecommendation = 'STRONG BUY';
      } else {
        baseRecommendation = 'BUY';
      }
    }

    // Adjust for current market conditions
    if (marketContext.volatility === 'high' && riskTolerance === 'conservative') {
      if (baseRecommendation === 'STRONG BUY') baseRecommendation = 'BUY';
      if (baseRecommendation === 'BUY') baseRecommendation = 'MODERATE BUY';
    }

    if (sectorTrend === 'negative' && baseRecommendation.includes('BUY')) {
      baseRecommendation = 'HOLD - Wait for sector recovery';
    }

    if (sectorTrend === 'positive' && marketContext.marketSentiment === 'bullish') {
      if (baseRecommendation === 'HOLD') baseRecommendation = 'MODERATE BUY';
    }

    return baseRecommendation;
  }

  private static getPersonalizedReasoning(
    stockInfo: StockInfo,
    userProfile: UserProfile,
    analysisType: string,
    marketContext: any
  ): string[] {
    const reasoning: string[] = [];
    const { riskTolerance, investmentExperience, timeHorizon, preferredSectors = [], investmentGoals = [], age = 30 } = userProfile;

    // Current market context reasoning
    reasoning.push(`**Current Market Analysis (${marketContext.currentTime}):**`);
    reasoning.push(`Market is currently ${marketContext.isMarketOpen ? 'OPEN' : 'CLOSED'} with ${marketContext.marketSentiment} sentiment`);
    reasoning.push(`Market volatility is ${marketContext.volatility} - ${marketContext.volatility === 'high' ? 'exercise caution' : 'favorable for entry'}`);

    // Sector-specific current trends
    const sectorTrend = marketContext.sectorTrends[stockInfo.sector];
    if (sectorTrend) {
      reasoning.push(`${stockInfo.sector} sector is currently trending ${sectorTrend} in today's market`);
    }

    // Age-based reasoning with current market context
    if (age < 35) {
      reasoning.push(`At ${age} years old, you have a long investment horizon. Current market ${marketContext.volatility === 'high' ? 'volatility provides buying opportunities' : 'conditions are favorable for growth investments'}`);
    } else if (age > 50) {
      reasoning.push(`At ${age} years old, balance growth with preservation. Current ${marketContext.volatility} volatility ${marketContext.volatility === 'high' ? 'suggests cautious positioning' : 'allows for moderate risk-taking'}`);
    }

    // Risk tolerance reasoning with market conditions
    if (riskTolerance === 'conservative') {
      reasoning.push(`Your conservative risk profile is ${marketContext.volatility === 'high' ? 'well-suited for current volatile conditions' : 'appropriate, though current stable conditions allow for slight risk increase'}`);
      if (stockInfo.sector === 'Technology') {
        reasoning.push(`Technology stocks are ${marketContext.sectorTrends['Technology'] === 'positive' ? 'currently performing well but' : 'facing headwinds and'} may be more volatile than your risk tolerance suggests`);
      }
    } else if (riskTolerance === 'aggressive') {
      reasoning.push(`Your aggressive risk tolerance ${marketContext.volatility === 'high' ? 'can capitalize on current market volatility' : 'is well-positioned for current stable growth conditions'}`);
      if (stockInfo.sector === 'Technology') {
        reasoning.push(`Technology sector ${marketContext.sectorTrends['Technology'] === 'positive' ? 'momentum aligns perfectly' : 'challenges present opportunities'} with your growth-oriented approach`);
      }
    }

    // Sector preference reasoning with current trends
    if (Array.isArray(preferredSectors) && preferredSectors.includes(stockInfo.sector)) {
      reasoning.push(`${stockInfo.sector} sector matches your preferences and is currently ${sectorTrend || 'stable'} in today's market`);
    } else {
      reasoning.push(`Consider if ${stockInfo.sector} sector fits your strategy, especially given current ${sectorTrend || 'neutral'} sector performance`);
    }

    // Experience-based reasoning with market timing
    if (investmentExperience === 'beginner') {
      reasoning.push(`**🌱 Beginner-Friendly Analysis:**`);
      reasoning.push(`As a new investor, current ${marketContext.volatility} volatility ${marketContext.volatility === 'high' ? 'requires extra caution - stick to established companies like Apple or Microsoft' : 'provides good learning opportunities with stable, well-known companies'}`);

      if (stockInfo.sector === 'Technology' && ['AAPL', 'MSFT', 'GOOGL'].includes(stockInfo.ticker)) {
        reasoning.push(`${stockInfo.ticker} is a beginner-friendly tech stock - large, established company with predictable business model`);
      } else if (stockInfo.ticker === 'TSLA' || stockInfo.ticker === 'NVDA') {
        reasoning.push(`⚠️ ${stockInfo.ticker} is quite volatile for beginners - consider starting with more stable companies first`);
      }

      reasoning.push(`💡 **Beginner Tip**: Start with broad market ETFs (like VTI or SPY) before individual stocks for better diversification`);

    } else if (investmentExperience === 'intermediate') {
      reasoning.push(`With some investment experience, you can ${marketContext.volatility === 'high' ? 'handle moderate volatility but should still be cautious' : 'explore growth opportunities while maintaining diversification'}`);
    } else if (investmentExperience === 'advanced') {
      reasoning.push(`Your advanced experience allows you to ${marketContext.volatility === 'high' ? 'navigate current market volatility effectively' : 'take advantage of current stable conditions for strategic positioning'}`);
    }

    // Goal-based reasoning with current market timing
    if (Array.isArray(investmentGoals) && investmentGoals.includes('Retirement Planning')) {
      reasoning.push(`For retirement planning, current ${marketContext.marketSentiment} market sentiment ${marketContext.marketSentiment === 'bullish' ? 'supports long-term growth positioning' : 'suggests defensive positioning with quality companies'}`);
    }
    if (Array.isArray(investmentGoals) && investmentGoals.includes('Income Generation')) {
      reasoning.push(`For income generation, current ${marketContext.volatility} volatility ${marketContext.volatility === 'high' ? 'may affect dividend stability - focus on dividend aristocrats' : 'provides stable environment for dividend growth'}`);
    }

    return reasoning;
  }

  private static getPersonalizedRiskAssessment(
    stockInfo: StockInfo,
    userProfile: UserProfile,
    marketContext: any
  ): string {
    const { riskTolerance, currentPortfolioValue } = userProfile;

    let riskLevel = 'Medium';
    let assessment = '';

    // Base risk assessment
    if (stockInfo.sector === 'Technology') {
      riskLevel = riskTolerance === 'conservative' ? 'High' : 'Medium-High';
      assessment = 'Technology stocks can be volatile but offer growth potential. ';
    } else if (stockInfo.sector === 'Utilities') {
      riskLevel = 'Low';
      assessment = 'Utility stocks are generally stable with lower volatility. ';
    } else if (stockInfo.ticker === 'TSLA') {
      riskLevel = 'High';
      assessment = 'Tesla is a high-volatility stock with significant growth potential but substantial risk. ';
    }

    // Adjust for current market conditions
    if (marketContext.volatility === 'high') {
      assessment += `Current high market volatility increases overall risk. `;
      if (riskLevel === 'Medium') riskLevel = 'Medium-High';
      if (riskLevel === 'Low') riskLevel = 'Medium';
    } else if (marketContext.volatility === 'low') {
      assessment += `Current low market volatility reduces risk levels. `;
    }

    // Sector trend impact on risk
    const sectorTrend = marketContext.sectorTrends[stockInfo.sector];
    if (sectorTrend === 'negative') {
      assessment += `${stockInfo.sector} sector is currently facing headwinds, increasing near-term risk. `;
    } else if (sectorTrend === 'positive') {
      assessment += `${stockInfo.sector} sector momentum is positive, supporting the investment thesis. `;
    }

    // Market timing considerations
    if (!marketContext.isMarketOpen) {
      assessment += 'After-hours trading can increase volatility and risk. ';
    }

    // Adjust based on user's risk tolerance with market context
    if (riskTolerance === 'conservative' && riskLevel === 'High') {
      assessment += `This may exceed your conservative risk tolerance, especially in current ${marketContext.volatility} volatility environment.`;
    } else if (riskTolerance === 'aggressive' && riskLevel === 'Low') {
      assessment += `This conservative investment may not meet your growth objectives, even in current ${marketContext.volatility} volatility conditions.`;
    } else {
      assessment += `This aligns reasonably well with your risk profile given current market conditions.`;
    }

    return `**Current Risk Level**: ${riskLevel} (adjusted for market conditions)\n\n${assessment}`;
  }

  private static getTimelineRecommendation(
    stockInfo: StockInfo,
    userProfile: UserProfile,
    marketContext: any
  ): string {
    const { timeHorizon, age } = userProfile;

    let baseRecommendation = '';
    let marketTiming = '';

    // Base timeline recommendations
    if (timeHorizon === 'short') {
      baseRecommendation = 'For short-term investing (1-3 years), focus on stability and consider taking profits at 15-20% gains.';
      marketTiming = marketContext.volatility === 'high' ?
        ' Current high volatility suggests extra caution for short-term positions.' :
        ' Current market conditions are favorable for short-term strategies.';
    } else if (timeHorizon === 'medium') {
      baseRecommendation = 'For medium-term investing (3-10 years), you can weather some volatility. Consider dollar-cost averaging and rebalancing annually.';
      marketTiming = marketContext.volatility === 'high' ?
        ' Current volatility provides good dollar-cost averaging opportunities.' :
        ' Current stable conditions support steady accumulation strategies.';
    } else {
      baseRecommendation = 'For long-term investing (10+ years), focus on fundamental strength and growth potential.';
      marketTiming = marketContext.volatility === 'high' ?
        ' Current market volatility is less concerning for long-term investors and may provide entry opportunities.' :
        ' Current market stability supports long-term wealth building strategies.';
    }

    // Add sector-specific timing
    const sectorTrend = marketContext.sectorTrends[stockInfo.sector];
    let sectorTiming = '';
    if (sectorTrend === 'positive') {
      sectorTiming = ` ${stockInfo.sector} sector momentum supports your investment timeline.`;
    } else if (sectorTrend === 'negative') {
      sectorTiming = ` Consider ${stockInfo.sector} sector headwinds in your timing strategy.`;
    }

    return baseRecommendation + marketTiming + sectorTiming;
  }

  private static getPositionSizing(
    stockInfo: StockInfo,
    userProfile: UserProfile,
    marketContext: any
  ): string {
    const { monthlyInvestmentBudget = 500, currentPortfolioValue = 10000, riskTolerance, investmentExperience } = userProfile;

    let maxPosition = 0;

    // Base position sizing
    if (riskTolerance === 'conservative') {
      maxPosition = stockInfo.sector === 'Technology' ? 3 : 5;
    } else if (riskTolerance === 'moderate') {
      maxPosition = stockInfo.sector === 'Technology' ? 5 : 7;
    } else {
      maxPosition = stockInfo.sector === 'Technology' ? 8 : 10;
    }

    // Adjust for experience - beginners should start smaller
    if (investmentExperience === 'beginner') {
      maxPosition = Math.min(maxPosition, 3); // Even more conservative for beginners
      if (stockInfo.ticker === 'TSLA' || stockInfo.ticker === 'NVDA') {
        maxPosition = Math.min(maxPosition, 2); // Extra conservative for volatile stocks
      }
    } else if (investmentExperience === 'intermediate') {
      maxPosition = Math.min(maxPosition, 7);
    }

    // Adjust for current market conditions
    if (marketContext.volatility === 'high') {
      maxPosition = Math.max(2, maxPosition - 2); // Reduce position size in high volatility
    } else if (marketContext.volatility === 'low') {
      maxPosition = Math.min(12, maxPosition + 1); // Slightly increase in low volatility
    }

    // Sector trend adjustment
    const sectorTrend = marketContext.sectorTrends[stockInfo.sector];
    if (sectorTrend === 'negative') {
      maxPosition = Math.max(2, maxPosition - 1); // Reduce if sector is struggling
    }

    const suggestedAmount = Math.min(
      currentPortfolioValue * (maxPosition / 100),
      monthlyInvestmentBudget * 2
    );

    let timingAdvice = '';
    if (marketContext.volatility === 'high') {
      timingAdvice = ' Consider dollar-cost averaging over 3-4 months due to current market volatility.';
    } else {
      timingAdvice = ' Consider dollar-cost averaging over 2-3 months in current market conditions.';
    }

    return `**Suggested Position Size**: ${maxPosition}% of portfolio (approximately $${suggestedAmount.toLocaleString()})\n\nAdjusted for current market volatility (${marketContext.volatility}) and sector trends.${timingAdvice}`;
  }

  private static getAlternativeOptions(
    stockInfo: StockInfo,
    userProfile: UserProfile,
    marketContext: any
  ): string[] {
    const { preferredSectors = [], riskTolerance, investmentExperience } = userProfile;
    const alternatives: string[] = [];

    // Beginner-specific alternatives first
    if (investmentExperience === 'beginner') {
      alternatives.push('🌱 **For Beginners**: Start with VTI (Total Stock Market ETF) - instant diversification across 4,000+ stocks');
      alternatives.push('📚 **Learning Pick**: SPY (S&P 500 ETF) - tracks the 500 largest US companies, great for learning market movements');

      if (riskTolerance === 'conservative') {
        alternatives.push('🛡️ **Conservative Start**: SCHD (Dividend ETF) - focuses on stable, dividend-paying companies');
      } else {
        alternatives.push('🚀 **Growth Focus**: QQQ (NASDAQ ETF) - tech-heavy but more diversified than individual tech stocks');
      }

      return alternatives.slice(0, 3); // Return early for beginners with specific recommendations
    }

    // Current market context alternatives
    if (marketContext.volatility === 'high') {
      alternatives.push('Consider VTI (Total Market ETF) for stability during volatile periods');
      if (riskTolerance === 'conservative') {
        alternatives.push('Look at SCHD (Dividend ETF) for defensive positioning');
      }
    }

    if (stockInfo.sector === 'Technology') {
      const techTrend = marketContext.sectorTrends['Technology'];
      if (riskTolerance === 'conservative') {
        alternatives.push(`Consider VTI for broader diversification${techTrend === 'negative' ? ', especially given current tech headwinds' : ''}`);
        alternatives.push('Look at SCHD (Dividend ETF) for income and stability');
      } else {
        alternatives.push(`Consider QQQ (NASDAQ ETF)${techTrend === 'positive' ? ' to capitalize on tech momentum' : ' for diversified tech exposure despite headwinds'}`);
        alternatives.push('Look at individual tech leaders like MSFT or GOOGL');
      }
    }

    // Add sector-specific alternatives based on current trends
    if (Array.isArray(preferredSectors)) {
      preferredSectors.forEach(sector => {
        if (sector !== stockInfo.sector) {
          const sectorTrend = marketContext.sectorTrends[sector];
          if (sectorTrend === 'positive') {
            alternatives.push(`Consider ${sector} sector ETFs - currently trending positive`);
          } else {
            alternatives.push(`Consider ${sector} sector ETFs for diversification`);
          }
        }
      });
    }

    return alternatives.slice(0, 3); // Limit to 3 alternatives
  }

  private static getPersonalizedNotes(
    stockInfo: StockInfo,
    userProfile: UserProfile,
    marketContext: any
  ): string[] {
    const notes: string[] = [];
    const { investmentGoals = [], financialGoals = [], age = 30, monthlyInvestmentBudget = 500, investmentExperience } = userProfile;

    // Beginner-specific notes first
    if (investmentExperience === 'beginner') {
      notes.push('🌱 **New Investor Tips**: Remember - investing is a marathon, not a sprint. Focus on learning and building good habits');

      if (stockInfo.ticker === 'AAPL' || stockInfo.ticker === 'MSFT') {
        notes.push('✅ **Beginner-Friendly Choice**: This is a good stock for beginners - stable, well-established company with predictable business');
      } else if (stockInfo.ticker === 'TSLA' || stockInfo.ticker === 'NVDA') {
        notes.push('⚠️ **High Risk for Beginners**: This stock is quite volatile. Consider starting with more stable companies or ETFs first');
      }

      notes.push('💡 **Learning Opportunity**: Research the company\'s business model, read their annual reports, and understand what drives their revenue');

      if (monthlyInvestmentBudget < 500) {
        notes.push('🎯 **Small Budget Strategy**: Perfect for learning! Start small, invest regularly, and increase as you gain confidence and knowledge');
      }
    }

    // Current market timing notes
    notes.push(`⏰ **Market Timing**: Analysis as of ${marketContext.currentTime} - Market is ${marketContext.isMarketOpen ? 'OPEN' : 'CLOSED'}`);

    if (marketContext.volatility === 'high') {
      const beginnerVolatilityNote = investmentExperience === 'beginner' ?
        ' As a beginner, use this as a learning opportunity but invest smaller amounts' :
        ' and potential opportunities for patient investors';
      notes.push(`⚠️ **High Volatility Alert**: Current market conditions suggest extra caution${beginnerVolatilityNote}`);
    }

    // Goal-specific notes with market context
    if (Array.isArray(investmentGoals) && investmentGoals.includes('Retirement Planning')) {
      notes.push(`💡 **Retirement Strategy**: Consider tax-advantaged accounts (401k, IRA)${marketContext.volatility === 'high' ? ' and dollar-cost averaging during volatile periods' : ' for this investment'}`);
    }

    if (Array.isArray(investmentGoals) && investmentGoals.includes('Income Generation')) {
      if (stockInfo.ticker === 'AAPL' || stockInfo.ticker === 'MSFT') {
        notes.push(`💰 **Dividend Income**: This stock pays dividends${marketContext.volatility === 'high' ? ' - monitor for dividend sustainability during volatile periods' : ', supporting your income generation goal'}`);
      } else {
        notes.push('⚠️ **Income Note**: This stock may not provide significant dividend income - consider dividend-focused alternatives');
      }
    }

    // Budget-specific notes with market timing
    if (monthlyInvestmentBudget < 500) {
      notes.push(`💡 **Budget Strategy**: Consider fractional shares${marketContext.volatility === 'high' ? ' and extended dollar-cost averaging to smooth out volatility' : ' to build positions gradually with your budget'}`);
    }

    // Age-specific notes with current market context
    if (age < 30) {
      notes.push(`🚀 **Young Investor Advantage**: Your age allows for aggressive growth strategies${marketContext.volatility === 'high' ? ' - current volatility may provide long-term opportunities' : ' in current market conditions'}`);
    } else if (age > 55) {
      notes.push(`🛡️ **Pre-Retirement Strategy**: Consider gradually shifting toward more conservative investments${marketContext.volatility === 'high' ? ', especially given current market volatility' : ' as you approach retirement'}`);
    }

    // Sector-specific market notes
    const sectorTrend = marketContext.sectorTrends[stockInfo.sector];
    if (sectorTrend === 'positive') {
      notes.push(`📈 **Sector Momentum**: ${stockInfo.sector} sector is currently outperforming - consider timing your entry`);
    } else if (sectorTrend === 'negative') {
      notes.push(`📉 **Sector Headwinds**: ${stockInfo.sector} sector facing challenges - consider waiting for better entry point`);
    }

    return notes.slice(0, 6); // Limit to 6 notes to avoid overwhelming
  }

  private static generateGenericAdvice(stockInfo: StockInfo, analysisType: string): PersonalizedAdvice {
    return {
      recommendation: 'HOLD',
      reasoning: ['Generic analysis without user profile', 'Consider creating a profile for personalized advice'],
      riskAssessment: 'Medium risk - Unable to assess personal risk tolerance',
      timelineRecommendation: 'Medium-term hold recommended',
      positionSizing: 'Consider 3-5% of portfolio maximum',
      alternativeOptions: ['Diversified ETFs', 'Index funds', 'Sector-specific ETFs'],
      personalizedNotes: ['Create a user profile for personalized investment advice']
    };
  }
}

// Comprehensive stock database with real company names and realistic data
export const stockDatabase: { [key: string]: StockInfo } = {
  // Major Tech Stocks
  'aapl': { ticker: 'AAPL', company: 'Apple Inc.', price: '$175.43', sector: 'Technology', marketCap: '$2.89T', peRatio: 28.5, dividend: 0.5 },
  'apple': { ticker: 'AAPL', company: 'Apple Inc.', price: '$175.43', sector: 'Technology', marketCap: '$2.89T', peRatio: 28.5, dividend: 0.5 },
  'msft': { ticker: 'MSFT', company: 'Microsoft Corporation', price: '$378.85', sector: 'Technology', marketCap: '$2.81T', peRatio: 32.1, dividend: 0.7 },
  'microsoft': { ticker: 'MSFT', company: 'Microsoft Corporation', price: '$378.85', sector: 'Technology', marketCap: '$2.81T', peRatio: 32.1, dividend: 0.7 },
  'googl': { ticker: 'GOOGL', company: 'Alphabet Inc.', price: '$138.45', sector: 'Technology', marketCap: '$1.75T', peRatio: 24.8 },
  'google': { ticker: 'GOOGL', company: 'Alphabet Inc.', price: '$138.45', sector: 'Technology', marketCap: '$1.75T', peRatio: 24.8 },
  'amzn': { ticker: 'AMZN', company: 'Amazon.com Inc.', price: '$155.89', sector: 'E-commerce', marketCap: '$1.62T', peRatio: 45.3 },
  'amazon': { ticker: 'AMZN', company: 'Amazon.com Inc.', price: '$155.89', sector: 'E-commerce', marketCap: '$1.62T', peRatio: 45.3 },
  'nvda': { ticker: 'NVDA', company: 'NVIDIA Corporation', price: '$875.28', sector: 'Technology', marketCap: '$2.15T', peRatio: 71.8, volatility: 'High' },
  'nvidia': { ticker: 'NVDA', company: 'NVIDIA Corporation', price: '$875.28', sector: 'Technology', marketCap: '$2.15T', peRatio: 71.8, volatility: 'High' },
  'meta': { ticker: 'META', company: 'Meta Platforms Inc.', price: '$485.22', sector: 'Technology', marketCap: '$1.23T', peRatio: 25.9 },
  'facebook': { ticker: 'META', company: 'Meta Platforms Inc.', price: '$485.22', sector: 'Technology', marketCap: '$1.23T', peRatio: 25.9 },

  // Electric Vehicles & Automotive
  'tsla': { ticker: 'TSLA', company: 'Tesla Inc.', price: '$248.87', sector: 'Automotive', marketCap: '$789B', peRatio: 65.2, volatility: 'High' },
  'tesla': { ticker: 'TSLA', company: 'Tesla Inc.', price: '$248.87', sector: 'Automotive', marketCap: '$789B', peRatio: 65.2, volatility: 'High' },

  // Quantum Computing & Emerging Tech
  'qbts': { ticker: 'QBTS', company: 'D-Wave Quantum Inc.', price: '$1.23', sector: 'Quantum Computing', marketCap: '$89.5M', peRatio: null, volatility: 'Very High' },
  'ionq': { ticker: 'IONQ', company: 'IonQ Inc.', price: '$8.45', sector: 'Quantum Computing', marketCap: '$1.8B', peRatio: null, volatility: 'Very High' },
  'rigetti': { ticker: 'RGTI', company: 'Rigetti Computing Inc.', price: '$2.67', sector: 'Quantum Computing', marketCap: '$245M', peRatio: null, volatility: 'Very High' },

  // Entertainment & Streaming
  'nflx': { ticker: 'NFLX', company: 'Netflix Inc.', price: '$612.34', sector: 'Entertainment', marketCap: '$271B', peRatio: 34.2 },
  'netflix': { ticker: 'NFLX', company: 'Netflix Inc.', price: '$612.34', sector: 'Entertainment', marketCap: '$271B', peRatio: 34.2 },
  'dis': { ticker: 'DIS', company: 'The Walt Disney Company', price: '$89.23', sector: 'Entertainment', marketCap: '$163B', peRatio: 18.5, dividend: 0.88 },
  'disney': { ticker: 'DIS', company: 'The Walt Disney Company', price: '$89.23', sector: 'Entertainment', marketCap: '$163B', peRatio: 18.5, dividend: 0.88 },

  // Financial Services
  'jpm': { ticker: 'JPM', company: 'JPMorgan Chase & Co.', price: '$178.45', sector: 'Financial Services', marketCap: '$523B', peRatio: 12.3, dividend: 4.0 },
  'bac': { ticker: 'BAC', company: 'Bank of America Corporation', price: '$34.56', sector: 'Financial Services', marketCap: '$278B', peRatio: 11.8, dividend: 3.2 },

  // Healthcare & Biotech
  'jnj': { ticker: 'JNJ', company: 'Johnson & Johnson', price: '$156.78', sector: 'Healthcare', marketCap: '$412B', peRatio: 15.6, dividend: 2.9 },
  'pfizer': { ticker: 'PFE', company: 'Pfizer Inc.', price: '$28.90', sector: 'Healthcare', marketCap: '$162B', peRatio: 13.4, dividend: 5.8 },
  'pfe': { ticker: 'PFE', company: 'Pfizer Inc.', price: '$28.90', sector: 'Healthcare', marketCap: '$162B', peRatio: 13.4, dividend: 5.8 },

  // Energy
  'xom': { ticker: 'XOM', company: 'Exxon Mobil Corporation', price: '$112.34', sector: 'Energy', marketCap: '$467B', peRatio: 14.2, dividend: 5.9 },
  'cvx': { ticker: 'CVX', company: 'Chevron Corporation', price: '$145.67', sector: 'Energy', marketCap: '$278B', peRatio: 13.8, dividend: 3.4 },

  // Retail & Consumer
  'wmt': { ticker: 'WMT', company: 'Walmart Inc.', price: '$67.89', sector: 'Retail', marketCap: '$548B', peRatio: 26.7, dividend: 2.2 },
  'walmart': { ticker: 'WMT', company: 'Walmart Inc.', price: '$67.89', sector: 'Retail', marketCap: '$548B', peRatio: 26.7, dividend: 2.2 },

  // Aerospace & Defense
  'ba': { ticker: 'BA', company: 'The Boeing Company', price: '$189.45', sector: 'Aerospace', marketCap: '$112B', peRatio: null, volatility: 'High' },
  'boeing': { ticker: 'BA', company: 'The Boeing Company', price: '$189.45', sector: 'Aerospace', marketCap: '$112B', peRatio: null, volatility: 'High' },

  // Semiconductors
  'amd': { ticker: 'AMD', company: 'Advanced Micro Devices Inc.', price: '$134.56', sector: 'Semiconductors', marketCap: '$217B', peRatio: 45.8 },
  'intc': { ticker: 'INTC', company: 'Intel Corporation', price: '$23.45', sector: 'Semiconductors', marketCap: '$98B', peRatio: 18.9, dividend: 4.8 },
  'intel': { ticker: 'INTC', company: 'Intel Corporation', price: '$23.45', sector: 'Semiconductors', marketCap: '$98B', peRatio: 18.9, dividend: 4.8 },

  // Airlines
  'luv': { ticker: 'LUV', company: 'Southwest Airlines Co.', price: '$28.45', sector: 'Airlines', marketCap: '$16.8B', peRatio: 12.4, dividend: 0.72 },
  'southwest': { ticker: 'LUV', company: 'Southwest Airlines Co.', price: '$28.45', sector: 'Airlines', marketCap: '$16.8B', peRatio: 12.4, dividend: 0.72 },
  'dal': { ticker: 'DAL', company: 'Delta Air Lines Inc.', price: '$42.67', sector: 'Airlines', marketCap: '$27.3B', peRatio: 8.9, dividend: 0.15 },
  'delta': { ticker: 'DAL', company: 'Delta Air Lines Inc.', price: '$42.67', sector: 'Airlines', marketCap: '$27.3B', peRatio: 8.9, dividend: 0.15 },
  'aal': { ticker: 'AAL', company: 'American Airlines Group Inc.', price: '$14.23', sector: 'Airlines', marketCap: '$9.2B', peRatio: null, volatility: 'High' },
  'american': { ticker: 'AAL', company: 'American Airlines Group Inc.', price: '$14.23', sector: 'Airlines', marketCap: '$9.2B', peRatio: null, volatility: 'High' },
  'ual': { ticker: 'UAL', company: 'United Airlines Holdings Inc.', price: '$45.89', sector: 'Airlines', marketCap: '$14.9B', peRatio: 6.8 },
  'united': { ticker: 'UAL', company: 'United Airlines Holdings Inc.', price: '$45.89', sector: 'Airlines', marketCap: '$14.9B', peRatio: 6.8 },

  // Telecommunications
  'vz': { ticker: 'VZ', company: 'Verizon Communications Inc.', price: '$40.12', sector: 'Telecommunications', marketCap: '$168B', peRatio: 8.7, dividend: 6.4 },
  'verizon': { ticker: 'VZ', company: 'Verizon Communications Inc.', price: '$40.12', sector: 'Telecommunications', marketCap: '$168B', peRatio: 8.7, dividend: 6.4 },
  't': { ticker: 'T', company: 'AT&T Inc.', price: '$19.45', sector: 'Telecommunications', marketCap: '$139B', peRatio: 7.2, dividend: 7.8 },
  'att': { ticker: 'T', company: 'AT&T Inc.', price: '$19.45', sector: 'Telecommunications', marketCap: '$139B', peRatio: 7.2, dividend: 7.8 },

  // Food & Beverage
  'ko': { ticker: 'KO', company: 'The Coca-Cola Company', price: '$59.78', sector: 'Consumer Staples', marketCap: '$258B', peRatio: 24.1, dividend: 3.1 },
  'coca': { ticker: 'KO', company: 'The Coca-Cola Company', price: '$59.78', sector: 'Consumer Staples', marketCap: '$258B', peRatio: 24.1, dividend: 3.1 },
  'pep': { ticker: 'PEP', company: 'PepsiCo Inc.', price: '$167.89', sector: 'Consumer Staples', marketCap: '$231B', peRatio: 25.3, dividend: 2.7 },
  'pepsi': { ticker: 'PEP', company: 'PepsiCo Inc.', price: '$167.89', sector: 'Consumer Staples', marketCap: '$231B', peRatio: 25.3, dividend: 2.7 },

  // Real Estate
  'amt': { ticker: 'AMT', company: 'American Tower Corporation', price: '$189.34', sector: 'Real Estate', marketCap: '$87B', peRatio: 28.9, dividend: 3.2 },
  'pld': { ticker: 'PLD', company: 'Prologis Inc.', price: '$112.45', sector: 'Real Estate', marketCap: '$104B', peRatio: 22.1, dividend: 3.1 },

  // Utilities
  'nee': { ticker: 'NEE', company: 'NextEra Energy Inc.', price: '$76.23', sector: 'Utilities', marketCap: '$154B', peRatio: 19.8, dividend: 3.0 },
  'so': { ticker: 'SO', company: 'The Southern Company', price: '$68.45', sector: 'Utilities', marketCap: '$72B', peRatio: 18.2, dividend: 4.1 }
};
