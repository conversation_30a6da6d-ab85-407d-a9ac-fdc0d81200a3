import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { 
  User,
  DollarSign,
  Target,
  Calendar,
  TrendingUp,
  Shield,
  X,
  Save,
  Edit
} from 'lucide-react';

export interface UserProfile {
  id: string;
  name: string;
  age: number;
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  investmentGoals: string[];
  timeHorizon: 'short' | 'medium' | 'long';
  investmentExperience: 'beginner' | 'intermediate' | 'advanced';
  monthlyInvestmentBudget: number;
  currentPortfolioValue: number;
  preferredSectors: string[];
  financialGoals: string;
  created: string;
  updated: string;
}

interface UserProfileModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (profile: UserProfile) => void;
  currentProfile?: UserProfile | null;
}

export const UserProfileModal: React.FC<UserProfileModalProps> = ({
  isOpen,
  onClose,
  onSave,
  currentProfile
}) => {
  const [profile, setProfile] = useState<Partial<UserProfile>>({
    name: '',
    age: 30,
    riskTolerance: 'moderate',
    investmentGoals: [],
    timeHorizon: 'medium',
    investmentExperience: 'intermediate',
    monthlyInvestmentBudget: 1000,
    currentPortfolioValue: 10000,
    preferredSectors: [],
    financialGoals: ''
  });

  useEffect(() => {
    if (currentProfile) {
      setProfile(currentProfile);
    }
  }, [currentProfile]);

  const investmentGoalOptions = [
    'Retirement Planning',
    'Wealth Building',
    'Income Generation',
    'Capital Preservation',
    'Education Funding',
    'Emergency Fund',
    'Home Purchase',
    'Travel & Lifestyle'
  ];

  const sectorOptions = [
    'Technology',
    'Healthcare',
    'Finance',
    'Energy',
    'Consumer Goods',
    'Real Estate',
    'Utilities',
    'Materials',
    'Industrials',
    'Telecommunications'
  ];

  const handleGoalToggle = (goal: string) => {
    const currentGoals = profile.investmentGoals || [];
    if (currentGoals.includes(goal)) {
      setProfile({
        ...profile,
        investmentGoals: currentGoals.filter(g => g !== goal)
      });
    } else {
      setProfile({
        ...profile,
        investmentGoals: [...currentGoals, goal]
      });
    }
  };

  const handleSectorToggle = (sector: string) => {
    const currentSectors = profile.preferredSectors || [];
    if (currentSectors.includes(sector)) {
      setProfile({
        ...profile,
        preferredSectors: currentSectors.filter(s => s !== sector)
      });
    } else {
      setProfile({
        ...profile,
        preferredSectors: [...currentSectors, sector]
      });
    }
  };

  const handleSave = () => {
    const completeProfile: UserProfile = {
      id: currentProfile?.id || `profile_${Date.now()}`,
      name: profile.name || 'Anonymous User',
      age: profile.age || 30,
      riskTolerance: profile.riskTolerance || 'moderate',
      investmentGoals: profile.investmentGoals || [],
      timeHorizon: profile.timeHorizon || 'medium',
      investmentExperience: profile.investmentExperience || 'intermediate',
      monthlyInvestmentBudget: profile.monthlyInvestmentBudget || 1000,
      currentPortfolioValue: profile.currentPortfolioValue || 10000,
      preferredSectors: profile.preferredSectors || [],
      financialGoals: profile.financialGoals || '',
      created: currentProfile?.created || new Date().toISOString(),
      updated: new Date().toISOString()
    };

    onSave(completeProfile);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[60] flex items-center justify-center p-4 pt-20">
      <Card className="w-full max-w-4xl max-h-[85vh] overflow-y-auto bg-gray-900 border-gray-700">
        <div className="p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-600 rounded-lg">
                <User className="h-6 w-6 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold text-white">Investment Profile</h2>
                <p className="text-gray-300">Personalize your financial advice</p>
              </div>
            </div>
            <Button
              onClick={onClose}
              variant="outline"
              size="sm"
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Basic Information */}
            <Card className="p-4 bg-white/5 border-white/10">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <User className="h-5 w-5 mr-2 text-blue-400" />
                Basic Information
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Name</label>
                  <Input
                    value={profile.name}
                    onChange={(e) => setProfile({ ...profile, name: e.target.value })}
                    placeholder="Your name"
                    className="bg-white/10 border-white/20 text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Age</label>
                  <Input
                    type="number"
                    value={profile.age}
                    onChange={(e) => setProfile({ ...profile, age: parseInt(e.target.value) || 30 })}
                    className="bg-white/10 border-white/20 text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Investment Experience</label>
                  <select
                    value={profile.investmentExperience}
                    onChange={(e) => setProfile({ ...profile, investmentExperience: e.target.value as any })}
                    className="w-full p-2 bg-gray-800 border border-white/20 rounded-md text-white [&>option]:bg-gray-800 [&>option]:text-white"
                  >
                    <option value="beginner">Beginner (0-2 years)</option>
                    <option value="intermediate">Intermediate (2-5 years)</option>
                    <option value="advanced">Advanced (5+ years)</option>
                  </select>
                </div>
              </div>
            </Card>

            {/* Risk & Goals */}
            <Card className="p-4 bg-white/5 border-white/10">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <Target className="h-5 w-5 mr-2 text-green-400" />
                Risk & Goals
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Risk Tolerance</label>
                  <select
                    value={profile.riskTolerance}
                    onChange={(e) => setProfile({ ...profile, riskTolerance: e.target.value as any })}
                    className="w-full p-2 bg-gray-800 border border-white/20 rounded-md text-white [&>option]:bg-gray-800 [&>option]:text-white"
                  >
                    <option value="conservative">Conservative - Preserve capital</option>
                    <option value="moderate">Moderate - Balanced growth</option>
                    <option value="aggressive">Aggressive - Maximum growth</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Time Horizon</label>
                  <select
                    value={profile.timeHorizon}
                    onChange={(e) => setProfile({ ...profile, timeHorizon: e.target.value as any })}
                    className="w-full p-2 bg-gray-800 border border-white/20 rounded-md text-white [&>option]:bg-gray-800 [&>option]:text-white"
                  >
                    <option value="short">Short-term (1-3 years)</option>
                    <option value="medium">Medium-term (3-10 years)</option>
                    <option value="long">Long-term (10+ years)</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Financial Goals</label>
                  <textarea
                    value={profile.financialGoals}
                    onChange={(e) => setProfile({ ...profile, financialGoals: e.target.value })}
                    placeholder="Describe your financial goals..."
                    className="w-full p-2 bg-white/10 border border-white/20 rounded-md text-white h-20 resize-none"
                  />
                </div>
              </div>
            </Card>

            {/* Financial Information */}
            <Card className="p-4 bg-white/5 border-white/10">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <DollarSign className="h-5 w-5 mr-2 text-yellow-400" />
                Financial Information
              </h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Monthly Investment Budget</label>
                  <Input
                    type="number"
                    value={profile.monthlyInvestmentBudget}
                    onChange={(e) => setProfile({ ...profile, monthlyInvestmentBudget: parseInt(e.target.value) || 0 })}
                    className="bg-white/10 border-white/20 text-white"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">Current Portfolio Value</label>
                  <Input
                    type="number"
                    value={profile.currentPortfolioValue}
                    onChange={(e) => setProfile({ ...profile, currentPortfolioValue: parseInt(e.target.value) || 0 })}
                    className="bg-white/10 border-white/20 text-white"
                  />
                </div>
              </div>
            </Card>

            {/* Investment Goals */}
            <Card className="p-4 bg-white/5 border-white/10">
              <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-purple-400" />
                Investment Goals
              </h3>
              
              <div className="flex flex-wrap gap-2">
                {investmentGoalOptions.map((goal) => (
                  <Badge
                    key={goal}
                    onClick={() => handleGoalToggle(goal)}
                    className={`cursor-pointer transition-colors ${
                      (profile.investmentGoals || []).includes(goal)
                        ? 'bg-blue-600 text-white'
                        : 'bg-white/10 text-gray-300 hover:bg-white/20'
                    }`}
                  >
                    {goal}
                  </Badge>
                ))}
              </div>
            </Card>
          </div>

          {/* Preferred Sectors */}
          <Card className="p-4 bg-white/5 border-white/10 mt-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              <Shield className="h-5 w-5 mr-2 text-orange-400" />
              Preferred Sectors
            </h3>
            
            <div className="flex flex-wrap gap-2">
              {sectorOptions.map((sector) => (
                <Badge
                  key={sector}
                  onClick={() => handleSectorToggle(sector)}
                  className={`cursor-pointer transition-colors ${
                    (profile.preferredSectors || []).includes(sector)
                      ? 'bg-green-600 text-white'
                      : 'bg-white/10 text-gray-300 hover:bg-white/20'
                  }`}
                >
                  {sector}
                </Badge>
              ))}
            </div>
          </Card>

          {/* Actions */}
          <div className="flex justify-end space-x-3 mt-6">
            <Button
              onClick={onClose}
              variant="outline"
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button
              onClick={handleSave}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              <Save className="h-4 w-4 mr-2" />
              Save Profile
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};
