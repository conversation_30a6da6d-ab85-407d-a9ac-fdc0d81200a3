import React, { useState, useEffect, useRef } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { AnalysisConclusion } from './AnalysisConclusion';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3, 
  PieChart, 
  Activity,
  Target,
  AlertTriangle,
  CheckCircle,
  Info,
  DollarSign,
  Percent,
  Calendar,
  Eye,
  Download,
  Share2
} from 'lucide-react';

interface AnalysisData {
  ticker: string;
  currentPrice: number;
  priceChange: number;
  priceChangePercent: number;
  volume: number;
  marketCap: string;
  peRatio: number;
  dividend: number;
  high52Week: number;
  low52Week: number;
  recommendation: 'BUY' | 'SELL' | 'HOLD';
  confidence: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH';
  targetPrice: number;
  analysisType: string;
  timestamp: string;
}

interface EnhancedAnalysisResultsProps {
  data: AnalysisData;
  rawAnalysis: string;
  onClose?: () => void;
  userTier: 'Basic' | 'Pro';
}

export const EnhancedAnalysisResults: React.FC<EnhancedAnalysisResultsProps> = ({
  data,
  rawAnalysis,
  onClose,
  userTier
}) => {
  const [activeTab, setActiveTab] = useState<'overview' | 'technical' | 'fundamental' | 'raw'>('overview');
  const [isVisible, setIsVisible] = useState(false);
  const resultsRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Smooth entrance animation
    setTimeout(() => setIsVisible(true), 100);
    
    // Enhanced auto-scroll
    const scrollToResults = () => {
      resultsRef.current?.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
        inline: 'nearest'
      });
    };

    scrollToResults();
    setTimeout(scrollToResults, 300);
    setTimeout(scrollToResults, 600);
  }, []);

  const getRecommendationColor = (rec: string) => {
    switch (rec) {
      case 'BUY': return 'bg-green-600 text-white';
      case 'SELL': return 'bg-red-600 text-white';
      case 'HOLD': return 'bg-yellow-600 text-white';
      default: return 'bg-gray-600 text-white';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'LOW': return 'text-green-400';
      case 'MEDIUM': return 'text-yellow-400';
      case 'HIGH': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const formatVolume = (volume: number) => {
    if (volume >= 1000000) {
      return `${(volume / 1000000).toFixed(1)}M`;
    } else if (volume >= 1000) {
      return `${(volume / 1000).toFixed(1)}K`;
    }
    return volume.toString();
  };

  // Simple SVG chart component
  const MiniChart = () => {
    const points = [
      { x: 0, y: 60 },
      { x: 50, y: 45 },
      { x: 100, y: 30 },
      { x: 150, y: 40 },
      { x: 200, y: 20 },
      { x: 250, y: 35 },
      { x: 300, y: 25 }
    ];

    const pathData = points.map((point, index) => 
      `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
    ).join(' ');

    return (
      <div className="w-full h-20 bg-white/5 rounded-lg p-2">
        <svg width="100%" height="100%" viewBox="0 0 300 80">
          <defs>
            <linearGradient id="chartGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="#10B981" stopOpacity="0.3"/>
              <stop offset="100%" stopColor="#10B981" stopOpacity="0"/>
            </linearGradient>
          </defs>
          <path
            d={`${pathData} L 300 80 L 0 80 Z`}
            fill="url(#chartGradient)"
          />
          <path
            d={pathData}
            stroke="#10B981"
            strokeWidth="2"
            fill="none"
          />
          {points.map((point, index) => (
            <circle
              key={index}
              cx={point.x}
              cy={point.y}
              r="3"
              fill="#10B981"
              className="opacity-0 hover:opacity-100 transition-opacity"
            />
          ))}
        </svg>
      </div>
    );
  };

  return (
    <div 
      ref={resultsRef}
      className={`transition-all duration-700 transform ${
        isVisible ? 'translate-y-0 opacity-100' : 'translate-y-8 opacity-0'
      }`}
    >
      <Card className="p-6 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-sm border-gray-700/50 shadow-2xl">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <div className="h-3 w-3 bg-green-500 rounded-full animate-pulse"></div>
              <h2 className="text-2xl font-bold text-white">{data.ticker} Analysis</h2>
            </div>
            <Badge className={getRecommendationColor(data.recommendation)}>
              {data.recommendation}
            </Badge>
            <Badge className="bg-blue-600 text-white">
              {data.confidence}% Confidence
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm" className="text-gray-300">
              <Share2 className="h-4 w-4 mr-1" />
              Share
            </Button>
            <Button variant="outline" size="sm" className="text-gray-300">
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Current Price</span>
              <DollarSign className="h-4 w-4 text-green-400" />
            </div>
            <div className="text-xl font-bold text-white">{formatCurrency(data.currentPrice)}</div>
            <div className={`text-sm ${data.priceChange >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPercent(data.priceChangePercent)}
            </div>
          </div>

          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Target Price</span>
              <Target className="h-4 w-4 text-blue-400" />
            </div>
            <div className="text-xl font-bold text-white">{formatCurrency(data.targetPrice)}</div>
            <div className="text-sm text-blue-400">
              {formatPercent(((data.targetPrice - data.currentPrice) / data.currentPrice) * 100)} upside
            </div>
          </div>

          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Risk Level</span>
              <AlertTriangle className="h-4 w-4 text-yellow-400" />
            </div>
            <div className={`text-xl font-bold ${getRiskColor(data.riskLevel)}`}>
              {data.riskLevel}
            </div>
            <div className="text-sm text-gray-400">Assessment</div>
          </div>

          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Volume</span>
              <Activity className="h-4 w-4 text-purple-400" />
            </div>
            <div className="text-xl font-bold text-white">{formatVolume(data.volume)}</div>
            <div className="text-sm text-gray-400">Today</div>
          </div>
        </div>

        {/* Chart */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
            <BarChart3 className="h-5 w-5 mr-2 text-green-400" />
            Price Trend
          </h3>
          <MiniChart />
        </div>

        {/* Analysis Tabs */}
        <div className="mb-4">
          <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
            {[
              { id: 'overview', label: 'Overview', icon: Eye },
              { id: 'technical', label: 'Technical', icon: BarChart3 },
              { id: 'fundamental', label: 'Fundamental', icon: PieChart },
              { id: 'raw', label: 'Full Analysis', icon: Info }
            ].map((tab) => {
              const IconComponent = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id as any)}
                  className={`flex-1 flex items-center justify-center space-x-2 py-2 px-4 rounded-md transition-all ${
                    activeTab === tab.id
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-400 hover:text-white hover:bg-white/10'
                  }`}
                >
                  <IconComponent className="h-4 w-4" />
                  <span className="text-sm font-medium">{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>

        {/* Tab Content */}
        <div className="bg-white/5 rounded-lg p-4 min-h-[200px]">
          {activeTab === 'overview' && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h4 className="font-semibold text-white mb-2">Key Statistics</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Market Cap:</span>
                      <span className="text-white">{data.marketCap}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">P/E Ratio:</span>
                      <span className="text-white">{data.peRatio}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Dividend:</span>
                      <span className="text-white">{data.dividend}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">52W High:</span>
                      <span className="text-white">{formatCurrency(data.high52Week)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">52W Low:</span>
                      <span className="text-white">{formatCurrency(data.low52Week)}</span>
                    </div>
                  </div>
                </div>
                <div>
                  <h4 className="font-semibold text-white mb-2">Analysis Summary</h4>
                  <div className="text-sm text-gray-300 leading-relaxed">
                    Based on our comprehensive analysis, {data.ticker} shows {data.recommendation.toLowerCase()} signals 
                    with {data.confidence}% confidence. The stock is currently trading at {formatCurrency(data.currentPrice)} 
                    with a target price of {formatCurrency(data.targetPrice)}, indicating potential 
                    {formatPercent(((data.targetPrice - data.currentPrice) / data.currentPrice) * 100)} movement.
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'raw' && (
            <div className="prose max-w-none">
              <pre className="whitespace-pre-wrap text-sm text-gray-100 leading-relaxed">
                {rawAnalysis}
              </pre>
            </div>
          )}

          {(activeTab === 'technical' || activeTab === 'fundamental') && (
            <div className="text-center py-8">
              <div className="text-gray-400 mb-2">
                {userTier === 'Basic' ? '🔒 Pro Feature' : '🚧 Coming Soon'}
              </div>
              <p className="text-sm text-gray-500">
                {userTier === 'Basic' 
                  ? 'Upgrade to Pro for detailed technical and fundamental analysis'
                  : 'Advanced analysis features are being developed'
                }
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="mt-6 pt-4 border-t border-white/10">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center space-x-4">
              <span>Analysis Type: {data.analysisType}</span>
              <span>•</span>
              <span>Generated: {new Date(data.timestamp).toLocaleString()}</span>
            </div>
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-3 w-3" />
              <span>Not financial advice</span>
            </div>
          </div>
        </div>
      </Card>

      {/* Analysis Conclusion */}
      <div className="mt-6">
        <AnalysisConclusion
          ticker={data.ticker}
          currentPrice={data.currentPrice}
          targetPrice={data.targetPrice}
          recommendation={data.recommendation}
          confidence={data.confidence}
          riskLevel={data.riskLevel}
          analysisType={data.analysisType}
          keyFactors={[
            'Strong technical momentum indicators',
            'Favorable market sentiment',
            'Solid fundamental metrics',
            'Positive analyst coverage'
          ]}
          risks={[
            'Market volatility exposure',
            'Sector-specific headwinds',
            'Regulatory uncertainty',
            'Economic cycle sensitivity'
          ]}
          catalysts={[
            'Upcoming earnings announcement',
            'Product launch timeline',
            'Market expansion opportunities',
            'Strategic partnerships'
          ]}
          timeHorizon="6M"
        />
      </div>
    </div>
  );
};
