import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { BookOpen, Search, Clock, Tag, ChevronRight, GraduationCap } from 'lucide-react';

interface EducationalContentItem {
  content_id: string;
  title: string;
  content_type: string;
  level: string;
  category: string;
  description: string;
  content: string;
  duration_minutes: number;
  tags: string[];
  created_date: string;
  updated_date: string;
}

interface EducationalContentProps {
  userTokens: number;
  onTokenDeduct: (amount: number) => void;
}

export const EducationalContent: React.FC<EducationalContentProps> = ({
  userTokens,
  onTokenDeduct
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLevel, setSelectedLevel] = useState<'all' | 'beginner' | 'intermediate' | 'advanced'>('beginner');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [contentItems, setContentItems] = useState<EducationalContentItem[]>([]);
  const [selectedContent, setSelectedContent] = useState<EducationalContentItem | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [learningPath, setLearningPath] = useState<EducationalContentItem[]>([]);

  const categories = [
    { id: 'all', name: 'All Topics', icon: '📚' },
    { id: 'fundamentals', name: 'Fundamentals', icon: '🏗️' },
    { id: 'technical_analysis', name: 'Technical Analysis', icon: '📈' },
    { id: 'portfolio_management', name: 'Portfolio Management', icon: '💼' },
    { id: 'reference', name: 'Reference', icon: '📖' }
  ];

  const levels = [
    { id: 'all', name: 'All Levels', color: 'bg-gray-100 text-gray-800' },
    { id: 'beginner', name: 'Beginner', color: 'bg-green-100 text-green-800' },
    { id: 'intermediate', name: 'Intermediate', color: 'bg-yellow-100 text-yellow-800' },
    { id: 'advanced', name: 'Advanced', color: 'bg-red-100 text-red-800' }
  ];

  useEffect(() => {
    loadEducationalContent();
    loadLearningPath();
  }, [selectedLevel, selectedCategory]);

  const loadEducationalContent = async () => {
    setIsLoading(true);
    try {
      const topic = selectedCategory === 'all' ? 'basics' : selectedCategory;
      const response = await fetch(`http://localhost:8124/analyze`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `get educational content about ${topic} for ${selectedLevel} level`
        }),
      });

      await response.json();
      
      // Mock data for demonstration (in production, this would come from the backend)
      const mockContent: EducationalContentItem[] = [
        {
          content_id: 'basics_001',
          title: 'Stock Market Basics: What Are Stocks?',
          content_type: 'article',
          level: 'beginner',
          category: 'fundamentals',
          description: 'Learn the fundamental concepts of stocks and how the stock market works',
          content: 'Comprehensive guide to understanding stocks...',
          duration_minutes: 10,
          tags: ['stocks', 'basics', 'investing', 'beginner'],
          created_date: new Date().toISOString(),
          updated_date: new Date().toISOString()
        },
        {
          content_id: 'technical_001',
          title: 'Understanding Technical Analysis',
          content_type: 'tutorial',
          level: 'intermediate',
          category: 'technical_analysis',
          description: 'Learn how to read charts and use technical indicators',
          content: 'Complete guide to technical analysis...',
          duration_minutes: 25,
          tags: ['technical_analysis', 'charts', 'indicators', 'patterns'],
          created_date: new Date().toISOString(),
          updated_date: new Date().toISOString()
        }
      ];

      setContentItems(mockContent.filter(item =>
        selectedLevel === 'all' || item.level === selectedLevel
      ));
    } catch (error) {
      console.error('Error loading educational content:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadLearningPath = async () => {
    try {
      // Mock learning path data
      const mockLearningPath: EducationalContentItem[] = [
        {
          content_id: 'path_001',
          title: 'Step 1: Market Fundamentals',
          content_type: 'article',
          level: selectedLevel,
          category: 'fundamentals',
          description: 'Start your journey with market basics',
          content: 'Foundation concepts...',
          duration_minutes: 15,
          tags: ['fundamentals', 'basics'],
          created_date: new Date().toISOString(),
          updated_date: new Date().toISOString()
        },
        {
          content_id: 'path_002',
          title: 'Step 2: Investment Strategies',
          content_type: 'tutorial',
          level: selectedLevel,
          category: 'portfolio_management',
          description: 'Learn different investment approaches',
          content: 'Strategy guide...',
          duration_minutes: 20,
          tags: ['strategy', 'portfolio'],
          created_date: new Date().toISOString(),
          updated_date: new Date().toISOString()
        }
      ];

      setLearningPath(mockLearningPath);
    } catch (error) {
      console.error('Error loading learning path:', error);
    }
  };

  const handleContentAccess = async (content: EducationalContentItem) => {
    const tokenCost = 5; // Educational content costs 5 tokens
    
    if (userTokens < tokenCost) {
      alert(`Insufficient tokens! Educational content costs ${tokenCost} tokens.`);
      return;
    }

    onTokenDeduct(tokenCost);
    setSelectedContent(content);
  };

  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    setIsLoading(true);
    try {
      // In production, this would search the educational content
      const filteredContent = contentItems.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
      setContentItems(filteredContent);
    } catch (error) {
      console.error('Error searching content:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getLevelBadgeColor = (level: string) => {
    const levelConfig = levels.find(l => l.id === level);
    return levelConfig?.color || 'bg-gray-100 text-gray-800';
  };

  const getContentTypeIcon = (type: string) => {
    switch (type) {
      case 'article': return '📄';
      case 'tutorial': return '🎓';
      case 'video': return '🎥';
      case 'quiz': return '❓';
      case 'glossary': return '📖';
      default: return '📚';
    }
  };

  if (selectedContent) {
    return (
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <Button
            variant="outline"
            onClick={() => setSelectedContent(null)}
            className="flex items-center space-x-2"
          >
            <ChevronRight className="h-4 w-4 rotate-180" />
            <span>Back to Library</span>
          </Button>
          <Badge className={getLevelBadgeColor(selectedContent.level)}>
            {selectedContent.level}
          </Badge>
        </div>

        <div className="mb-6">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-2xl">{getContentTypeIcon(selectedContent.content_type)}</span>
            <h1 className="text-2xl font-bold text-gray-900">{selectedContent.title}</h1>
          </div>
          
          <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
            <div className="flex items-center space-x-1">
              <Clock className="h-4 w-4" />
              <span>{selectedContent.duration_minutes} min read</span>
            </div>
            <div className="flex items-center space-x-1">
              <Tag className="h-4 w-4" />
              <span>{selectedContent.category.replace('_', ' ')}</span>
            </div>
          </div>

          <p className="text-gray-700 mb-6">{selectedContent.description}</p>
        </div>

        <div className="prose max-w-none">
          <div className="bg-gray-50 p-6 rounded-lg">
            <pre className="whitespace-pre-wrap text-sm text-gray-800">
              {selectedContent.content}
            </pre>
          </div>
        </div>

        <div className="mt-6 flex flex-wrap gap-2">
          {selectedContent.tags.map((tag, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <GraduationCap className="h-8 w-8 text-blue-600" />
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Financial Education Center</h2>
          <p className="text-gray-600">Learn investing fundamentals and advanced strategies</p>
        </div>
      </div>

      {/* Search and Filters */}
      <Card className="p-6">
        <div className="space-y-4">
          {/* Search Bar */}
          <div className="flex space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search educational content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
            </div>
            <Button onClick={handleSearch} className="bg-blue-600 hover:bg-blue-700">
              Search
            </Button>
          </div>

          {/* Level Filter */}
          <div className="flex space-x-2">
            <span className="text-sm font-medium text-gray-700 py-2">Level:</span>
            {levels.map((level) => (
              <Button
                key={level.id}
                variant={selectedLevel === level.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedLevel(level.id as any)}
              >
                {level.name}
              </Button>
            ))}
          </div>

          {/* Category Filter */}
          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium text-gray-700 py-2">Category:</span>
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
                className="flex items-center space-x-1"
              >
                <span>{category.icon}</span>
                <span>{category.name}</span>
              </Button>
            ))}
          </div>
        </div>
      </Card>

      {/* Learning Path */}
      {learningPath.length > 0 && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center space-x-2">
            <BookOpen className="h-5 w-5" />
            <span>Recommended Learning Path</span>
          </h3>
          <div className="grid gap-4 md:grid-cols-2">
            {learningPath.map((item, index) => (
              <div
                key={item.content_id}
                className="flex items-center space-x-4 p-4 border rounded-lg hover:bg-gray-50 cursor-pointer"
                onClick={() => handleContentAccess(item)}
              >
                <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold">
                  {index + 1}
                </div>
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{item.title}</h4>
                  <p className="text-sm text-gray-600">{item.description}</p>
                  <div className="flex items-center space-x-2 mt-1">
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-500">{item.duration_minutes} min</span>
                  </div>
                </div>
                <ChevronRight className="h-4 w-4 text-gray-400" />
              </div>
            ))}
          </div>
        </Card>
      )}

      {/* Content Library */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Content Library</h3>
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-gray-600 mt-2">Loading content...</p>
          </div>
        ) : (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {contentItems.map((item) => (
              <div
                key={item.content_id}
                className="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
                onClick={() => handleContentAccess(item)}
              >
                <div className="flex items-start justify-between mb-2">
                  <span className="text-2xl">{getContentTypeIcon(item.content_type)}</span>
                  <Badge className={getLevelBadgeColor(item.level)} variant="secondary">
                    {item.level}
                  </Badge>
                </div>
                
                <h4 className="font-semibold text-gray-900 mb-2 line-clamp-2">{item.title}</h4>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{item.description}</p>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>{item.duration_minutes} min</span>
                  </div>
                  <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">5 tokens</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
};
