"""
Token-Aware LangGraph Wrapper for FinanceGPT Pro
Integrates token consumption with the existing analysis engine
"""

from typing import Dict, Any, Optional, List
import logging
from datetime import datetime

from .graph import graph, GraphState, determine_analysis_type, extract_ticker_from_query
from ..models.token_models import (
    token_manager,
    AnalysisType,
    TokenPricing,
    SubscriptionTier
)

logger = logging.getLogger(__name__)

class TokenAwareAnalysisEngine:
    """Wrapper for the LangGraph agent that handles token consumption"""
    
    def __init__(self):
        self.graph = graph
        self.analysis_type_mapping = {
            "price": AnalysisType.PRICE_CHECK,
            "sentiment": AnalysisType.SENTIMENT_ANALYSIS,
            "technical": AnalysisType.TECHNICAL_ANALYSIS,
            "fundamental": AnalysisType.FUNDAMENTAL_ANALYSIS,
            "recommendation": AnalysisType.INVESTMENT_RECOMMENDATION,
            "comprehensive": AnalysisType.COMPREHENSIVE_ANALYSIS
        }
    
    async def analyze_with_tokens(
        self,
        query: str,
        user_id: str,
        thread_id: Optional[str] = None,
        force_analysis_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Perform analysis with token consumption
        
        Args:
            query: User query
            user_id: User ID for token tracking
            thread_id: Optional thread ID for conversation history
            force_analysis_type: Force specific analysis type (overrides auto-detection)
        
        Returns:
            Analysis result with token consumption info
        """
        try:
            # Get user profile
            user = token_manager.get_user(user_id)
            if not user:
                # Create demo user if not exists
                user = token_manager.create_user(
                    user_id=user_id,
                    username=f"User_{user_id}",
                    email=f"{user_id}@demo.com",
                    subscription_tier=SubscriptionTier.PROFESSIONAL
                )
            
            # Determine analysis type
            if force_analysis_type:
                analysis_type_str = force_analysis_type
            else:
                analysis_type_str = determine_analysis_type(query)
            
            # Map to AnalysisType enum
            analysis_type = self.analysis_type_mapping.get(
                analysis_type_str, 
                AnalysisType.CUSTOM_QUERY
            )
            
            # Get token cost
            token_cost = TokenPricing.get_cost(analysis_type)
            
            # Pre-validate token availability
            validation_result = self._validate_token_requirements(user, analysis_type, token_cost)
            if not validation_result["valid"]:
                return {
                    "success": False,
                    "error": validation_result["error"],
                    "token_info": {
                        "required_tokens": token_cost,
                        "available_tokens": user.current_tokens,
                        "analysis_type": analysis_type.value
                    }
                }
            
            # Consume tokens before analysis
            consumption_result = token_manager.process_analysis_request(user_id, analysis_type)
            if not consumption_result["success"]:
                return {
                    "success": False,
                    "error": consumption_result["error"],
                    "token_info": {
                        "required_tokens": token_cost,
                        "available_tokens": user.current_tokens,
                        "analysis_type": analysis_type.value
                    }
                }
            
            # Prepare input for LangGraph
            input_data = {
                "messages": [{"role": "user", "content": query}]
            }
            
            # Configure thread for conversation history
            config = {"configurable": {"thread_id": thread_id}} if thread_id else {}
            
            # Run the analysis
            start_time = datetime.now()
            try:
                result = self.graph.invoke(input_data, config)
                analysis_success = True
                error_message = None
            except Exception as e:
                logger.error(f"Analysis failed for user {user_id}: {str(e)}")
                analysis_success = False
                error_message = str(e)
                
                # Refund tokens on failure
                user.add_tokens(token_cost)
                logger.info(f"Refunded {token_cost} tokens to user {user_id} due to analysis failure")
                
                result = {
                    "messages": [{
                        "role": "assistant",
                        "content": f"I apologize, but the analysis failed due to a technical error: {error_message}. Your tokens have been refunded."
                    }]
                }
            
            end_time = datetime.now()
            analysis_duration = (end_time - start_time).total_seconds()
            
            # Extract ticker for additional context
            ticker = extract_ticker_from_query(query)
            
            # Prepare response
            response = {
                "success": analysis_success,
                "messages": result.get("messages", []),
                "analysis_info": {
                    "query": query,
                    "ticker": ticker,
                    "analysis_type": analysis_type.value,
                    "duration_seconds": analysis_duration,
                    "timestamp": start_time.isoformat()
                },
                "token_info": {
                    "tokens_consumed": token_cost if analysis_success else 0,
                    "remaining_tokens": user.current_tokens,
                    "daily_usage": user.daily_usage,
                    "daily_remaining": consumption_result.get("daily_remaining", 0),
                    "analysis_type": analysis_type.value
                },
                "user_info": {
                    "user_id": user_id,
                    "subscription_tier": user.subscription_tier.value,
                    "subscription_status": user.get_subscription_status()
                }
            }
            
            if not analysis_success:
                response["error"] = error_message
            
            # Log the analysis
            logger.info(
                f"Analysis completed - User: {user_id}, Type: {analysis_type.value}, "
                f"Tokens: {token_cost}, Success: {analysis_success}, Duration: {analysis_duration:.2f}s"
            )
            
            return response
            
        except Exception as e:
            logger.error(f"Token-aware analysis failed: {str(e)}")
            return {
                "success": False,
                "error": f"Analysis system error: {str(e)}",
                "token_info": {
                    "tokens_consumed": 0,
                    "analysis_type": "unknown"
                }
            }
    
    def _validate_token_requirements(self, user, analysis_type: AnalysisType, token_cost: int) -> Dict[str, Any]:
        """Validate if user meets token requirements"""
        
        # Check token balance
        if not user.can_afford(token_cost):
            return {
                "valid": False,
                "error": {
                    "type": "insufficient_tokens",
                    "message": f"Insufficient tokens. Required: {token_cost}, Available: {user.current_tokens}",
                    "required_tokens": token_cost,
                    "available_tokens": user.current_tokens,
                    "suggestion": "Purchase more tokens or upgrade your subscription"
                }
            }
        
        # Check daily limits
        if not user.has_daily_limit_remaining(token_cost):
            from ..models.token_models import SUBSCRIPTION_TIERS
            config = SUBSCRIPTION_TIERS[user.subscription_tier]
            return {
                "valid": False,
                "error": {
                    "type": "daily_limit_exceeded",
                    "message": f"Daily limit exceeded. Limit: {config.daily_limit}, Used: {user.daily_usage}",
                    "daily_limit": config.daily_limit,
                    "daily_usage": user.daily_usage,
                    "suggestion": "Upgrade subscription for higher daily limits or wait until tomorrow"
                }
            }
        
        # Check subscription status
        if not user.is_subscription_active():
            return {
                "valid": False,
                "error": {
                    "type": "subscription_expired",
                    "message": "Your subscription has expired",
                    "suggestion": "Renew your subscription to continue using advanced features"
                }
            }
        
        return {"valid": True}
    
    def get_analysis_cost_estimate(self, query: str, analysis_type: Optional[str] = None) -> Dict[str, Any]:
        """Get cost estimate for an analysis without performing it"""
        
        # Determine analysis type
        if analysis_type:
            analysis_type_str = analysis_type
        else:
            analysis_type_str = determine_analysis_type(query)
        
        # Map to AnalysisType enum
        mapped_analysis_type = self.analysis_type_mapping.get(
            analysis_type_str, 
            AnalysisType.CUSTOM_QUERY
        )
        
        token_cost = TokenPricing.get_cost(mapped_analysis_type)
        ticker = extract_ticker_from_query(query)
        
        return {
            "query": query,
            "ticker": ticker,
            "analysis_type": mapped_analysis_type.value,
            "estimated_cost": token_cost,
            "description": self._get_analysis_description(mapped_analysis_type)
        }
    
    def _get_analysis_description(self, analysis_type: AnalysisType) -> str:
        """Get description of what the analysis includes"""
        descriptions = {
            AnalysisType.PRICE_CHECK: "Current stock price and basic market data",
            AnalysisType.TECHNICAL_ANALYSIS: "Technical indicators including RSI, MACD, moving averages, and Bollinger Bands",
            AnalysisType.SENTIMENT_ANALYSIS: "Market sentiment analysis from news and social media",
            AnalysisType.FUNDAMENTAL_ANALYSIS: "Financial health analysis including P/E ratios, debt levels, and profitability",
            AnalysisType.INVESTMENT_RECOMMENDATION: "Buy/Hold/Sell recommendation with detailed reasoning",
            AnalysisType.COMPREHENSIVE_ANALYSIS: "Complete analysis including all technical, fundamental, and sentiment indicators",
            AnalysisType.IMAGE_ANALYSIS: "Chart pattern recognition and technical analysis from uploaded images",
            AnalysisType.CUSTOM_QUERY: "Custom analysis based on your specific query"
        }
        return descriptions.get(analysis_type, "Custom analysis")
    
    def get_user_analysis_history(self, user_id: str, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent analysis history for a user"""
        user_transactions = [
            {
                "transaction_id": t.transaction_id,
                "analysis_type": t.analysis_type.value if t.analysis_type else "unknown",
                "token_cost": abs(t.token_amount),
                "description": t.description,
                "timestamp": t.timestamp.isoformat(),
                "metadata": t.metadata
            }
            for t in token_manager.transactions
            if t.user_id == user_id and t.transaction_type == "usage"
        ]
        
        # Sort by timestamp (newest first) and limit
        user_transactions.sort(key=lambda x: x["timestamp"], reverse=True)
        return user_transactions[:limit]

# Global token-aware analysis engine instance
token_aware_engine = TokenAwareAnalysisEngine()
