export interface User {
  id: string;
  email: string;
  name: string;
  createdAt: Date;
  subscription: Subscription;
  usage: UserUsage;
}

export interface Subscription {
  tier: 'free' | 'pro';
  status: 'active' | 'cancelled' | 'expired' | 'trial';
  startDate: Date;
  endDate?: Date;
  stripeCustomerId?: string;
  stripeSubscriptionId?: string;
}

export interface UserUsage {
  dailyQueries: number;
  dailyLimit: number;
  monthlyTokens: number;
  monthlyLimit: number;
  lastResetDate: Date;
  totalQueries: number;
}

export interface SubscriptionTier {
  id: 'free' | 'pro';
  name: string;
  price: number;
  currency: string;
  interval: 'month' | 'year';
  features: FeatureSet;
  limits: UsageLimits;
}

export interface FeatureSet {
  basicAnalysis: boolean;
  technicalAnalysis: boolean;
  fundamentalAnalysis: boolean;
  sentimentAnalysis: boolean;
  imageAnalysis: boolean;
  comprehensiveReports: boolean;
  portfolioTracking: boolean;
  exportReports: boolean;
  prioritySupport: boolean;
}

export interface UsageLimits {
  dailyQueries: number;
  monthlyTokens: number;
  imageAnalysisPerDay: number;
  reportsPerMonth: number;
}

export const SUBSCRIPTION_TIERS: Record<string, SubscriptionTier> = {
  free: {
    id: 'free',
    name: 'FinanceGPT Basic',
    price: 0,
    currency: 'USD',
    interval: 'month',
    features: {
      basicAnalysis: true,
      technicalAnalysis: true, // Now included in free
      fundamentalAnalysis: true, // Now included in free
      sentimentAnalysis: true, // Now included in free
      imageAnalysis: false, // Pro only
      comprehensiveReports: false, // Pro only
      portfolioTracking: false, // Pro only
      exportReports: false, // Pro only
      prioritySupport: false,
    },
    limits: {
      dailyQueries: 25, // Increased from 10
      monthlyTokens: 500, // Increased from 250
      imageAnalysisPerDay: 0,
      reportsPerMonth: 0,
    },
  },
  pro: {
    id: 'pro',
    name: 'FinanceGPT Pro',
    price: 29.99,
    currency: 'USD',
    interval: 'month',
    features: {
      basicAnalysis: true,
      technicalAnalysis: true,
      fundamentalAnalysis: true,
      sentimentAnalysis: true,
      imageAnalysis: true,
      comprehensiveReports: true,
      portfolioTracking: true,
      exportReports: true,
      prioritySupport: true,
    },
    limits: {
      dailyQueries: -1, // unlimited
      monthlyTokens: 10000,
      imageAnalysisPerDay: 50,
      reportsPerMonth: 100,
    },
  },
};

export interface AuthState {
  isAuthenticated: boolean;
  user: User | null;
  loading: boolean;
  error: string | null;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface SignupCredentials {
  email: string;
  password: string;
  name: string;
}
