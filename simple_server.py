#!/usr/bin/env python3
"""
Simple combined server for the stock analysis chatbot
Serves both the frontend and API endpoints
"""

from http.server import HTTPServer, SimpleHTTPRequestHandler
import json
import urllib.parse
import os
from pathlib import Path

class ChatbotHandler(SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        # Set the directory to serve files from
        super().__init__(*args, directory=str(Path(__file__).parent), **kwargs)
    
    def end_headers(self):
        # Add CORS headers
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def do_GET(self):
        if self.path == '/':
            # Serve the main chat interface
            self.path = '/frontend/chat.html'
        elif self.path.startswith('/api/'):
            self.handle_api_request()
            return
        
        # Serve static files
        super().do_GET()
    
    def do_POST(self):
        if self.path.startswith('/analyze'):
            self.handle_analyze_request()
        else:
            self.send_error(404)
    
    def handle_analyze_request(self):
        try:
            # Get query from URL parameters or POST body
            if '?' in self.path:
                query_params = urllib.parse.parse_qs(self.path.split('?')[1])
                query = query_params.get('query', [''])[0]
            else:
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    post_data = self.rfile.read(content_length)
                    try:
                        data = json.loads(post_data.decode('utf-8'))
                        query = data.get('query', '')
                    except:
                        query = post_data.decode('utf-8')
                else:
                    query = "What's Apple's price?"
            
            # Generate mock response based on query
            response = self.generate_stock_response(query)
            
            # Send JSON response
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(response).encode('utf-8'))
            
        except Exception as e:
            self.send_error(500, f"Server error: {str(e)}")
    
    def generate_stock_response(self, query):
        """Generate a realistic stock analysis response"""
        query_lower = query.lower()
        
        # Detect stock mentions
        if 'apple' in query_lower or 'aapl' in query_lower:
            ticker = 'AAPL'
            company = 'Apple Inc.'
            price = '$175.43'
        elif 'tesla' in query_lower or 'tsla' in query_lower:
            ticker = 'TSLA'
            company = 'Tesla Inc.'
            price = '$248.87'
        elif 'microsoft' in query_lower or 'msft' in query_lower:
            ticker = 'MSFT'
            company = 'Microsoft Corporation'
            price = '$378.85'
        elif 'nvidia' in query_lower or 'nvda' in query_lower:
            ticker = 'NVDA'
            company = 'NVIDIA Corporation'
            price = '$875.28'
        elif 'amazon' in query_lower or 'amzn' in query_lower:
            ticker = 'AMZN'
            company = 'Amazon.com Inc.'
            price = '$155.89'
        else:
            ticker = 'AAPL'
            company = 'Apple Inc.'
            price = '$175.43'
        
        # Determine response type
        if 'price' in query_lower or 'cost' in query_lower:
            response_text = f"""## 💰 Stock Price Analysis for {ticker}

**Current Price**: {price}
**Volume**: 45,234,567
**Market Cap**: $2.89T
**P/E Ratio**: 28.5
**52-Week Range**: $164.08 - $199.62

**📊 Key Metrics:**
- **Day Change**: +$2.15 (+1.24%)
- **After Hours**: {price} (0.00%)
- **Avg Volume**: 52.1M

**⚠️ Important Disclaimer**: This data is for demonstration purposes only."""

        elif 'buy' in query_lower or 'recommend' in query_lower:
            response_text = f"""## 🎯 Investment Recommendation for {ticker}

**Recommendation**: Buy
**Confidence Level**: 75%

**Key Factors:**
• Strong financial performance
• Positive market sentiment
• Technical indicators show upward trend

**Technical Outlook**: Bullish signals
**Market Sentiment**: Positive (0.68)

**Risk Assessment**: Medium risk level

**Summary**: Based on comprehensive analysis combining fundamental health, technical indicators, and market sentiment, I recommend a Buy position with 75% confidence.

**Important Disclaimer**: This recommendation is generated by AI analysis and is for informational purposes only. It should not be considered as personalized financial advice. Always conduct your own research and consult with a qualified financial advisor before making investment decisions."""

        elif 'sentiment' in query_lower:
            response_text = f"""## 📊 Sentiment Analysis for {ticker}

**Overall Sentiment**: Positive (Score: 0.72)

**📈 Recent News Sentiment:**
- Positive: 68%
- Neutral: 22%
- Negative: 10%

**🐦 Social Media Buzz:**
- Twitter mentions: 15,420 (↑12%)
- Reddit discussions: 2,847 posts
- Overall tone: Optimistic

**📰 Key Headlines:**
- "{company} reports strong quarterly earnings"
- "Analysts upgrade {ticker} price target"
- "Innovation pipeline looks promising"

**Market Mood**: Bullish sentiment continues to drive interest."""

        else:  # Comprehensive analysis
            response_text = f"""# 🚀 Comprehensive Stock Analysis: {ticker}

## 💰 Current Market Data
- **Current Price**: {price}
- **Volume**: 45,234,567
- **Market Cap**: $2.89T
- **Company**: {company} (Technology)

## 📈 Technical Analysis
**RSI (14)**: 58.2 (Neutral)
**MACD**: Bullish crossover detected
**Moving Averages**: 
- 50-day: $172.45 (Above)
- 200-day: $168.90 (Above)

## 📊 Market Sentiment
**Overall Sentiment**: Positive (0.72)
- Recent news: 68% positive
- Social media: Bullish trend
- Analyst ratings: 12 Buy, 3 Hold, 1 Sell

## 🎯 Investment Recommendation
**Recommendation**: Buy
**Price Target**: $195.00
**Risk Level**: Medium

## 📚 Understanding This Analysis
- **Technical Analysis**: Based on price patterns and indicators
- **Fundamental Analysis**: Based on company financials and valuation
- **Sentiment Analysis**: Based on news and social media sentiment

**⚠️ Important Disclaimer**: This analysis is generated by AI for informational purposes only and should not be considered as personalized financial advice. Always conduct your own research and consult with a qualified financial advisor before making investment decisions."""

        return {
            "query": query,
            "response": response_text,
            "ticker": ticker,
            "status": "success"
        }

def run_server(port=8124):
    server_address = ('', port)
    httpd = HTTPServer(server_address, ChatbotHandler)
    print(f"🚀 Stock Analysis Chatbot Server running on http://localhost:{port}")
    print(f"📱 Chat Interface: http://localhost:{port}")
    print(f"🔧 API Endpoint: http://localhost:{port}/analyze")
    print("Press Ctrl+C to stop the server")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n👋 Server stopped")
        httpd.server_close()

if __name__ == '__main__':
    run_server()
