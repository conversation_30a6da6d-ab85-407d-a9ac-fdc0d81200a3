import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { 
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Target,
  Lightbulb,
  Shield,
  Zap,
  Eye,
  RefreshCw,
  Sparkles
} from 'lucide-react';

interface PortfolioHolding {
  ticker: string;
  company_name: string;
  shares: number;
  average_cost: number;
  current_price: number;
  market_value: number;
  unrealized_gain_loss: number;
  unrealized_gain_loss_percent: number;
  sector: string;
}

interface Portfolio {
  portfolio_id: string;
  name: string;
  total_value: number;
  total_gain_loss: number;
  total_gain_loss_percent: number;
  holdings: PortfolioHolding[];
  cash_balance: number;
  created_date: string;
  last_updated: string;
}

interface AIInsight {
  id: string;
  type: 'opportunity' | 'risk' | 'optimization' | 'alert';
  title: string;
  description: string;
  confidence: number;
  impact: 'high' | 'medium' | 'low';
  actionable: boolean;
  recommendation?: string;
  ticker?: string;
  sector?: string;
}

interface AIPortfolioInsightsProps {
  portfolio: Portfolio;
  userTier: 'Basic' | 'Pro';
  onTokenDeduct: (amount: number) => void;
}

export const AIPortfolioInsights: React.FC<AIPortfolioInsightsProps> = ({
  portfolio,
  userTier,
  onTokenDeduct
}) => {
  const [insights, setInsights] = useState<AIInsight[]>([]);
  const [loading, setLoading] = useState(false);
  const [lastAnalysis, setLastAnalysis] = useState<Date | null>(null);
  const [selectedInsightType, setSelectedInsightType] = useState<'all' | 'opportunity' | 'risk' | 'optimization' | 'alert'>('all');

  // Generate AI insights
  const generateInsights = async () => {
    if (userTier === 'Basic') {
      // Basic users get limited insights
      const basicInsights: AIInsight[] = [
        {
          id: '1',
          type: 'optimization',
          title: 'Portfolio Diversification',
          description: 'Your portfolio shows concentration in Technology sector (65%). Consider diversifying across more sectors.',
          confidence: 85,
          impact: 'medium',
          actionable: true,
          recommendation: 'Add holdings in Healthcare, Finance, or Consumer sectors',
          sector: 'Technology'
        },
        {
          id: '2',
          type: 'alert',
          title: 'High Volatility Stock',
          description: 'TSLA has shown high volatility (±15%) in recent weeks. Monitor position size.',
          confidence: 78,
          impact: 'medium',
          actionable: true,
          ticker: 'TSLA'
        }
      ];
      setInsights(basicInsights);
      return;
    }

    // Pro users get comprehensive AI analysis
    setLoading(true);
    
    try {
      // Simulate AI analysis delay
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const proInsights: AIInsight[] = [
        {
          id: '1',
          type: 'opportunity',
          title: 'Undervalued Growth Opportunity',
          description: 'AI analysis suggests GOOGL is undervalued by 12% based on DCF model and peer comparison.',
          confidence: 92,
          impact: 'high',
          actionable: true,
          recommendation: 'Consider increasing GOOGL position by 15-20%',
          ticker: 'GOOGL'
        },
        {
          id: '2',
          type: 'risk',
          title: 'Sector Concentration Risk',
          description: 'Technology sector represents 68% of portfolio. High correlation risk during tech selloffs.',
          confidence: 88,
          impact: 'high',
          actionable: true,
          recommendation: 'Reduce tech exposure to 50% and diversify into defensive sectors',
          sector: 'Technology'
        },
        {
          id: '3',
          type: 'optimization',
          title: 'Rebalancing Opportunity',
          description: 'AAPL position has grown to 35% of portfolio due to price appreciation. Consider taking profits.',
          confidence: 85,
          impact: 'medium',
          actionable: true,
          recommendation: 'Trim AAPL position by 25% and reinvest in underweight positions',
          ticker: 'AAPL'
        },
        {
          id: '4',
          type: 'alert',
          title: 'Earnings Risk Alert',
          description: 'NVDA reports earnings next week. Options activity suggests high volatility expected.',
          confidence: 79,
          impact: 'medium',
          actionable: true,
          recommendation: 'Consider hedging NVDA position or reducing size before earnings',
          ticker: 'NVDA'
        },
        {
          id: '5',
          type: 'opportunity',
          title: 'Market Timing Signal',
          description: 'AI sentiment analysis shows oversold conditions in semiconductor sector. Potential entry point.',
          confidence: 76,
          impact: 'medium',
          actionable: true,
          recommendation: 'Consider adding semiconductor ETF or individual names on weakness',
          sector: 'Technology'
        },
        {
          id: '6',
          type: 'optimization',
          title: 'Tax Loss Harvesting',
          description: 'TSLA position shows unrealized loss. Consider tax loss harvesting opportunity.',
          confidence: 82,
          impact: 'low',
          actionable: true,
          recommendation: 'Harvest TSLA loss and reinvest in similar EV stock after wash sale period',
          ticker: 'TSLA'
        }
      ];
      
      setInsights(proInsights);
      setLastAnalysis(new Date());
      
      // Deduct tokens for AI analysis
      onTokenDeduct(150);
      
    } catch (error) {
      console.error('Error generating insights:', error);
    } finally {
      setLoading(false);
    }
  };

  // Auto-generate insights on component mount
  useEffect(() => {
    generateInsights();
  }, [portfolio.portfolio_id]);

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return <TrendingUp className="h-5 w-5 text-green-400" />;
      case 'risk': return <AlertTriangle className="h-5 w-5 text-red-400" />;
      case 'optimization': return <Target className="h-5 w-5 text-blue-400" />;
      case 'alert': return <Zap className="h-5 w-5 text-yellow-400" />;
      default: return <Lightbulb className="h-5 w-5 text-purple-400" />;
    }
  };

  const getInsightColor = (type: string) => {
    switch (type) {
      case 'opportunity': return 'from-green-900/20 to-emerald-900/20 border-green-500/30';
      case 'risk': return 'from-red-900/20 to-rose-900/20 border-red-500/30';
      case 'optimization': return 'from-blue-900/20 to-cyan-900/20 border-blue-500/30';
      case 'alert': return 'from-yellow-900/20 to-orange-900/20 border-yellow-500/30';
      default: return 'from-purple-900/20 to-pink-900/20 border-purple-500/30';
    }
  };

  const getImpactBadge = (impact: string) => {
    const colors = {
      high: 'bg-red-600',
      medium: 'bg-yellow-600',
      low: 'bg-green-600'
    };
    return colors[impact as keyof typeof colors] || 'bg-gray-600';
  };

  const filteredInsights = selectedInsightType === 'all' 
    ? insights 
    : insights.filter(insight => insight.type === selectedInsightType);

  const insightTypes = [
    { key: 'all', label: 'All Insights', count: insights.length },
    { key: 'opportunity', label: 'Opportunities', count: insights.filter(i => i.type === 'opportunity').length },
    { key: 'risk', label: 'Risks', count: insights.filter(i => i.type === 'risk').length },
    { key: 'optimization', label: 'Optimization', count: insights.filter(i => i.type === 'optimization').length },
    { key: 'alert', label: 'Alerts', count: insights.filter(i => i.type === 'alert').length }
  ];

  return (
    <div className="space-y-6">
      {/* AI Insights Header */}
      <Card className="p-6 bg-gradient-to-r from-purple-900/20 to-blue-900/20 border-purple-500/30">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Brain className="h-6 w-6 text-white" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-white">AI Portfolio Insights</h3>
              <p className="text-gray-300 text-sm">
                {userTier === 'Pro' ? 'Advanced AI analysis with actionable recommendations' : 'Basic portfolio insights'}
              </p>
            </div>
          </div>
          
          <div className="flex items-center space-x-3">
            {lastAnalysis && (
              <div className="text-right text-sm text-gray-300">
                <div>Last Analysis</div>
                <div>{lastAnalysis.toLocaleTimeString()}</div>
              </div>
            )}
            
            <Button
              onClick={generateInsights}
              disabled={loading}
              className="bg-purple-600 hover:bg-purple-700 text-white"
            >
              {loading ? (
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <Sparkles className="h-4 w-4 mr-2" />
              )}
              {loading ? 'Analyzing...' : 'Refresh Insights'}
            </Button>
          </div>
        </div>

        {/* Insight Type Filters */}
        <div className="flex flex-wrap gap-2">
          {insightTypes.map((type) => (
            <button
              key={type.key}
              onClick={() => setSelectedInsightType(type.key as any)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                selectedInsightType === type.key
                  ? 'bg-purple-600 text-white'
                  : 'bg-white/10 text-gray-300 hover:bg-white/20'
              }`}
            >
              {type.label} ({type.count})
            </button>
          ))}
        </div>
      </Card>

      {/* Insights List */}
      {loading ? (
        <Card className="p-8 text-center">
          <div className="flex items-center justify-center space-x-3 mb-4">
            <Brain className="h-8 w-8 text-purple-400 animate-pulse" />
            <RefreshCw className="h-6 w-6 text-blue-400 animate-spin" />
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">AI Analysis in Progress</h3>
          <p className="text-gray-300">
            Analyzing portfolio composition, market conditions, and risk factors...
          </p>
        </Card>
      ) : (
        <div className="space-y-4">
          {filteredInsights.map((insight) => (
            <Card key={insight.id} className={`p-6 bg-gradient-to-r ${getInsightColor(insight.type)}`}>
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4 flex-1">
                  <div className="p-2 bg-white/10 rounded-lg">
                    {getInsightIcon(insight.type)}
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="text-lg font-semibold text-white">{insight.title}</h4>
                      <Badge className={`${getImpactBadge(insight.impact)} text-white text-xs`}>
                        {insight.impact.toUpperCase()} IMPACT
                      </Badge>
                      <Badge className="bg-gray-600 text-gray-200 text-xs">
                        {insight.confidence}% Confidence
                      </Badge>
                    </div>
                    
                    <p className="text-gray-300 mb-3">{insight.description}</p>
                    
                    {insight.recommendation && (
                      <div className="bg-white/5 rounded-lg p-3 mb-3">
                        <div className="flex items-center space-x-2 mb-1">
                          <Lightbulb className="h-4 w-4 text-yellow-400" />
                          <span className="text-sm font-medium text-white">AI Recommendation</span>
                        </div>
                        <p className="text-gray-300 text-sm">{insight.recommendation}</p>
                      </div>
                    )}
                    
                    <div className="flex items-center space-x-4 text-sm text-gray-400">
                      {insight.ticker && (
                        <div className="flex items-center space-x-1">
                          <Target className="h-3 w-3" />
                          <span>Ticker: {insight.ticker}</span>
                        </div>
                      )}
                      {insight.sector && (
                        <div className="flex items-center space-x-1">
                          <Shield className="h-3 w-3" />
                          <span>Sector: {insight.sector}</span>
                        </div>
                      )}
                      <div className="flex items-center space-x-1">
                        <Eye className="h-3 w-3" />
                        <span>Type: {insight.type}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {insight.actionable && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="border-white/20 text-white hover:bg-white/10"
                  >
                    Take Action
                  </Button>
                )}
              </div>
            </Card>
          ))}
        </div>
      )}

      {/* Pro Upgrade Prompt for Basic Users */}
      {userTier === 'Basic' && (
        <Card className="p-6 bg-gradient-to-r from-orange-900/20 to-purple-900/20 border-orange-500/30">
          <div className="flex items-center space-x-4">
            <Sparkles className="h-8 w-8 text-orange-400" />
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-white mb-2">
                Unlock Advanced AI Insights
              </h3>
              <p className="text-gray-300 mb-4">
                Upgrade to Pro for comprehensive AI analysis including market timing signals, 
                advanced risk assessment, tax optimization strategies, and personalized recommendations.
              </p>
              <Button className="bg-orange-600 hover:bg-orange-700 text-white">
                Upgrade to Pro
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
