import React, { useState, useEffect, useMemo } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { 
  BarChart3, 
  PieChart, 
  TrendingUp, 
  TrendingDown,
  Calendar,
  Target,
  Activity,
  Maximize2,
  Minimize2
} from 'lucide-react';

interface PortfolioHolding {
  ticker: string;
  company_name: string;
  shares: number;
  average_cost: number;
  current_price: number;
  market_value: number;
  unrealized_gain_loss: number;
  unrealized_gain_loss_percent: number;
  sector: string;
}

interface Portfolio {
  portfolio_id: string;
  name: string;
  total_value: number;
  total_gain_loss: number;
  total_gain_loss_percent: number;
  holdings: PortfolioHolding[];
  cash_balance: number;
  created_date: string;
  last_updated: string;
}

interface PortfolioChartsProps {
  portfolio: Portfolio;
  userTier: 'Basic' | 'Pro';
}

interface ChartDataPoint {
  date: string;
  value: number;
  gain_loss: number;
  gain_loss_percent: number;
}

export const PortfolioCharts: React.FC<PortfolioChartsProps> = ({
  portfolio,
  userTier
}) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'1W' | '1M' | '3M' | '6M' | '1Y' | 'ALL'>('1M');
  const [expandedChart, setExpandedChart] = useState<string | null>(null);
  const [historicalData, setHistoricalData] = useState<ChartDataPoint[]>([]);

  // Generate mock historical data
  useEffect(() => {
    const generateHistoricalData = () => {
      const days = selectedTimeframe === '1W' ? 7 : 
                   selectedTimeframe === '1M' ? 30 :
                   selectedTimeframe === '3M' ? 90 :
                   selectedTimeframe === '6M' ? 180 :
                   selectedTimeframe === '1Y' ? 365 : 730;

      const data: ChartDataPoint[] = [];
      const startValue = portfolio.total_value - portfolio.total_gain_loss;
      
      for (let i = days; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        
        // Generate realistic portfolio growth with some volatility
        const progress = (days - i) / days;
        const trend = portfolio.total_gain_loss * progress;
        const volatility = (Math.random() - 0.5) * (portfolio.total_value * 0.02); // 2% daily volatility
        const value = startValue + trend + volatility;
        
        data.push({
          date: date.toISOString().split('T')[0],
          value: Math.max(value, startValue * 0.8), // Prevent unrealistic drops
          gain_loss: value - startValue,
          gain_loss_percent: ((value - startValue) / startValue) * 100
        });
      }
      
      return data;
    };

    setHistoricalData(generateHistoricalData());
  }, [selectedTimeframe, portfolio]);

  const sectorAllocation = useMemo(() => {
    const sectorMap = new Map<string, number>();
    portfolio.holdings.forEach(holding => {
      const current = sectorMap.get(holding.sector) || 0;
      sectorMap.set(holding.sector, current + holding.market_value);
    });
    
    const total = portfolio.holdings.reduce((sum, holding) => sum + holding.market_value, 0);
    return Array.from(sectorMap.entries()).map(([sector, value]) => ({
      sector,
      value,
      percentage: (value / total) * 100
    }));
  }, [portfolio.holdings]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  };

  const timeframes = [
    { key: '1W', label: '1W' },
    { key: '1M', label: '1M' },
    { key: '3M', label: '3M' },
    { key: '6M', label: '6M' },
    { key: '1Y', label: '1Y' },
    { key: 'ALL', label: 'ALL' }
  ];

  const getSectorColor = (index: number) => {
    const colors = [
      '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6',
      '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
    ];
    return colors[index % colors.length];
  };

  const renderPerformanceChart = () => {
    if (historicalData.length === 0) return null;

    const maxValue = Math.max(...historicalData.map(d => d.value));
    const minValue = Math.min(...historicalData.map(d => d.value));
    const range = maxValue - minValue;
    const padding = range * 0.1;

    return (
      <div className="relative h-64 w-full">
        <svg className="w-full h-full" viewBox="0 0 800 200">
          {/* Grid lines */}
          {[0, 25, 50, 75, 100].map(y => (
            <line
              key={y}
              x1="50"
              y1={y * 2}
              x2="750"
              y2={y * 2}
              stroke="#374151"
              strokeWidth="0.5"
              opacity="0.3"
            />
          ))}
          
          {/* Performance line */}
          <polyline
            fill="none"
            stroke={portfolio.total_gain_loss >= 0 ? "#10B981" : "#EF4444"}
            strokeWidth="2"
            points={historicalData.map((point, index) => {
              const x = 50 + (index / (historicalData.length - 1)) * 700;
              const y = 180 - ((point.value - minValue + padding) / (range + 2 * padding)) * 160;
              return `${x},${y}`;
            }).join(' ')}
          />
          
          {/* Data points */}
          {historicalData.map((point, index) => {
            const x = 50 + (index / (historicalData.length - 1)) * 700;
            const y = 180 - ((point.value - minValue + padding) / (range + 2 * padding)) * 160;
            
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="3"
                fill={portfolio.total_gain_loss >= 0 ? "#10B981" : "#EF4444"}
                className="opacity-0 hover:opacity-100 transition-opacity"
              />
            );
          })}
          
          {/* Y-axis labels */}
          {[minValue, (minValue + maxValue) / 2, maxValue].map((value, index) => (
            <text
              key={index}
              x="40"
              y={180 - (index * 80) + 5}
              fill="#9CA3AF"
              fontSize="12"
              textAnchor="end"
            >
              {formatCurrency(value)}
            </text>
          ))}
        </svg>
        
        {/* Chart overlay info */}
        <div className="absolute top-4 left-4 bg-black/50 backdrop-blur-sm rounded-lg p-3">
          <div className="text-white text-sm">
            <div className="font-semibold">{formatCurrency(portfolio.total_value)}</div>
            <div className={`text-xs ${portfolio.total_gain_loss >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatCurrency(portfolio.total_gain_loss)} ({formatPercent(portfolio.total_gain_loss_percent)})
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderSectorPieChart = () => {
    const centerX = 120;
    const centerY = 120;
    const radius = 80;
    let currentAngle = 0;

    return (
      <div className="relative">
        <svg className="w-60 h-60" viewBox="0 0 240 240">
          {sectorAllocation.map((sector, index) => {
            const angle = (sector.percentage / 100) * 2 * Math.PI;
            const startAngle = currentAngle;
            const endAngle = currentAngle + angle;
            
            const x1 = centerX + radius * Math.cos(startAngle);
            const y1 = centerY + radius * Math.sin(startAngle);
            const x2 = centerX + radius * Math.cos(endAngle);
            const y2 = centerY + radius * Math.sin(endAngle);
            
            const largeArcFlag = angle > Math.PI ? 1 : 0;
            
            const pathData = [
              `M ${centerX} ${centerY}`,
              `L ${x1} ${y1}`,
              `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
              'Z'
            ].join(' ');
            
            currentAngle += angle;
            
            return (
              <path
                key={sector.sector}
                d={pathData}
                fill={getSectorColor(index)}
                stroke="#1F2937"
                strokeWidth="2"
                className="hover:opacity-80 transition-opacity cursor-pointer"
              />
            );
          })}
          
          {/* Center circle */}
          <circle
            cx={centerX}
            cy={centerY}
            r="30"
            fill="#1F2937"
            stroke="#374151"
            strokeWidth="2"
          />
          
          {/* Center text */}
          <text
            x={centerX}
            y={centerY - 5}
            fill="#FFFFFF"
            fontSize="12"
            textAnchor="middle"
            fontWeight="bold"
          >
            Portfolio
          </text>
          <text
            x={centerX}
            y={centerY + 10}
            fill="#9CA3AF"
            fontSize="10"
            textAnchor="middle"
          >
            Allocation
          </text>
        </svg>
        
        {/* Legend */}
        <div className="absolute top-0 left-64 space-y-2">
          {sectorAllocation.map((sector, index) => (
            <div key={sector.sector} className="flex items-center space-x-2">
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: getSectorColor(index) }}
              />
              <div className="text-sm">
                <span className="text-white font-medium">{sector.sector}</span>
                <span className="text-gray-300 ml-2">{sector.percentage.toFixed(1)}%</span>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="space-y-6">
      {/* Portfolio Performance Chart */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white flex items-center">
            <TrendingUp className="h-5 w-5 mr-2 text-blue-400" />
            Portfolio Performance
          </h3>
          
          <div className="flex items-center space-x-4">
            {/* Timeframe Selector */}
            <div className="flex space-x-1 bg-white/5 rounded-lg p-1">
              {timeframes.map((timeframe) => (
                <button
                  key={timeframe.key}
                  onClick={() => setSelectedTimeframe(timeframe.key as any)}
                  className={`px-3 py-1 rounded text-sm font-medium transition-colors ${
                    selectedTimeframe === timeframe.key
                      ? 'bg-blue-600 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-white/10'
                  }`}
                >
                  {timeframe.label}
                </button>
              ))}
            </div>
            
            {/* Expand button */}
            <Button
              onClick={() => setExpandedChart(expandedChart === 'performance' ? null : 'performance')}
              variant="outline"
              size="sm"
              className="border-gray-500 text-gray-300 hover:bg-gray-700"
            >
              {expandedChart === 'performance' ? (
                <Minimize2 className="h-4 w-4" />
              ) : (
                <Maximize2 className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
        
        {renderPerformanceChart()}
        
        {/* Performance metrics */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              {historicalData.length > 0 ? formatPercent(historicalData[historicalData.length - 1]?.gain_loss_percent || 0) : '0%'}
            </div>
            <div className="text-sm text-gray-400">Total Return</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              {formatCurrency(Math.max(...historicalData.map(d => d.value)) - Math.min(...historicalData.map(d => d.value)))}
            </div>
            <div className="text-sm text-gray-400">Volatility Range</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              {Math.max(...historicalData.map(d => d.gain_loss_percent)).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-400">Peak Gain</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-white">
              {Math.min(...historicalData.map(d => d.gain_loss_percent)).toFixed(1)}%
            </div>
            <div className="text-sm text-gray-400">Max Drawdown</div>
          </div>
        </div>
      </Card>

      {/* Sector Allocation Chart */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white flex items-center">
            <PieChart className="h-5 w-5 mr-2 text-purple-400" />
            Sector Allocation
          </h3>
          
          <Button
            onClick={() => setExpandedChart(expandedChart === 'sectors' ? null : 'sectors')}
            variant="outline"
            size="sm"
            className="border-gray-500 text-gray-300 hover:bg-gray-700"
          >
            {expandedChart === 'sectors' ? (
              <Minimize2 className="h-4 w-4" />
            ) : (
              <Maximize2 className="h-4 w-4" />
            )}
          </Button>
        </div>
        
        <div className="flex items-center justify-center">
          {renderSectorPieChart()}
        </div>
      </Card>

      {/* Holdings Performance Chart */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-xl font-semibold text-white flex items-center">
            <BarChart3 className="h-5 w-5 mr-2 text-green-400" />
            Holdings Performance
          </h3>
        </div>
        
        <div className="space-y-3">
          {portfolio.holdings
            .sort((a, b) => b.unrealized_gain_loss_percent - a.unrealized_gain_loss_percent)
            .map((holding) => (
              <div key={holding.ticker} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                <div className="flex items-center space-x-3">
                  <span className="font-semibold text-white">{holding.ticker}</span>
                  <Badge className="bg-gray-600 text-gray-200 text-xs">
                    {holding.sector}
                  </Badge>
                </div>
                
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-white font-medium">
                      {formatCurrency(holding.market_value)}
                    </div>
                    <div className="text-gray-400 text-sm">
                      {holding.shares} shares
                    </div>
                  </div>
                  
                  <div className="w-32 bg-gray-700 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        holding.unrealized_gain_loss_percent >= 0 ? 'bg-green-500' : 'bg-red-500'
                      }`}
                      style={{
                        width: `${Math.min(Math.abs(holding.unrealized_gain_loss_percent) * 2, 100)}%`
                      }}
                    />
                  </div>
                  
                  <div className={`text-right font-medium ${
                    holding.unrealized_gain_loss >= 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    <div>{formatPercent(holding.unrealized_gain_loss_percent)}</div>
                    <div className="text-sm">{formatCurrency(holding.unrealized_gain_loss)}</div>
                  </div>
                </div>
              </div>
            ))}
        </div>
      </Card>
    </div>
  );
};
