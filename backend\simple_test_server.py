#!/usr/bin/env python3
"""
Simple test server for Advanced Analytics
"""

from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
import uvicorn
import json
import random
import re

app = FastAPI(title="Simple Test Server for Advanced Analytics")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

class AnalysisRequest(BaseModel):
    query: str
    thread_id: Optional[str] = None
    analysis_type: Optional[str] = None
    user_id: Optional[str] = None

class Message(BaseModel):
    role: str
    content: str

@app.get("/")
async def root():
    return {"message": "Simple Test Server is running!", "status": "ok"}

@app.post("/analyze")
async def analyze_stock(request: AnalysisRequest):
    """
    Simple mock analysis endpoint
    """
    try:
        # Extract ticker from query using regex
        query_text = request.query
        query_lower = query_text.lower()

        # Look for ticker patterns (1-5 uppercase letters)
        ticker_match = re.search(r'\b([A-Z]{1,5})\b', query_text)
        if ticker_match:
            ticker = ticker_match.group(1)
        else:
            # Try to find ticker in lowercase and convert
            ticker_match = re.search(r'\b([a-z]{1,5})\b', query_lower)
            if ticker_match:
                ticker = ticker_match.group(1).upper()
            else:
                ticker = "UNKNOWN"

        # Determine analysis type based on query content
        analysis_type = "comprehensive"  # Default to comprehensive analysis
        if "momentum" in query_lower:
            analysis_type = "momentum"
        elif "volatility" in query_lower:
            analysis_type = "volatility"
        elif "risk" in query_lower:
            analysis_type = "risk"
        elif "correlation" in query_lower:
            analysis_type = "correlation"
        elif "pattern" in query_lower:
            analysis_type = "pattern"
        elif "sector" in query_lower:
            analysis_type = "sector"
        elif any(word in query_lower for word in ["price", "buy", "sell", "recommend", "analysis", "stock"]):
            analysis_type = "comprehensive"
        
        # Generate realistic dynamic analysis based on type
        def generate_momentum_analysis(ticker):
            momentum_score = round(random.uniform(0.05, 0.25), 4)
            short_term = round(random.uniform(-8, 18), 2)
            medium_term = round(random.uniform(-12, 28), 2)
            long_term = round(random.uniform(-20, 40), 2)
            rsi = round(random.uniform(25, 75), 1)
            macd = round(random.uniform(-3, 6), 2)

            trend_strength = "Strong Uptrend" if momentum_score > 0.18 else "Moderate Uptrend" if momentum_score > 0.12 else "Weak Uptrend" if momentum_score > 0.08 else "Sideways"
            rsi_status = "Overbought" if rsi > 70 else "Oversold" if rsi < 30 else "Neutral"
            macd_status = "Bullish" if macd > 0 else "Bearish"

            return f"""📈 **Momentum Analysis for {ticker}**

**Trend Strength**: {trend_strength}
**Overall Momentum Score**: {momentum_score}

**Timeframe Analysis**:
- Short-term (10 days): {short_term:+.2f}%
- Medium-term (30 days): {medium_term:+.2f}%
- Long-term (60 days): {long_term:+.2f}%

**Technical Indicators**:
- RSI (14): {rsi} ({rsi_status})
- MACD: {macd} ({macd_status})
- Moving Average Convergence: {'Positive' if macd > 0 else 'Negative'}

**Interpretation**:
{trend_strength} indicates {'strong positive' if momentum_score > 0.18 else 'moderate positive' if momentum_score > 0.12 else 'mixed'} price momentum across timeframes. The stock shows {'consistent upward movement' if momentum_score > 0.15 else 'moderate price action' if momentum_score > 0.08 else 'sideways movement'} with {'accelerating' if short_term > medium_term else 'decelerating'} momentum in recent periods. RSI at {rsi} suggests the stock is {'not overbought' if rsi < 65 else 'approaching overbought levels' if rsi < 75 else 'overbought'}, {'providing room for continued growth' if rsi < 65 else 'suggesting caution may be warranted'}."""

        mock_results = {
            "momentum": generate_momentum_analysis(ticker),

            "volatility": lambda: f"""📊 **Volatility Analysis for {ticker}**

**Risk Level**: {random.choice(['Low', 'Medium', 'High'])}
**Current Volatility**: {random.uniform(15, 45):.2f}% (annualized)
**Historical Volatility**: {random.uniform(20, 50):.2f}% (annualized)
**Volatility Percentile**: {random.uniform(20, 90):.1f}%

**Volatility Breakdown**:
- 1-week volatility: {random.uniform(10, 30):.1f}%
- 1-month volatility: {random.uniform(15, 35):.1f}%
- 3-month volatility: {random.uniform(20, 40):.1f}%

**Risk Assessment**:
Current volatility is at the {random.uniform(20, 90):.0f}th percentile of historical levels. Risk level indicates {'low' if random.random() > 0.6 else 'moderate'} price fluctuations compared to historical norms. The {'decreasing' if random.random() > 0.5 else 'increasing'} volatility trend suggests {'stabilizing' if random.random() > 0.5 else 'increasing'} price action.""",

            "risk": lambda: f"""⚠️ **Risk Metrics for {ticker}**

**Overall Risk Score**: {random.uniform(3, 9):.1f}/10 ({random.choice(['Low', 'Medium', 'High'])})
**Volatility**: {random.uniform(15, 45):.2f}% (annualized)
**Value at Risk (95%)**: -{random.uniform(1.5, 4.5):.2f}%
**Maximum Drawdown**: -{random.uniform(10, 35):.2f}%
**Sharpe Ratio**: {random.uniform(0.5, 2.5):.3f}
**Sortino Ratio**: {random.uniform(0.8, 3.0):.3f}
**Beta**: {random.uniform(0.6, 1.8):.2f}

**Risk Breakdown**:
- Market Risk: {random.choice(['Low', 'Medium', 'High'])} (β = {random.uniform(0.6, 1.8):.2f})
- Liquidity Risk: {random.choice(['Low', 'Medium'])}
- Credit Risk: {random.choice(['Low', 'Medium'])}
- Operational Risk: {random.choice(['Low', 'Medium', 'High'])}

**Interpretation**:
These metrics indicate a {'favorable' if random.random() > 0.4 else 'moderate'} risk-return profile. The Sharpe ratio {'above 1.0' if random.random() > 0.6 else 'below 1.0'} suggests {'good' if random.random() > 0.6 else 'moderate'} risk-adjusted returns, while the Sortino ratio shows {'strong' if random.random() > 0.5 else 'adequate'} downside risk management.""",

            "correlation": f"""🔗 **Correlation Analysis for {ticker}**

**Market Correlation**: 0.78 (High)
**Sector Correlation**: 0.85 (Very High)

**Top Correlations**:
- SPY (S&P 500): 0.78
- QQQ (NASDAQ): 0.82
- Sector ETF: 0.85
- MSFT: 0.71
- GOOGL: 0.69

**Diversification Score**: 3.2/10 (Low)

**Interpretation**:
High correlation with market indices suggests the stock moves closely with broader market trends. Strong sector correlation indicates sector-specific factors significantly influence price movements.""",

            "pattern": f"""📈 **Pattern Recognition for {ticker}**

**Primary Pattern**: Ascending Triangle
**Pattern Confidence**: 82%
**Completion**: 70%

**Technical Patterns Detected**:
- Ascending Triangle (82% confidence)
- Higher Lows Formation (75% confidence)
- Volume Confirmation (68% confidence)

**Support/Resistance Levels**:
- Key Support: $168.50
- Secondary Support: $172.20
- Primary Resistance: $178.90
- Target Resistance: $185.20

**Pattern Implications**:
Ascending triangle pattern suggests bullish continuation. The pattern shows higher lows with consistent resistance, indicating accumulation phase.""",

            "sector": lambda: f"""🏢 **Sector Performance Analysis for {ticker}**

**Sector**: {random.choice(['Technology', 'Healthcare', 'Financial', 'Consumer', 'Energy', 'Industrial'])}
**Sector Performance**: {random.uniform(-5, 20):+.1f}% (YTD)
**Relative Performance**: {random.uniform(-3, 5):+.1f}% vs Sector

**Sector Rankings**:
- Performance Rank: {random.randint(1, 11)}/11 sectors
- Momentum Rank: {random.randint(1, 11)}/11 sectors
- Volatility Rank: {random.randint(1, 11)}/11 sectors

**Peer Comparison**:
- vs Peer 1: {random.uniform(-5, 8):+.1f}%
- vs Peer 2: {random.uniform(-5, 8):+.1f}%
- vs Peer 3: {random.uniform(-5, 8):+.1f}%
- vs Peer 4: {random.uniform(-5, 8):+.1f}%

**Sector Outlook**: {random.choice(['Positive', 'Neutral', 'Cautious'])}
The sector shows {'strong' if random.random() > 0.5 else 'moderate'} fundamentals with {'growing' if random.random() > 0.6 else 'stable'} demand trends.""",

            "comprehensive": lambda: f"""🚀 **Comprehensive Stock Analysis for {ticker}**

**Current Market Data**:
- Current Price: ${random.uniform(10, 500):.2f}
- Market Cap: ${random.uniform(1, 500):.1f}B
- Volume: {random.uniform(1, 50):.1f}M shares
- P/E Ratio: {random.uniform(8, 35):.1f}

**Technical Analysis**:
- RSI (14): {random.uniform(25, 75):.1f} ({random.choice(['Oversold', 'Neutral', 'Overbought'])})
- MACD: {random.uniform(-2, 4):.2f} ({random.choice(['Bullish', 'Bearish'])})
- 50-day MA: ${random.uniform(10, 500):.2f}
- 200-day MA: ${random.uniform(10, 500):.2f}

**Fundamental Metrics**:
- Revenue Growth: {random.uniform(-10, 25):+.1f}% (YoY)
- Profit Margin: {random.uniform(5, 30):.1f}%
- ROE: {random.uniform(8, 25):.1f}%
- Debt-to-Equity: {random.uniform(0.1, 2.0):.2f}

**Market Sentiment**:
- Analyst Rating: {random.choice(['Strong Buy', 'Buy', 'Hold', 'Sell'])}
- Price Target: ${random.uniform(15, 600):.2f}
- Sentiment Score: {random.uniform(0.3, 0.9):.2f} ({random.choice(['Positive', 'Neutral', 'Negative'])})

**Investment Recommendation**:
**Action**: {random.choice(['BUY', 'HOLD', 'SELL'])}
**Confidence**: {random.uniform(60, 95):.0f}%
**Risk Level**: {random.choice(['Low', 'Medium', 'High'])}

**Key Factors**:
• {'Strong' if random.random() > 0.5 else 'Moderate'} financial performance
• {'Positive' if random.random() > 0.4 else 'Mixed'} market sentiment
• {'Bullish' if random.random() > 0.5 else 'Bearish'} technical indicators

⚠️ **Disclaimer**: This analysis is for informational purposes only and should not be considered as personalized financial advice."""
        }
        
        # Get the result and call it if it's a function
        result_func = mock_results.get(analysis_type, lambda: f"Analysis completed for {ticker} ({analysis_type})")
        result_content = result_func() if callable(result_func) else result_func
        
        return {
            "thread_id": "test-thread-123",
            "messages": [
                {
                    "role": "assistant",
                    "content": result_content
                }
            ],
            "status": "success"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.post("/analyze-image")
async def analyze_chart_image(file: UploadFile = File(...)):
    """
    Mock image analysis endpoint
    """
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Mock analysis result
        return {
            "success": True,
            "analysis": {
                "confidence_score": 0.87,
                "patterns_detected": [
                    {
                        "type": "Ascending Triangle",
                        "confidence": 0.82,
                        "bullish": True,
                        "completion": 0.7
                    }
                ],
                "technical_indicators": {
                    "rsi": 58.2,
                    "sma_5": 175.43,
                    "sma_20": 172.15,
                    "macd": 3.28,
                    "current_price": 175.43
                },
                "support_levels": [168.50, 172.20, 174.80],
                "resistance_levels": [178.90, 182.45, 185.20],
                "price_targets": {
                    "upside_target": 189.46,
                    "extended_target": 201.74
                },
                "risk_levels": {
                    "stop_loss": 166.66,
                    "risk_level": "medium"
                },
                "recommendations": {
                    "action": "BUY",
                    "confidence": 0.75,
                    "reasoning": ["Bullish patterns detected", "RSI neutral", "MACD bullish crossover"]
                }
            },
            "metadata": {
                "filename": file.filename,
                "file_size": file.size,
                "content_type": file.content_type
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Image analysis failed: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting Simple Test Server on port 8125...")
    uvicorn.run(app, host="0.0.0.0", port=8125)
