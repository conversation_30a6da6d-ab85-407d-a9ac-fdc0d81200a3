#!/bin/bash

# FinanceGPT Pro - Production Deployment Script
# This script handles the complete deployment process

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="financegpt-pro"
COMPOSE_FILE="docker-compose.production.yml"
ENV_FILE=".env.production"
BACKUP_DIR="/opt/backups/financegpt"
LOG_FILE="/var/log/financegpt-deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please install Docker first."
    fi
    
    # Check if Docker Compose is installed
    if ! command -v docker-compose &> /dev/null; then
        error "Docker Compose is not installed. Please install Docker Compose first."
    fi
    
    # Check if environment file exists
    if [ ! -f "$ENV_FILE" ]; then
        error "Environment file $ENV_FILE not found. Please create it from .env.production template."
    fi
    
    # Check if compose file exists
    if [ ! -f "$COMPOSE_FILE" ]; then
        error "Docker Compose file $COMPOSE_FILE not found."
    fi
    
    success "Prerequisites check passed"
}

# Create backup
create_backup() {
    log "Creating backup..."
    
    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"
    
    # Backup database
    if docker-compose -f "$COMPOSE_FILE" ps postgres | grep -q "Up"; then
        log "Backing up database..."
        docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump -U financegpt financegpt > "$BACKUP_DIR/db_backup_$(date +%Y%m%d_%H%M%S).sql"
        success "Database backup created"
    else
        warning "Database container not running, skipping database backup"
    fi
    
    # Backup application data
    if [ -d "./backend/data" ]; then
        log "Backing up application data..."
        tar -czf "$BACKUP_DIR/app_data_backup_$(date +%Y%m%d_%H%M%S).tar.gz" ./backend/data
        success "Application data backup created"
    fi
}

# Pull latest images
pull_images() {
    log "Pulling latest Docker images..."
    docker-compose -f "$COMPOSE_FILE" pull
    success "Images pulled successfully"
}

# Deploy services
deploy_services() {
    log "Deploying services..."
    
    # Start database and cache first
    log "Starting database and cache services..."
    docker-compose -f "$COMPOSE_FILE" up -d postgres redis
    
    # Wait for database to be ready
    log "Waiting for database to be ready..."
    timeout=60
    while ! docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_isready -U financegpt; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            error "Database failed to start within 60 seconds"
        fi
    done
    success "Database is ready"
    
    # Start backend
    log "Starting backend service..."
    docker-compose -f "$COMPOSE_FILE" up -d backend
    
    # Wait for backend to be ready
    log "Waiting for backend to be ready..."
    timeout=60
    while ! curl -f http://localhost:8000/health &> /dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            error "Backend failed to start within 60 seconds"
        fi
    done
    success "Backend is ready"
    
    # Start frontend
    log "Starting frontend service..."
    docker-compose -f "$COMPOSE_FILE" up -d frontend
    
    # Wait for frontend to be ready
    log "Waiting for frontend to be ready..."
    timeout=60
    while ! curl -f http://localhost:3000/health &> /dev/null; do
        sleep 2
        timeout=$((timeout - 2))
        if [ $timeout -le 0 ]; then
            error "Frontend failed to start within 60 seconds"
        fi
    done
    success "Frontend is ready"
}

# Health check
health_check() {
    log "Performing health checks..."
    
    # Check backend health
    if curl -f http://localhost:8000/health &> /dev/null; then
        success "Backend health check passed"
    else
        error "Backend health check failed"
    fi
    
    # Check frontend health
    if curl -f http://localhost:3000/health &> /dev/null; then
        success "Frontend health check passed"
    else
        error "Frontend health check failed"
    fi
    
    # Check database connection
    if docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_isready -U financegpt &> /dev/null; then
        success "Database health check passed"
    else
        error "Database health check failed"
    fi
    
    # Check Redis connection
    if docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli ping | grep -q "PONG"; then
        success "Redis health check passed"
    else
        error "Redis health check failed"
    fi
}

# Cleanup old images and containers
cleanup() {
    log "Cleaning up old images and containers..."
    docker system prune -f
    docker volume prune -f
    success "Cleanup completed"
}

# Show deployment status
show_status() {
    log "Deployment Status:"
    echo "===================="
    docker-compose -f "$COMPOSE_FILE" ps
    echo "===================="
    
    log "Service URLs:"
    echo "Frontend: http://localhost:3000"
    echo "Backend API: http://localhost:8000"
    echo "API Documentation: http://localhost:8000/docs"
    echo "===================="
}

# Main deployment process
main() {
    log "Starting FinanceGPT Pro deployment..."
    
    # Load environment variables
    if [ -f "$ENV_FILE" ]; then
        export $(cat "$ENV_FILE" | grep -v '^#' | xargs)
    fi
    
    check_prerequisites
    create_backup
    pull_images
    deploy_services
    health_check
    cleanup
    show_status
    
    success "🎉 FinanceGPT Pro deployment completed successfully!"
    log "Deployment finished at $(date)"
}

# Handle script arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "backup")
        create_backup
        ;;
    "health")
        health_check
        ;;
    "status")
        show_status
        ;;
    "cleanup")
        cleanup
        ;;
    *)
        echo "Usage: $0 {deploy|backup|health|status|cleanup}"
        echo "  deploy  - Full deployment (default)"
        echo "  backup  - Create backup only"
        echo "  health  - Run health checks only"
        echo "  status  - Show deployment status"
        echo "  cleanup - Clean up old images and containers"
        exit 1
        ;;
esac
