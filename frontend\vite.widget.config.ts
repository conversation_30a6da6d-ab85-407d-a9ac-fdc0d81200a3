import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

export default defineConfig({
  plugins: [react()],
  
  // Widget-specific build configuration
  build: {
    outDir: 'dist-widget',
    emptyOutDir: true,
    
    rollupOptions: {
      input: {
        widget: resolve(__dirname, 'widget.html'),
        'widget-bundle': resolve(__dirname, 'src/widget.tsx')
      },
      
      output: {
        // Create separate bundles for different integration methods
        entryFileNames: (chunkInfo) => {
          if (chunkInfo.name === 'widget-bundle') {
            return 'finance-gpt-widget.js';
          }
          return '[name]-[hash].js';
        },
        
        chunkFileNames: 'chunks/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]',
        
        // Global variable for script tag integration
        format: 'umd',
        name: 'FinanceGPTWidget',
        
        globals: {
          react: 'React',
          'react-dom': 'ReactDOM'
        }
      },
      
      // External dependencies for UMD build
      external: ['react', 'react-dom']
    },
    
    // Optimize for widget usage
    target: 'es2015',
    minify: 'terser',
    
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  },
  
  // Development server for widget testing
  server: {
    port: 5174,
    open: '/widget.html'
  },
  
  // Define environment variables for widget mode
  define: {
    __WIDGET_MODE__: true,
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development')
  },
  
  // Resolve configuration
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  
  // CSS configuration for widget
  css: {
    modules: {
      localsConvention: 'camelCase'
    },
    postcss: {
      plugins: []
    }
  }
});
