import React from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { SUBSCRIPTION_TIERS } from '../types/subscription';
import { useAuth } from '../contexts/AuthContext';

interface PricingPageProps {
  onUpgrade?: () => void;
  onUpgradeToProDemo?: () => void;
}

export const PricingPage: React.FC<PricingPageProps> = ({ onUpgrade, onUpgradeToProDemo }) => {
  const { user } = useAuth();
  const currentTier = user?.subscription.tier || 'free';

  const handleUpgrade = async (tierId: string) => {
    if (tierId === 'pro') {
      // For demo purposes, upgrade immediately
      if (onUpgradeToProDemo) {
        onUpgradeToProDemo();
        alert('🎉 Upgraded to Pro! You now have access to all premium features.');
      } else {
        // In a real app, integrate with Stripe
        alert('Redirecting to payment processor...');
        onUpgrade?.();
      }
    }
  };

  const FeatureList = ({ features, limits }: {
    features: any,
    limits: any
  }) => (
    <div className="space-y-3">
      <div className="space-y-2">
        <div className="flex items-center gap-2">
          <span className={features.basicAnalysis ? "text-green-400" : "text-gray-500"}>
            {features.basicAnalysis ? "✅" : "❌"}
          </span>
          <span className="text-sm text-gray-300">Basic Stock Analysis</span>
        </div>
        
        <div className="flex items-center gap-2">
          <span className={features.technicalAnalysis ? "text-green-400" : "text-gray-500"}>
            {features.technicalAnalysis ? "✅" : "❌"}
          </span>
          <span className="text-sm text-gray-300">Technical Analysis (RSI, MACD)</span>
        </div>
        
        <div className="flex items-center gap-2">
          <span className={features.sentimentAnalysis ? "text-green-400" : "text-gray-500"}>
            {features.sentimentAnalysis ? "✅" : "❌"}
          </span>
          <span className="text-sm text-gray-300">News Sentiment Analysis</span>
        </div>
        
        <div className="flex items-center gap-2">
          <span className={features.imageAnalysis ? "text-green-400" : "text-gray-500"}>
            {features.imageAnalysis ? "✅" : "❌"}
          </span>
          <span className="text-sm text-gray-300">Chart Image Analysis</span>
        </div>
        
        <div className="flex items-center gap-2">
          <span className={features.comprehensiveReports ? "text-green-400" : "text-gray-500"}>
            {features.comprehensiveReports ? "✅" : "❌"}
          </span>
          <span className="text-sm text-gray-300">Comprehensive Reports</span>
        </div>

        <div className="flex items-center gap-2">
          <span className={features.exportReports ? "text-green-400" : "text-gray-500"}>
            {features.exportReports ? "✅" : "❌"}
          </span>
          <span className="text-sm text-gray-300">Export Reports (PDF/CSV)</span>
        </div>
      </div>
      
      <div className="pt-4 border-t border-white/20">
        <h4 className="text-sm font-semibold text-white mb-2">Usage Limits</h4>
        <div className="space-y-1 text-xs text-gray-400">
          <div>Daily Queries: {limits.dailyQueries === -1 ? 'Unlimited' : limits.dailyQueries}</div>
          <div>Monthly Tokens: {limits.monthlyTokens.toLocaleString()}</div>
          <div>Image Analysis: {limits.imageAnalysisPerDay === 0 ? 'None' : limits.imageAnalysisPerDay}/day</div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* Header */}
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold text-white mb-4">
          Choose Your Plan
        </h1>
        <p className="text-xl text-gray-300 max-w-2xl mx-auto">
          Get the financial insights you need with our flexible pricing options
        </p>
      </div>

      {/* Pricing Cards */}
      <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        {/* Free Tier */}
        <Card className="relative p-8 bg-white/5 backdrop-blur-sm border border-white/20">
          <div className="text-center mb-6">
            <h3 className="text-2xl font-bold text-white mb-2">
              {SUBSCRIPTION_TIERS.free.name}
            </h3>
            <div className="text-4xl font-bold text-white mb-2">
              ${SUBSCRIPTION_TIERS.free.price}
              <span className="text-lg font-normal text-gray-400">/month</span>
            </div>
            <p className="text-gray-400">Perfect for getting started</p>
          </div>

          <FeatureList
            features={SUBSCRIPTION_TIERS.free.features}
            limits={SUBSCRIPTION_TIERS.free.limits}
          />

          <div className="mt-8">
            <Button 
              className="w-full bg-gray-600 hover:bg-gray-700 text-white"
              disabled={currentTier === 'free'}
            >
              {currentTier === 'free' ? 'Current Plan' : 'Downgrade to Free'}
            </Button>
          </div>
        </Card>

        {/* Pro Tier */}
        <Card className="relative p-8 bg-gradient-to-br from-blue-600/20 to-purple-600/20 backdrop-blur-sm border border-blue-500/50">
          {/* Popular Badge */}
          <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
            <Badge className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-4 py-1">
              MOST POPULAR
            </Badge>
          </div>

          <div className="text-center mb-6">
            <h3 className="text-2xl font-bold text-white mb-2">
              {SUBSCRIPTION_TIERS.pro.name}
            </h3>
            <div className="text-4xl font-bold text-white mb-2">
              ${SUBSCRIPTION_TIERS.pro.price}
              <span className="text-lg font-normal text-gray-400">/month</span>
            </div>
            <p className="text-gray-400">For serious investors</p>
          </div>

          <FeatureList
            features={SUBSCRIPTION_TIERS.pro.features}
            limits={SUBSCRIPTION_TIERS.pro.limits}
          />

          <div className="mt-8">
            <Button 
              className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
              onClick={() => handleUpgrade('pro')}
              disabled={currentTier === 'pro'}
            >
              {currentTier === 'pro' ? 'Current Plan' : 'Upgrade to Pro'}
            </Button>
          </div>
        </Card>
      </div>

      {/* FAQ Section */}
      <div className="mt-16 max-w-3xl mx-auto">
        <h2 className="text-2xl font-bold text-white text-center mb-8">
          Frequently Asked Questions
        </h2>
        
        <div className="space-y-6">
          <Card className="p-6 bg-white/5 backdrop-blur-sm border border-white/20">
            <h3 className="text-lg font-semibold text-white mb-2">
              Can I switch plans anytime?
            </h3>
            <p className="text-gray-300">
              Yes! You can upgrade or downgrade your plan at any time. Changes take effect immediately.
            </p>
          </Card>
          
          <Card className="p-6 bg-white/5 backdrop-blur-sm border border-white/20">
            <h3 className="text-lg font-semibold text-white mb-2">
              What happens if I exceed my limits?
            </h3>
            <p className="text-gray-300">
              Free users will be prompted to upgrade when limits are reached. Pro users have generous limits that reset monthly.
            </p>
          </Card>
          
          <Card className="p-6 bg-white/5 backdrop-blur-sm border border-white/20">
            <h3 className="text-lg font-semibold text-white mb-2">
              Is there a free trial for Pro?
            </h3>
            <p className="text-gray-300">
              New users get full access to Pro features for the first 7 days, then automatically switch to the Free plan unless upgraded.
            </p>
          </Card>
        </div>
      </div>
    </div>
  );
};
