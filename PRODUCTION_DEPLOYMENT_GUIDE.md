# 🚀 FinanceGPT Pro - Production Deployment Guide

## 📋 **Prerequisites**

### **System Requirements**
- **OS**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **RAM**: Minimum 8GB, Recommended 16GB+
- **CPU**: Minimum 4 cores, Recommended 8+ cores
- **Storage**: Minimum 100GB SSD
- **Network**: Stable internet connection with public IP

### **Software Requirements**
- **Docker**: Version 20.10+
- **Docker Compose**: Version 2.0+
- **Git**: For code deployment
- **Nginx**: For reverse proxy (optional)
- **SSL Certificate**: For HTTPS (recommended)

---

## 🔧 **Quick Deployment**

### **1. Clone Repository**
```bash
git clone https://github.com/yourusername/financegpt-pro.git
cd financegpt-pro
```

### **2. Configure Environment**
```bash
# Copy production environment template
cp .env.production .env

# Edit environment variables
nano .env
```

**Required Environment Variables:**
```bash
# API Keys (REQUIRED)
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_SEARCH_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_CSE_ID=your_google_search_cse_id_here
STOCKS_API_KEY=your_alpha_vantage_api_key_here

# Security (CHANGE THESE!)
POSTGRES_PASSWORD=your_secure_database_password
REDIS_PASSWORD=your_secure_redis_password
JWT_SECRET=your_super_secure_jwt_secret

# Domain Configuration
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
REACT_APP_API_URL=https://api.yourdomain.com
```

### **3. Deploy with Script**
```bash
# Make deployment script executable
chmod +x scripts/deploy.sh

# Run deployment
sudo ./scripts/deploy.sh
```

### **4. Verify Deployment**
```bash
# Check service status
docker-compose -f docker-compose.production.yml ps

# Test health endpoints
curl http://localhost:8000/health
curl http://localhost:3000/health
```

---

## 🏗️ **Manual Deployment Steps**

### **Step 1: Prepare Server**
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Create application directory
sudo mkdir -p /opt/financegpt-pro
sudo chown $USER:$USER /opt/financegpt-pro
cd /opt/financegpt-pro
```

### **Step 2: Setup Application**
```bash
# Clone repository
git clone https://github.com/yourusername/financegpt-pro.git .

# Configure environment
cp .env.production .env
nano .env  # Edit with your values

# Create necessary directories
mkdir -p backend/logs backend/data database/backups
```

### **Step 3: Build and Deploy**
```bash
# Build images
docker-compose -f docker-compose.production.yml build

# Start services
docker-compose -f docker-compose.production.yml up -d

# Check logs
docker-compose -f docker-compose.production.yml logs -f
```

---

## 🔒 **Security Configuration**

### **SSL/TLS Setup**
```bash
# Install Certbot for Let's Encrypt
sudo apt install certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### **Firewall Configuration**
```bash
# Configure UFW
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# For development (remove in production)
sudo ufw allow 3000/tcp
sudo ufw allow 8000/tcp
```

### **Database Security**
```bash
# Change default passwords
# Edit .env file with secure passwords

# Backup database regularly
docker-compose -f docker-compose.production.yml exec postgres pg_dump -U financegpt financegpt > backup.sql
```

---

## 📊 **Monitoring & Logging**

### **Health Monitoring**
```bash
# Check application health
curl https://yourdomain.com/api/health

# Monitor containers
docker stats

# View logs
docker-compose -f docker-compose.production.yml logs -f backend
docker-compose -f docker-compose.production.yml logs -f frontend
```

### **Log Management**
```bash
# Configure log rotation
sudo nano /etc/logrotate.d/financegpt

# Content:
/opt/financegpt-pro/backend/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    copytruncate
}
```

---

## 🔄 **CI/CD Pipeline**

### **GitHub Actions Setup**
1. **Repository Secrets** (Settings → Secrets):
   ```
   PRODUCTION_HOST=your.server.ip
   PRODUCTION_USER=deploy
   PRODUCTION_SSH_KEY=your_private_key
   PRODUCTION_URL=https://yourdomain.com
   SLACK_WEBHOOK=your_slack_webhook_url
   ```

2. **Deploy Key Setup**:
   ```bash
   # On server
   ssh-keygen -t rsa -b 4096 -C "deploy@financegpt"
   cat ~/.ssh/id_rsa.pub >> ~/.ssh/authorized_keys
   
   # Add private key to GitHub secrets
   cat ~/.ssh/id_rsa
   ```

### **Automated Deployment**
- **Push to `main`** → Automatic deployment
- **Pull Request** → Run tests only
- **Health checks** after deployment
- **Slack notifications** on success/failure

---

## 🚨 **Troubleshooting**

### **Common Issues**

#### **1. Container Won't Start**
```bash
# Check logs
docker-compose -f docker-compose.production.yml logs container_name

# Check resource usage
docker stats

# Restart specific service
docker-compose -f docker-compose.production.yml restart backend
```

#### **2. Database Connection Issues**
```bash
# Check database status
docker-compose -f docker-compose.production.yml exec postgres pg_isready

# Reset database
docker-compose -f docker-compose.production.yml down
docker volume rm financegpt-pro_postgres_data
docker-compose -f docker-compose.production.yml up -d
```

#### **3. API Key Issues**
```bash
# Verify environment variables
docker-compose -f docker-compose.production.yml exec backend env | grep API

# Test API connectivity
docker-compose -f docker-compose.production.yml exec backend curl -f "https://api.example.com/test"
```

### **Performance Optimization**
```bash
# Increase worker processes
# Edit docker-compose.production.yml:
# command: ["uvicorn", "src.agent.app:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "8"]

# Enable Redis caching
# Ensure REDIS_URL is configured in .env

# Database optimization
docker-compose -f docker-compose.production.yml exec postgres psql -U financegpt -c "VACUUM ANALYZE;"
```

---

## 📈 **Scaling**

### **Horizontal Scaling**
```yaml
# docker-compose.production.yml
backend:
  deploy:
    replicas: 3
  
frontend:
  deploy:
    replicas: 2
```

### **Load Balancer Setup**
```nginx
# /etc/nginx/sites-available/financegpt
upstream backend {
    server localhost:8000;
    server localhost:8001;
    server localhost:8002;
}

upstream frontend {
    server localhost:3000;
    server localhost:3001;
}
```

---

## 🎯 **Production Checklist**

### **Pre-Deployment**
- [ ] Environment variables configured
- [ ] SSL certificates obtained
- [ ] Database backups tested
- [ ] API keys validated
- [ ] Security headers configured
- [ ] Monitoring setup
- [ ] Log rotation configured

### **Post-Deployment**
- [ ] Health checks passing
- [ ] All services running
- [ ] Database migrations applied
- [ ] SSL working correctly
- [ ] API endpoints responding
- [ ] Frontend loading properly
- [ ] Monitoring alerts configured

---

## 🆘 **Support**

### **Emergency Contacts**
- **System Admin**: <EMAIL>
- **DevOps Team**: <EMAIL>
- **On-Call**: +1-XXX-XXX-XXXX

### **Useful Commands**
```bash
# Quick status check
./scripts/deploy.sh status

# Emergency backup
./scripts/deploy.sh backup

# Health check
./scripts/deploy.sh health

# Full redeployment
./scripts/deploy.sh deploy
```

---

**🎉 Your FinanceGPT Pro is now ready for production!**

Access your application at: **https://yourdomain.com**
