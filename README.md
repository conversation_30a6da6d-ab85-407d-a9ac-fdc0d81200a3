# 🚀 FinanceGPT - AI-Powered Financial Analysis WebApp

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![React](https://img.shields.io/badge/React-18.0-blue.svg)](https://reactjs.org/)
[![TypeScript](https://img.shields.io/badge/TypeScript-5.0-blue.svg)](https://www.typescriptlang.org/)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.104-green.svg)](https://fastapi.tiangolo.com/)
[![Python](https://img.shields.io/badge/Python-3.11-blue.svg)](https://www.python.org/)

A comprehensive financial analysis and portfolio tracking web application powered by Google Gemini AI. Features real-time stock analysis, personalized investment advice, portfolio tracking, and an intuitive chat interface.

## ✨ Features

### 🎯 **Core Features**
- **AI-Powered Stock Analysis** - Multiple analysis types (Quick, Technical, Fundamental, Comprehensive)
- **Portfolio Tracking** - Real-time portfolio monitoring with performance analytics
- **Personalized Chat** - AI chatbot for investment questions and advice
- **User Profiling** - Customized experience based on risk tolerance and goals
- **Token System** - Basic and Pro tiers with usage-based pricing

### 🎨 **UI/UX Features**
- **Responsive Design** - Works seamlessly on desktop, tablet, and mobile
- **Dark Theme** - Professional dark theme with proper contrast
- **Clean Interface** - Minimal, clutter-free design focused on usability
- **Real-time Updates** - Live data and instant analysis results

### 🔧 **Technical Features**
- **Widget Integration** - Embeddable in other applications
- **RESTful API** - Complete backend API for all functionality
- **Authentication** - Secure JWT-based user authentication
- **Database Integration** - PostgreSQL with SQLAlchemy ORM
- **Containerized** - Docker support for easy deployment

## 🚀 Quick Start

### Prerequisites

Make sure you have the following installed:
- **Node.js** 18+ and npm
- **Python** 3.11+
- **PostgreSQL** (or use SQLite for development)
- **Git**

### 1. Clone the Repository

```bash
git clone https://github.com/your-username/financegpt-webapp.git
cd financegpt-webapp
```

### 2. Environment Setup

#### Backend Environment
```bash
cd backend
cp .env.example .env
```

Edit `.env` file with your configuration:
```env
# API Keys
GEMINI_API_KEY=your_gemini_api_key_here
FINANCIAL_DATA_API_KEY=your_financial_api_key

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/financegpt
# Or for development with SQLite:
# DATABASE_URL=sqlite:///./financegpt.db

# Security
JWT_SECRET_KEY=your-super-secret-jwt-key-here
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Environment
ENVIRONMENT=development
DEBUG=true
```

#### Frontend Environment
```bash
cd ../frontend
cp .env.example .env.local
```

Edit `.env.local` file:
```env
REACT_APP_API_URL=http://localhost:8000
REACT_APP_ENVIRONMENT=development
```

### 3. Backend Setup

```bash
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run database migrations (if using PostgreSQL)
python -m alembic upgrade head

# Start the backend server
uvicorn src.agent.app:app --reload --host 0.0.0.0 --port 8000
```

The backend will be available at: `http://localhost:8000`

### 4. Frontend Setup

```bash
cd frontend

# Install dependencies
npm install

# Start the development server
npm start
```

The frontend will be available at: `http://localhost:3000`

## 📖 Usage Guide

### Getting Started

1. **Open the application** at `http://localhost:3000`
2. **Create an account** or sign in
3. **Complete the onboarding questionnaire** (3 quick questions about your investment profile)
4. **Start analyzing stocks** by entering a ticker symbol (e.g., AAPL, MSFT)

### Stock Analysis

1. **Enter a stock ticker** in the search box
2. **Choose analysis type**:
   - **Quick Price Check** (Free) - Basic price and change info
   - **Technical Analysis** (25 tokens) - Charts, indicators, trends
   - **Fundamental Analysis** (50 tokens) - Company financials, ratios
   - **Comprehensive Report** (100 tokens) - Complete analysis with AI insights
3. **Click "Analyze"** to get AI-powered insights

### Portfolio Tracking

1. **Navigate to Portfolio** section
2. **View Overview** - See your portfolio allocation and performance
3. **Check Performance** - Detailed analytics and charts
4. **Monitor Holdings** - Track individual stock performance

### AI Chat Assistant

1. **Click on the chat interface**
2. **Ask questions** about stocks, investing strategies, or your portfolio
3. **Get personalized advice** based on your risk profile and goals

## 🛠️ Development

### Project Structure

```
financegpt-webapp/
├── frontend/                 # React TypeScript frontend
│   ├── src/
│   │   ├── components/      # React components
│   │   ├── services/        # API services
│   │   ├── types/          # TypeScript types
│   │   └── utils/          # Utility functions
│   └── public/             # Static assets
├── backend/                 # Python FastAPI backend
│   ├── src/
│   │   ├── agent/          # Core application logic
│   │   ├── api/            # API endpoints
│   │   ├── models/         # Database models
│   │   └── services/       # Business logic services
│   └── tests/              # Backend tests
├── database/               # Database schemas and migrations
├── docker-compose.yml      # Docker configuration
└── docs/                   # Documentation
```

### Available Scripts

#### Frontend
```bash
npm start          # Start development server
npm test           # Run tests
npm run build      # Build for production
npm run lint       # Run ESLint
```

#### Backend
```bash
uvicorn src.agent.app:app --reload    # Start development server
pytest                                # Run tests
python -m pytest --cov=src          # Run tests with coverage
```

### API Documentation

When the backend is running, visit:
- **Swagger UI**: `http://localhost:8000/docs`
- **ReDoc**: `http://localhost:8000/redoc`

## 🐳 Docker Deployment

### Development with Docker Compose

```bash
# Build and start all services
docker-compose up --build

# Run in background
docker-compose up -d

# Stop services
docker-compose down
```

### Production Deployment

```bash
# Use production configuration
docker-compose -f docker-compose.production.yml up -d
```

## 🔑 API Keys Setup

### Required API Keys

1. **Google Gemini AI API Key**
   - Go to [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Add to your `.env` file as `GEMINI_API_KEY`

2. **Financial Data API** (Optional - for real-time data)
   - Choose from: Alpha Vantage, Yahoo Finance, or IEX Cloud
   - Get your API key from the provider
   - Add to your `.env` file as `FINANCIAL_DATA_API_KEY`

## 🔒 Security

### Important Security Notes

- **Never commit API keys** to version control
- **Use environment variables** for all sensitive configuration
- **Enable HTTPS** in production
- **Use strong JWT secrets** (generate with `openssl rand -hex 32`)
- **Regularly update dependencies** to patch security vulnerabilities

### Production Security Checklist

- [ ] Set strong `JWT_SECRET_KEY`
- [ ] Use HTTPS with valid SSL certificates
- [ ] Configure CORS properly
- [ ] Set up rate limiting
- [ ] Enable database connection encryption
- [ ] Use environment variables for all secrets
- [ ] Set up monitoring and logging

## 🧪 Testing

### Running Tests

```bash
# Frontend tests
cd frontend
npm test

# Backend tests
cd backend
pytest

# Run with coverage
pytest --cov=src --cov-report=html
```

### Test Coverage

The project includes:
- **Unit tests** for components and services
- **Integration tests** for API endpoints
- **E2E tests** for complete user workflows

## 📚 Documentation

For comprehensive documentation, see:
- **[DOCUMENTATION.md](./DOCUMENTATION.md)** - Complete technical documentation
- **[INTEGRATION.md](./frontend/INTEGRATION.md)** - Integration guide for embedding
- **[API Documentation](http://localhost:8000/docs)** - Interactive API docs (when backend is running)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/new-feature`
3. Make your changes and add tests
4. Commit your changes: `git commit -m "Add new feature"`
5. Push to the branch: `git push origin feature/new-feature`
6. Submit a pull request

## 🆘 Support

If you encounter any issues:

1. **Check the logs** in the browser console and backend terminal
2. **Verify environment variables** are set correctly
3. **Ensure all dependencies** are installed
4. **Check the troubleshooting section** in [DOCUMENTATION.md](./DOCUMENTATION.md)

For additional help, please open an issue on GitHub.

## 🚀 Key Features Highlights

### 💡 **Smart Analysis Options**
- **Quick Price Check** - Instant stock price and basic info
- **Technical Analysis** - RSI, MACD, moving averages, chart patterns
- **Fundamental Analysis** - P/E ratios, earnings, financial health
- **Comprehensive Report** - Complete AI-powered analysis with recommendations

### 📊 **Portfolio Management**
- **Real-time Tracking** - Live portfolio value and performance
- **Performance Analytics** - Detailed charts and metrics
- **Risk Assessment** - Based on your personal risk profile
- **Clean Interface** - Focused on monitoring, not cluttered management

### 🤖 **AI Chat Assistant**
- **Personalized Advice** - Based on your investment profile
- **Market Insights** - Real-time market analysis and trends
- **Educational Support** - Learn about investing concepts
- **Portfolio Questions** - Ask about your specific holdings

### 🎯 **User Experience**
- **Beginner Friendly** - Onboarding questionnaire and guidance
- **Professional Interface** - Clean, dark theme design
- **Responsive Design** - Works on all devices
- **Token System** - Fair usage with Basic and Pro tiers

## 🔧 Technologies Used

### Frontend
- **React 18** with TypeScript for type safety
- **Tailwind CSS** for responsive styling
- **Lucide React** for consistent icons
- **Custom UI Components** for cohesive design

### Backend
- **Python FastAPI** for high-performance API
- **SQLAlchemy** for database operations
- **Google Gemini AI** for intelligent analysis
- **JWT Authentication** for secure access

### Infrastructure
- **PostgreSQL** for production database
- **Docker** for containerized deployment
- **GitHub Actions** for CI/CD
- **Environment-based Configuration** for different stages

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

**Built with ❤️ using React, TypeScript, Python, and Google Gemini AI**

### 🌟 **Ready to Get Started?**

1. **Clone the repository**
2. **Set up your environment variables**
3. **Install dependencies**
4. **Run the development servers**
5. **Start analyzing stocks!**

For detailed setup instructions, see the [Quick Start](#-quick-start) section above.