"""
Portfolio Management Service for FinanceGPT Pro
Handles portfolio tracking, performance analysis, and optimization
"""

from typing import Dict, List, Any, Optional, Tuple
import logging
from datetime import datetime, timedelta
import random

from ..models.user_profile_models import (
    Portfolio,
    PortfolioHolding,
    UserProfile,
    user_profile_manager,
    RiskTolerance
)

logger = logging.getLogger(__name__)

class PortfolioService:
    """Service for portfolio management and analysis"""
    
    def __init__(self):
        self.sector_mappings = {
            "AAPL": "Technology",
            "MSFT": "Technology", 
            "GOOGL": "Technology",
            "AMZN": "Consumer Discretionary",
            "TSLA": "Consumer Discretionary",
            "NVDA": "Technology",
            "META": "Technology",
            "JPM": "Financial Services",
            "JNJ": "Healthcare",
            "V": "Financial Services",
            "WMT": "Consumer Staples",
            "PG": "Consumer Staples",
            "UNH": "Healthcare",
            "HD": "Consumer Discretionary",
            "MA": "Financial Services"
        }
        
        self.company_names = {
            "AAPL": "Apple Inc.",
            "MSFT": "Microsoft Corporation",
            "GOOGL": "Alphabet Inc.",
            "AMZN": "Amazon.com Inc.",
            "TSLA": "Tesla Inc.",
            "NVDA": "NVIDIA Corporation",
            "META": "Meta Platforms Inc.",
            "JPM": "JPMorgan Chase & Co.",
            "JNJ": "Johnson & Johnson",
            "V": "Visa Inc.",
            "WMT": "Walmart Inc.",
            "PG": "Procter & Gamble Co.",
            "UNH": "UnitedHealth Group Inc.",
            "HD": "The Home Depot Inc.",
            "MA": "Mastercard Inc."
        }
    
    def create_portfolio(self, user_id: str, name: str = "My Portfolio") -> Portfolio:
        """Create a new portfolio for user"""
        profile = user_profile_manager.get_profile(user_id)
        if not profile:
            raise ValueError("User profile not found")
        
        portfolio = Portfolio(
            user_id=user_id,
            name=name
        )
        
        profile.portfolios.append(portfolio)
        profile.last_updated = datetime.now()
        
        logger.info(f"Created portfolio '{name}' for user {user_id}")
        return portfolio
    
    def add_holding(
        self, 
        user_id: str, 
        portfolio_id: str, 
        ticker: str, 
        shares: float, 
        purchase_price: float
    ) -> PortfolioHolding:
        """Add a holding to portfolio"""
        portfolio = self._get_portfolio(user_id, portfolio_id)
        if not portfolio:
            raise ValueError("Portfolio not found")
        
        # Get company info
        company_name = self.company_names.get(ticker, ticker)
        sector = self.sector_mappings.get(ticker, "Unknown")
        
        # Check if holding already exists
        existing_holding = None
        for holding in portfolio.holdings:
            if holding.ticker == ticker:
                existing_holding = holding
                break
        
        if existing_holding:
            # Update existing holding (average cost calculation)
            total_shares = existing_holding.shares + shares
            total_cost = (existing_holding.shares * existing_holding.average_cost) + (shares * purchase_price)
            new_average_cost = total_cost / total_shares
            
            existing_holding.shares = total_shares
            existing_holding.average_cost = new_average_cost
            existing_holding.last_updated = datetime.now()
            
            logger.info(f"Updated holding {ticker} for user {user_id}: {total_shares} shares at avg cost ${new_average_cost:.2f}")
            return existing_holding
        else:
            # Create new holding
            holding = PortfolioHolding(
                ticker=ticker,
                company_name=company_name,
                shares=shares,
                average_cost=purchase_price,
                current_price=purchase_price,  # Will be updated by price service
                sector=sector
            )
            
            portfolio.holdings.append(holding)
            portfolio.last_updated = datetime.now()
            
            logger.info(f"Added holding {ticker} for user {user_id}: {shares} shares at ${purchase_price:.2f}")
            return holding
    
    def remove_holding(self, user_id: str, portfolio_id: str, holding_id: str) -> bool:
        """Remove a holding from portfolio"""
        portfolio = self._get_portfolio(user_id, portfolio_id)
        if not portfolio:
            return False
        
        for i, holding in enumerate(portfolio.holdings):
            if holding.holding_id == holding_id:
                removed_holding = portfolio.holdings.pop(i)
                portfolio.last_updated = datetime.now()
                logger.info(f"Removed holding {removed_holding.ticker} for user {user_id}")
                return True
        
        return False
    
    def update_holding_prices(self, user_id: str, portfolio_id: str, price_updates: Dict[str, float]) -> int:
        """Update current prices for holdings"""
        portfolio = self._get_portfolio(user_id, portfolio_id)
        if not portfolio:
            return 0
        
        updated_count = 0
        for holding in portfolio.holdings:
            if holding.ticker in price_updates:
                holding.current_price = price_updates[holding.ticker]
                holding.last_updated = datetime.now()
                updated_count += 1
        
        if updated_count > 0:
            portfolio.last_updated = datetime.now()
        
        logger.info(f"Updated {updated_count} holding prices for user {user_id}")
        return updated_count
    
    def get_portfolio_performance(self, user_id: str, portfolio_id: str) -> Dict[str, Any]:
        """Get comprehensive portfolio performance analysis"""
        portfolio = self._get_portfolio(user_id, portfolio_id)
        if not portfolio:
            raise ValueError("Portfolio not found")
        
        # Simulate some price updates for demo
        self._simulate_price_updates(portfolio)
        
        performance = {
            "portfolio_id": portfolio_id,
            "name": portfolio.name,
            "summary": {
                "total_value": portfolio.total_market_value,
                "total_cost": portfolio.total_cost_basis,
                "total_gain_loss": portfolio.total_unrealized_gain_loss,
                "total_gain_loss_percent": portfolio.total_unrealized_gain_loss_percent,
                "cash_balance": portfolio.cash_balance,
                "holdings_count": len(portfolio.holdings)
            },
            "holdings": [
                {
                    "holding_id": h.holding_id,
                    "ticker": h.ticker,
                    "company_name": h.company_name,
                    "shares": h.shares,
                    "average_cost": h.average_cost,
                    "current_price": h.current_price,
                    "market_value": h.market_value,
                    "cost_basis": h.cost_basis,
                    "gain_loss": h.unrealized_gain_loss,
                    "gain_loss_percent": h.unrealized_gain_loss_percent,
                    "sector": h.sector,
                    "weight": (h.market_value / portfolio.total_market_value * 100) if portfolio.total_market_value > 0 else 0
                }
                for h in portfolio.holdings
            ],
            "sector_allocation": portfolio.get_sector_allocation(),
            "top_performers": self._get_top_performers(portfolio),
            "bottom_performers": self._get_bottom_performers(portfolio),
            "risk_metrics": self._calculate_risk_metrics(portfolio),
            "recommendations": self._get_portfolio_recommendations(user_id, portfolio)
        }
        
        return performance
    
    def _simulate_price_updates(self, portfolio: Portfolio):
        """Simulate price updates for demo purposes"""
        for holding in portfolio.holdings:
            # Simulate price movement (±10% from average cost)
            price_change = random.uniform(-0.1, 0.1)
            holding.current_price = holding.average_cost * (1 + price_change)
    
    def _get_top_performers(self, portfolio: Portfolio, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top performing holdings"""
        performers = [
            {
                "ticker": h.ticker,
                "company_name": h.company_name,
                "gain_loss_percent": h.unrealized_gain_loss_percent,
                "gain_loss": h.unrealized_gain_loss,
                "market_value": h.market_value
            }
            for h in portfolio.holdings
        ]
        
        return sorted(performers, key=lambda x: x["gain_loss_percent"], reverse=True)[:limit]
    
    def _get_bottom_performers(self, portfolio: Portfolio, limit: int = 5) -> List[Dict[str, Any]]:
        """Get worst performing holdings"""
        performers = [
            {
                "ticker": h.ticker,
                "company_name": h.company_name,
                "gain_loss_percent": h.unrealized_gain_loss_percent,
                "gain_loss": h.unrealized_gain_loss,
                "market_value": h.market_value
            }
            for h in portfolio.holdings
        ]
        
        return sorted(performers, key=lambda x: x["gain_loss_percent"])[:limit]
    
    def _calculate_risk_metrics(self, portfolio: Portfolio) -> Dict[str, Any]:
        """Calculate portfolio risk metrics"""
        if not portfolio.holdings:
            return {}
        
        # Simplified risk calculations for demo
        sector_allocation = portfolio.get_sector_allocation()
        
        # Concentration risk (higher when portfolio is concentrated in few holdings)
        concentration_risk = "High" if len(portfolio.holdings) < 5 else "Medium" if len(portfolio.holdings) < 10 else "Low"
        
        # Sector concentration
        max_sector_weight = max(sector_allocation.values()) if sector_allocation else 0
        sector_concentration = "High" if max_sector_weight > 50 else "Medium" if max_sector_weight > 30 else "Low"
        
        # Volatility estimate (simplified)
        gain_loss_values = [h.unrealized_gain_loss_percent for h in portfolio.holdings]
        volatility = sum(abs(x) for x in gain_loss_values) / len(gain_loss_values) if gain_loss_values else 0
        
        return {
            "concentration_risk": concentration_risk,
            "sector_concentration": sector_concentration,
            "estimated_volatility": volatility,
            "diversification_score": min(100, len(portfolio.holdings) * 10),  # Simple score
            "largest_holding_weight": max([h.market_value / portfolio.total_market_value * 100 for h in portfolio.holdings]) if portfolio.holdings else 0
        }
    
    def _get_portfolio_recommendations(self, user_id: str, portfolio: Portfolio) -> List[str]:
        """Get portfolio optimization recommendations"""
        recommendations = []
        
        if not portfolio.holdings:
            recommendations.append("Start building your portfolio by adding some diversified holdings")
            return recommendations
        
        # Check diversification
        if len(portfolio.holdings) < 5:
            recommendations.append("Consider adding more holdings to improve diversification")
        
        # Check sector concentration
        sector_allocation = portfolio.get_sector_allocation()
        if sector_allocation:
            max_sector = max(sector_allocation, key=sector_allocation.get)
            max_weight = sector_allocation[max_sector]
            
            if max_weight > 50:
                recommendations.append(f"Consider reducing exposure to {max_sector} sector (currently {max_weight:.1f}%)")
        
        # Check individual holding concentration
        for holding in portfolio.holdings:
            weight = (holding.market_value / portfolio.total_market_value) * 100
            if weight > 25:
                recommendations.append(f"Consider reducing position size in {holding.ticker} (currently {weight:.1f}% of portfolio)")
        
        # Check cash allocation
        cash_weight = (portfolio.cash_balance / portfolio.total_market_value) * 100 if portfolio.total_market_value > 0 else 0
        if cash_weight > 20:
            recommendations.append("Consider investing excess cash to improve returns")
        elif cash_weight < 5:
            recommendations.append("Consider maintaining some cash for opportunities and emergencies")
        
        # Risk-based recommendations
        profile = user_profile_manager.get_profile(user_id)
        if profile and profile.risk_tolerance:
            if profile.risk_tolerance == RiskTolerance.CONSERVATIVE:
                tech_weight = sector_allocation.get("Technology", 0)
                if tech_weight > 30:
                    recommendations.append("Consider reducing technology exposure for a more conservative approach")
            elif profile.risk_tolerance == RiskTolerance.AGGRESSIVE:
                if len(set(h.sector for h in portfolio.holdings)) < 3:
                    recommendations.append("Consider adding exposure to growth sectors like technology or emerging markets")
        
        return recommendations
    
    def get_portfolio_comparison(self, user_id: str, portfolio_id: str) -> Dict[str, Any]:
        """Compare portfolio against benchmarks and user's risk profile"""
        portfolio = self._get_portfolio(user_id, portfolio_id)
        if not portfolio:
            raise ValueError("Portfolio not found")
        
        profile = user_profile_manager.get_profile(user_id)
        
        # Get recommended allocation based on risk tolerance
        recommended_allocation = {}
        if profile and profile.risk_tolerance:
            if profile.risk_tolerance == RiskTolerance.CONSERVATIVE:
                recommended_allocation = {"stocks": 30, "bonds": 60, "cash": 10}
            elif profile.risk_tolerance == RiskTolerance.MODERATE:
                recommended_allocation = {"stocks": 60, "bonds": 30, "cash": 10}
            else:  # AGGRESSIVE
                recommended_allocation = {"stocks": 80, "bonds": 15, "cash": 5}
        
        # Calculate current allocation (simplified - treating all holdings as stocks)
        current_allocation = {
            "stocks": 100 - (portfolio.cash_balance / portfolio.total_market_value * 100) if portfolio.total_market_value > 0 else 0,
            "bonds": 0,  # Simplified - no bond tracking in this demo
            "cash": (portfolio.cash_balance / portfolio.total_market_value * 100) if portfolio.total_market_value > 0 else 100
        }
        
        return {
            "current_allocation": current_allocation,
            "recommended_allocation": recommended_allocation,
            "allocation_differences": {
                asset: recommended_allocation.get(asset, 0) - current_allocation.get(asset, 0)
                for asset in set(list(current_allocation.keys()) + list(recommended_allocation.keys()))
            },
            "rebalancing_needed": any(
                abs(recommended_allocation.get(asset, 0) - current_allocation.get(asset, 0)) > 10
                for asset in recommended_allocation
            ),
            "performance_vs_benchmark": self._calculate_benchmark_comparison(portfolio)
        }
    
    def _calculate_benchmark_comparison(self, portfolio: Portfolio) -> Dict[str, Any]:
        """Compare portfolio performance against market benchmarks"""
        # Simplified benchmark comparison for demo
        portfolio_return = portfolio.total_unrealized_gain_loss_percent
        
        # Simulated benchmark returns
        sp500_return = random.uniform(-5, 15)  # S&P 500
        nasdaq_return = random.uniform(-10, 20)  # NASDAQ
        
        return {
            "portfolio_return": portfolio_return,
            "sp500_return": sp500_return,
            "nasdaq_return": nasdaq_return,
            "vs_sp500": portfolio_return - sp500_return,
            "vs_nasdaq": portfolio_return - nasdaq_return,
            "outperforming_sp500": portfolio_return > sp500_return,
            "outperforming_nasdaq": portfolio_return > nasdaq_return
        }
    
    def _get_portfolio(self, user_id: str, portfolio_id: str) -> Optional[Portfolio]:
        """Get portfolio by user ID and portfolio ID"""
        profile = user_profile_manager.get_profile(user_id)
        if not profile:
            return None
        
        for portfolio in profile.portfolios:
            if portfolio.portfolio_id == portfolio_id:
                return portfolio
        
        return None
    
    def get_user_portfolios(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all portfolios for a user"""
        profile = user_profile_manager.get_profile(user_id)
        if not profile:
            return []
        
        portfolios = []
        for portfolio in profile.portfolios:
            self._simulate_price_updates(portfolio)  # Demo price updates
            
            portfolios.append({
                "portfolio_id": portfolio.portfolio_id,
                "name": portfolio.name,
                "total_value": portfolio.total_market_value,
                "total_gain_loss": portfolio.total_unrealized_gain_loss,
                "total_gain_loss_percent": portfolio.total_unrealized_gain_loss_percent,
                "holdings_count": len(portfolio.holdings),
                "last_updated": portfolio.last_updated.isoformat(),
                "created_date": portfolio.created_date.isoformat()
            })
        
        return portfolios

    def delete_portfolio(self, user_id: str, portfolio_id: str) -> bool:
        """Delete a portfolio"""
        profile = user_profile_manager.get_profile(user_id)
        if not profile:
            return False

        # Find and remove portfolio
        for i, portfolio in enumerate(profile.portfolios):
            if portfolio.portfolio_id == portfolio_id:
                del profile.portfolios[i]
                profile.last_updated = datetime.now()
                logger.info(f"Deleted portfolio {portfolio_id} for user {user_id}")
                return True

        return False

    def update_holding(self, user_id: str, portfolio_id: str, ticker: str, shares: float, price: float) -> bool:
        """Update an existing holding"""
        portfolio = self._get_portfolio(user_id, portfolio_id)
        if not portfolio:
            return False

        # Find and update holding
        for holding in portfolio.holdings:
            if holding.ticker == ticker:
                holding.shares = shares
                holding.average_cost = price
                holding.last_updated = datetime.now()
                portfolio.last_updated = datetime.now()
                logger.info(f"Updated holding {ticker} for user {user_id}")
                return True

        return False

    def get_risk_analysis(self, user_id: str, portfolio_id: str) -> Dict[str, Any]:
        """Get comprehensive risk analysis for portfolio"""
        portfolio = self._get_portfolio(user_id, portfolio_id)
        if not portfolio:
            raise ValueError("Portfolio not found")

        # Simulate comprehensive risk analysis
        risk_metrics = self._calculate_risk_metrics(portfolio)

        # Enhanced risk analysis
        analysis = {
            "portfolio_id": portfolio_id,
            "risk_score": risk_metrics.get("risk_score", 50),
            "volatility": risk_metrics.get("volatility", 15.0),
            "beta": risk_metrics.get("beta", 1.0),
            "sharpe_ratio": risk_metrics.get("sharpe_ratio", 1.2),
            "max_drawdown": risk_metrics.get("max_drawdown", -8.5),
            "var_95": risk_metrics.get("var_95", -12.3),  # Value at Risk 95%
            "correlation_analysis": {
                "market_correlation": 0.85,
                "sector_concentration": self._calculate_sector_concentration(portfolio),
                "diversification_score": self._calculate_diversification_score(portfolio)
            },
            "risk_factors": [
                "High concentration in technology sector",
                "Limited international exposure",
                "Moderate correlation with market indices"
            ],
            "recommendations": [
                "Consider adding bonds or defensive stocks",
                "Diversify across more sectors",
                "Add international exposure for better diversification"
            ]
        }

        return analysis

    def _calculate_sector_concentration(self, portfolio: Portfolio) -> float:
        """Calculate sector concentration risk"""
        if not portfolio.holdings:
            return 0.0

        sector_allocation = portfolio.get_sector_allocation()
        if not sector_allocation:
            return 0.0

        # Calculate Herfindahl-Hirschman Index for concentration
        total_value = sum(allocation["value"] for allocation in sector_allocation.values())
        if total_value == 0:
            return 0.0

        hhi = sum((allocation["value"] / total_value) ** 2 for allocation in sector_allocation.values())
        return hhi * 100  # Convert to percentage

    def _calculate_diversification_score(self, portfolio: Portfolio) -> float:
        """Calculate portfolio diversification score (0-100)"""
        if not portfolio.holdings:
            return 0.0

        # Factors: number of holdings, sector diversity, position sizes
        num_holdings = len(portfolio.holdings)
        num_sectors = len(set(h.sector for h in portfolio.holdings))

        # Base score from number of holdings
        holdings_score = min(num_holdings * 5, 40)  # Max 40 points for holdings count

        # Sector diversity score
        sector_score = min(num_sectors * 10, 30)  # Max 30 points for sector diversity

        # Position size distribution score
        total_value = sum(h.market_value for h in portfolio.holdings)
        if total_value > 0:
            position_sizes = [h.market_value / total_value for h in portfolio.holdings]
            # Penalize large positions
            max_position = max(position_sizes)
            position_score = max(30 - (max_position * 100 - 10), 0)  # Max 30 points
        else:
            position_score = 0

        return min(holdings_score + sector_score + position_score, 100)

# Global portfolio service instance
portfolio_service = PortfolioService()
