<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Analysis Chatbot</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 80vh;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 16px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
            display: flex;
            flex-direction: column;
            overflow: hidden;
            backdrop-filter: blur(16px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .chat-header {
            background: linear-gradient(135deg, #1e3a8a 0%, #3730a3 50%, #581c87 100%);
            color: white;
            padding: 20px;
            border-radius: 16px 16px 0 0;
            box-shadow: none;
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }

        .brand h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 700;
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .brand p {
            margin: 5px 0 0 0;
            opacity: 0.9;
            font-size: 14px;
            font-weight: 300;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .token-counter {
            background: rgba(255,255,255,0.1);
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            backdrop-filter: blur(10px);
        }

        .token-count {
            font-weight: 600;
            color: #10b981;
        }

        .token-limit {
            opacity: 0.7;
        }

        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .user-name {
            font-size: 14px;
            font-weight: 500;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
        }
        
        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }
        
        .message.user .message-content {
            background: #667eea;
            color: white;
            border-bottom-right-radius: 4px;
        }
        
        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
            border-bottom-left-radius: 4px;
        }
        
        .chat-input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 10px;
        }
        
        .chat-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: border-color 0.3s;
        }
        
        .chat-input:focus {
            border-color: #667eea;
        }
        
        .send-button {
            padding: 12px 24px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: background 0.3s;
        }
        
        .send-button:hover {
            background: #5a6fd8;
        }
        
        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 10px;
            color: #666;
        }
        
        .examples {
            margin-top: 20px;
            padding: 15px;
            background: #e8f2ff;
            border-radius: 10px;
        }
        
        .examples h3 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .example-button {
            display: inline-block;
            margin: 5px;
            padding: 8px 12px;
            background: white;
            border: 1px solid #667eea;
            color: #667eea;
            border-radius: 15px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }
        
        .example-button:hover {
            background: #667eea;
            color: white;
        }

        /* Options Menu Styling */
        .options-menu {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }

        .option-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 16px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-align: left;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .option-btn:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .option-btn:active {
            transform: translateY(0);
        }

        /* Token Notification */
        .token-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Image Upload Styling - Sleek & Professional */
        .input-wrapper {
            display: flex;
            gap: 8px;
            align-items: center;
        }

        .chat-input {
            flex: 1;
        }

        .input-actions {
            display: flex;
            gap: 6px;
        }

        .image-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s ease;
            min-width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .image-button:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
        }

        .image-button.active {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }

        .image-upload-area {
            margin-top: 12px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
        }

        .upload-zone {
            background: linear-gradient(135deg, #f8faff 0%, #f1f5f9 100%);
            border: 2px dashed #cbd5e1;
            border-radius: 10px;
            padding: 24px 16px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 12px;
        }

        .upload-zone:hover {
            border-color: #667eea;
            background: linear-gradient(135deg, #eef2ff 0%, #e0e7ff 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
        }

        .upload-zone.dragover {
            border-color: #4f46e5;
            background: linear-gradient(135deg, #e0e7ff 0%, #c7d2fe 100%);
            transform: scale(1.02);
            box-shadow: 0 12px 30px rgba(79, 70, 229, 0.25);
        }

        .upload-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .upload-icon {
            font-size: 32px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .upload-text strong {
            font-size: 16px;
            color: #1f2937;
            font-weight: 600;
            margin-bottom: 4px;
        }

        .upload-text p {
            color: #6b7280;
            margin: 0;
            font-size: 14px;
        }

        .upload-text small {
            color: #9ca3af;
            font-size: 12px;
            margin-top: 4px;
        }

        .image-preview {
            margin: 12px;
            text-align: center;
            background: white;
            border-radius: 10px;
            padding: 16px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .image-preview img {
            max-width: 100%;
            max-height: 200px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }

        .preview-actions {
            margin-top: 12px;
            display: flex;
            gap: 8px;
            justify-content: center;
        }

        .analyze-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .analyze-button:hover {
            background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        }

        .clear-button {
            background: #ef4444;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 500;
            font-size: 14px;
            transition: all 0.2s ease;
        }

        .clear-button:hover {
            background: #dc2626;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .header-content {
                flex-direction: column;
                text-align: center;
            }

            .user-info {
                justify-content: center;
            }

            .options-menu {
                grid-template-columns: 1fr;
            }

            .input-wrapper {
                flex-direction: column;
                gap: 10px;
            }

            .input-actions {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="header-content">
                <div class="brand">
                    <h1>📊 FinanceGPT Pro</h1>
                    <p>Professional AI-Powered Financial Analysis Platform</p>
                </div>
                <div class="user-info">
                    <div class="token-counter">
                        <span class="token-label">Tokens:</span>
                        <span class="token-count" id="tokenCount">2,500</span>
                        <span class="token-limit">/ 5,000</span>
                    </div>
                    <div class="user-profile">
                        <span class="user-name" id="userName">Professional User</span>
                        <div class="user-avatar">👤</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="chat-messages" id="chatMessages">
            <div class="message assistant">
                <div class="message-content">
                    <strong>Welcome to FinanceGPT Pro - Professional Financial Analysis Platform</strong><br><br>
                    <strong>🏢 Enterprise-Grade Capabilities:</strong><br>
                    • 📊 Advanced market data and analytics<br>
                    • 📈 Technical analysis with 20+ indicators (RSI, MACD, Bollinger Bands)<br>
                    • 📰 AI-powered sentiment analysis from 1000+ news sources<br>
                    • 💼 Comprehensive fundamental analysis and financial modeling<br>
                    • 🎯 Risk-adjusted investment recommendations<br>
                    • 📸 Chart pattern recognition and image analysis<br>
                    • 👤 Personalized portfolio optimization<br><br>
                    <strong>💡 Getting Started:</strong> Enter any ticker symbol (e.g., AAPL, TSLA) to see intelligent analysis options.
                </div>
            </div>
            
            <div class="examples">
                <h3>💡 Try these examples:</h3>
                <span class="example-button" onclick="sendExample('What is Apple\'s current stock price?')">Apple Price</span>
                <span class="example-button" onclick="sendExample('Is Tesla overbought?')">Tesla Technical</span>
                <span class="example-button" onclick="sendExample('What\'s the sentiment for Microsoft?')">MSFT Sentiment</span>
                <span class="example-button" onclick="sendExample('Should I buy NVDA stock?')">NVDA Recommendation</span>
                <span class="example-button" onclick="sendExample('Analyze everything about Amazon')">AMZN Complete</span>
            </div>
        </div>
        
        <div class="loading" id="loading">
            🤖 Analyzing... Please wait...
        </div>
        
        <div class="chat-input-container">
            <div class="input-wrapper">
                <input type="text" id="chatInput" class="chat-input" placeholder="Ask about any stock... (e.g., 'What's AAPL's price?')" onkeypress="handleKeyPress(event)">
                <div class="input-actions">
                    <button onclick="toggleImageUpload()" class="image-button" id="imageButton" title="Upload Chart Image">
                        📸
                    </button>
                    <button onclick="sendMessage()" class="send-button" id="sendButton">Send</button>
                </div>
            </div>

            <!-- Image Upload Area - Compact & Elegant -->
            <div class="image-upload-area" id="imageUploadArea" style="display: none;">
                <div class="upload-zone" ondrop="handleImageDrop(event)" ondragover="allowDrop(event)" onclick="document.getElementById('chartImageInput').click()">
                    <input type="file" id="chartImageInput" accept="image/*" style="display: none;" onchange="handleImageSelect(event)">
                    <div class="upload-content">
                        <div class="upload-icon">📊</div>
                        <div class="upload-text">
                            <strong>Chart Analysis</strong>
                            <p>Drop image or click to upload</p>
                            <small>PNG, JPG, WebP • Max 10MB</small>
                        </div>
                    </div>
                </div>

                <!-- Image Preview - Compact -->
                <div class="image-preview" id="imagePreview" style="display: none;">
                    <img id="previewImage" alt="Chart Preview">
                    <div class="preview-actions">
                        <button onclick="analyzeImage()" class="analyze-button">🔍 Analyze</button>
                        <button onclick="clearImage()" class="clear-button">✕</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8124';

        // Token Management System
        let userTokens = {
            current: 2500,
            limit: 5000,
            dailyUsed: 0,
            dailyLimit: 1000
        };

        const tokenCosts = {
            'price-analysis': 10,
            'technical-analysis': 25,
            'sentiment-analysis': 20,
            'buy-recommendation': 30,
            'risk-assessment': 35,
            'comprehensive': 50,
            'image-analysis': 75
        };
        
        // Token Management Functions
        function updateTokenDisplay() {
            document.getElementById('tokenCount').textContent = userTokens.current.toLocaleString();

            // Change color based on token level
            const tokenElement = document.getElementById('tokenCount');
            if (userTokens.current < 500) {
                tokenElement.style.color = '#ef4444'; // Red
            } else if (userTokens.current < 1000) {
                tokenElement.style.color = '#f59e0b'; // Orange
            } else {
                tokenElement.style.color = '#10b981'; // Green
            }
        }

        function deductTokens(analysisType, customCost = null) {
            const cost = customCost || tokenCosts[analysisType] || 10;

            if (userTokens.current >= cost) {
                userTokens.current -= cost;
                userTokens.dailyUsed += cost;
                updateTokenDisplay();

                // Show token usage notification
                showTokenNotification(cost, analysisType);
                return true;
            } else {
                showInsufficientTokensMessage();
                return false;
            }
        }

        function showTokenNotification(cost, analysisType) {
            const notification = document.createElement('div');
            notification.className = 'token-notification';
            notification.innerHTML = `📊 Analysis complete • ${cost} tokens used • ${userTokens.current} remaining`;

            document.body.appendChild(notification);
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        function showInsufficientTokensMessage() {
            addMessage(`⚠️ **Insufficient Tokens**\n\nYou need more tokens to perform this analysis. Current balance: ${userTokens.current} tokens.\n\n**Upgrade Options:**\n• Professional Plan: 10,000 tokens/month\n• Enterprise Plan: Unlimited tokens\n\nContact support for token top-up options.`);
        }

        function scrollToLatestMessage() {
            const messagesContainer = document.getElementById('chatMessages');
            const messages = messagesContainer.querySelectorAll('.message');

            if (messages.length > 0) {
                const lastMessage = messages[messages.length - 1];
                lastMessage.scrollIntoView({
                    behavior: 'smooth',
                    block: 'end',
                    inline: 'nearest'
                });
            }
        }

        function addMessage(content, isUser = false) {
            const messagesContainer = document.getElementById('chatMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : 'assistant'}`;
            
            const contentDiv = document.createElement('div');
            contentDiv.className = 'message-content';
            
            if (isUser) {
                contentDiv.textContent = content;
            } else {
                // Convert markdown-like formatting to HTML
                let formattedContent = content
                    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                    .replace(/\*(.*?)\*/g, '<em>$1</em>')
                    .replace(/\n/g, '<br>');
                contentDiv.innerHTML = formattedContent;
            }
            
            messageDiv.appendChild(contentDiv);
            messagesContainer.appendChild(messageDiv);

            // Enhanced scroll to bottom with smooth animation
            setTimeout(() => {
                messagesContainer.scrollTo({
                    top: messagesContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }, 100); // Small delay to ensure content is rendered
        }
        
        async function sendMessage() {
            const input = document.getElementById('chatInput');
            const sendButton = document.getElementById('sendButton');
            const loading = document.getElementById('loading');

            const query = input.value.trim();
            if (!query) return;

            // Add user message
            addMessage(query, true);
            input.value = '';

            // Show loading
            sendButton.disabled = true;
            loading.style.display = 'block';

            // Simulate API delay and generate mock response
            setTimeout(() => {
                const response = generateMockResponse(query);

                // Determine analysis type for token deduction
                const analysisType = determineAnalysisType(query);

                // Deduct tokens and show response
                if (deductTokens(analysisType)) {
                    addMessage(response);
                    // Additional scroll after analysis is complete to ensure visibility
                    setTimeout(() => {
                        scrollToLatestMessage();
                    }, 200);
                } else {
                    // Insufficient tokens message already shown by deductTokens
                }

                sendButton.disabled = false;
                loading.style.display = 'none';
            }, 1500);
        }

        // Stock detection and helper functions
        function detectStock(queryLower) {
            const stocks = {
                'aapl': { ticker: 'AAPL', company: 'Apple Inc.', price: '$175.43', sector: 'Technology' },
                'apple': { ticker: 'AAPL', company: 'Apple Inc.', price: '$175.43', sector: 'Technology' },
                'tsla': { ticker: 'TSLA', company: 'Tesla Inc.', price: '$248.87', sector: 'Automotive' },
                'tesla': { ticker: 'TSLA', company: 'Tesla Inc.', price: '$248.87', sector: 'Automotive' },
                'msft': { ticker: 'MSFT', company: 'Microsoft Corporation', price: '$378.85', sector: 'Technology' },
                'microsoft': { ticker: 'MSFT', company: 'Microsoft Corporation', price: '$378.85', sector: 'Technology' },
                'nvda': { ticker: 'NVDA', company: 'NVIDIA Corporation', price: '$875.28', sector: 'Technology' },
                'nvidia': { ticker: 'NVDA', company: 'NVIDIA Corporation', price: '$875.28', sector: 'Technology' },
                'amzn': { ticker: 'AMZN', company: 'Amazon.com Inc.', price: '$155.89', sector: 'E-commerce' },
                'amazon': { ticker: 'AMZN', company: 'Amazon.com Inc.', price: '$155.89', sector: 'E-commerce' },
                'googl': { ticker: 'GOOGL', company: 'Alphabet Inc.', price: '$138.45', sector: 'Technology' },
                'google': { ticker: 'GOOGL', company: 'Alphabet Inc.', price: '$138.45', sector: 'Technology' },
                'meta': { ticker: 'META', company: 'Meta Platforms Inc.', price: '$485.22', sector: 'Technology' },
                'facebook': { ticker: 'META', company: 'Meta Platforms Inc.', price: '$485.22', sector: 'Technology' }
            };

            for (const [key, stock] of Object.entries(stocks)) {
                if (queryLower.includes(key)) {
                    return stock;
                }
            }

            // Default to Apple if no match
            return stocks['aapl'];
        }

        function isSimpleTickerQuery(query, stockInfo) {
            const queryLower = query.toLowerCase().trim();
            const words = queryLower.split(' ').filter(word => word.length > 0);

            // Check if it's just a ticker symbol or company name (1-2 words)
            if (words.length <= 2) {
                // Check if it contains the ticker or company name but no analysis keywords
                const analysisKeywords = ['price', 'buy', 'sell', 'recommend', 'sentiment', 'analysis', 'technical', 'fundamental', 'should', 'what', 'how', 'when', 'why'];
                const hasAnalysisKeyword = analysisKeywords.some(keyword => queryLower.includes(keyword));

                if (!hasAnalysisKeyword) {
                    return true;
                }
            }

            return false;
        }

        function generateOptionsMenu(stockInfo) {
            return `## 🎯 What would you like to know about ${stockInfo.ticker}?

**${stockInfo.company}** (${stockInfo.sector})
**Current Price**: ${stockInfo.price}

Please choose what type of analysis you'd like:

<div class="options-menu">
    <button class="option-btn" onclick="handleOptionClick('${stockInfo.ticker}', 'buy-recommendation')">
        🎯 Should I buy ${stockInfo.ticker}?
    </button>
    <button class="option-btn" onclick="handleOptionClick('${stockInfo.ticker}', 'price-analysis')">
        💰 Price Analysis & Trends
    </button>
    <button class="option-btn" onclick="handleOptionClick('${stockInfo.ticker}', 'technical-analysis')">
        📈 Technical Analysis (RSI, MACD)
    </button>
    <button class="option-btn" onclick="handleOptionClick('${stockInfo.ticker}', 'sentiment-analysis')">
        📊 Market Sentiment
    </button>
    <button class="option-btn" onclick="handleOptionClick('${stockInfo.ticker}', 'risk-assessment')">
        ⚠️ Risk Assessment
    </button>
    <button class="option-btn" onclick="handleOptionClick('${stockInfo.ticker}', 'comprehensive')">
        🚀 Complete Analysis
    </button>
</div>

**Or type your specific question about ${stockInfo.ticker}!**`;
        }

        function handleOptionClick(ticker, analysisType) {
            const queries = {
                'buy-recommendation': `Should I buy ${ticker} stock?`,
                'price-analysis': `What's ${ticker}'s price analysis?`,
                'technical-analysis': `${ticker} technical analysis`,
                'sentiment-analysis': `${ticker} market sentiment`,
                'risk-assessment': `${ticker} risk assessment`,
                'comprehensive': `Analyze everything about ${ticker}`
            };

            const query = queries[analysisType];
            document.getElementById('chatInput').value = query;
            sendMessage();
        }

        function generateMockResponse(query) {
            const queryLower = query.toLowerCase().trim();
            const currentTime = new Date().toLocaleString();
            const marketHours = new Date().getHours();
            const isMarketOpen = marketHours >= 9 && marketHours < 16;
            const marketStatus = isMarketOpen ? 'MARKET OPEN' : 'AFTER HOURS';

            // Detect stock mentions and get stock info
            const stockInfo = detectStock(queryLower);

            // Check if user just entered a ticker symbol (short query, no specific request)
            if (isSimpleTickerQuery(query, stockInfo)) {
                return generateOptionsMenu(stockInfo);
            }

            // Generate response based on query type
            if (queryLower.includes('price') || queryLower.includes('cost')) {
                return `## � Stock Price Analysis for ${stockInfo.ticker}

**${stockInfo.company}** - ${stockInfo.sector}
**Current Price**: ${stockInfo.price}
**Market Status**: ${marketStatus}
**Analysis Time**: ${currentTime}

**📊 Market Data:**
- **Volume**: 45,234,567 (${isMarketOpen ? 'Live Trading' : 'After Hours'})
- **Market Cap**: $2.89T
- **P/E Ratio**: 28.5
- **52-Week Range**: $164.08 - $199.62

**� Current Session:**
- **Day Change**: +$2.15 (+1.24%)
- **After Hours**: ${stockInfo.price} (0.00%)
- **Avg Volume**: 52.1M
- **Bid/Ask**: $${(parseFloat(stockInfo.price.replace('$', '')) - 0.05).toFixed(2)}/$${(parseFloat(stockInfo.price.replace('$', '')) + 0.05).toFixed(2)}

**� MARKET CONDITIONS:**
- **Market Sentiment**: ${Math.random() > 0.5 ? 'Bullish' : 'Bearish'}
- **Volatility**: ${Math.random() > 0.7 ? 'High' : Math.random() > 0.3 ? 'Moderate' : 'Low'}
- **Sector Performance**: ${stockInfo.sector} is ${Math.random() > 0.5 ? 'outperforming' : 'underperforming'} today

**⚠️ Real-Time Disclaimer**: This analysis reflects current market conditions as of ${currentTime}. Market data updates continuously during trading hours.`;

            } else if (queryLower.includes('buy') || queryLower.includes('recommend')) {
                const marketSentiment = Math.random() > 0.5 ? 'Bullish' : 'Bearish';
                const volatility = Math.random() > 0.7 ? 'High' : Math.random() > 0.3 ? 'Moderate' : 'Low';
                const sectorPerformance = Math.random() > 0.5 ? 'outperforming' : 'underperforming';

                return `## 🔴 **REAL-TIME** Investment Recommendation for ${stockInfo.ticker}

**${stockInfo.company}** - ${stockInfo.sector}
**Analysis Time**: ${currentTime}
**Market Status**: ${marketStatus}

**🎯 Current Recommendation**: ${Math.random() > 0.3 ? 'BUY' : 'HOLD'}
**Confidence Level**: ${(Math.random() * 30 + 60).toFixed(0)}%

**🔴 LIVE Market Factors:**
• **Current Market Sentiment**: ${marketSentiment}
• **Market Volatility**: ${volatility} - ${volatility === 'High' ? 'Exercise caution' : 'Favorable conditions'}
• **Sector Performance**: ${stockInfo.sector} is ${sectorPerformance} today
• **Trading Session**: ${isMarketOpen ? 'Active trading with high liquidity' : 'After-hours with limited liquidity'}

**📊 Real-Time Technical Outlook**:
- **RSI**: ${(Math.random() * 40 + 30).toFixed(1)} (${Math.random() > 0.5 ? 'Neutral' : 'Oversold'})
- **MACD**: ${Math.random() > 0.5 ? 'Bullish crossover' : 'Bearish divergence'}
- **Volume**: ${Math.random() > 0.5 ? 'Above average' : 'Below average'}

**⚠️ Current Risk Assessment**: ${volatility === 'High' ? 'Elevated' : 'Moderate'} risk level
${volatility === 'High' ? '• High volatility suggests smaller position sizes\n• Consider dollar-cost averaging' : '• Stable conditions support normal position sizing'}

**💡 Real-Time Strategy**:
${isMarketOpen ?
  `Market is open - ${marketSentiment === 'Bullish' ? 'favorable for entry' : 'wait for better entry point'}` :
  `After-hours trading - consider waiting for market open for better execution`}

**🔴 Live Disclaimer**: This recommendation reflects current market conditions as of ${currentTime}. Market conditions change rapidly during trading hours.`;

            } else if (queryLower.includes('sentiment')) {
                const sentimentScore = (Math.random() * 0.6 + 0.2).toFixed(2);
                const positiveNews = Math.floor(Math.random() * 30 + 50);
                const neutralNews = Math.floor(Math.random() * 30 + 20);
                const negativeNews = 100 - positiveNews - neutralNews;
                const twitterMentions = Math.floor(Math.random() * 20000 + 10000);
                const redditPosts = Math.floor(Math.random() * 3000 + 1000);

                return `## � **REAL-TIME** Sentiment Analysis for ${stockInfo.ticker}

**${stockInfo.company}** - ${stockInfo.sector}
**Analysis Time**: ${currentTime}
**Market Status**: ${marketStatus}

**📊 Live Sentiment Score**: ${sentimentScore > 0.5 ? 'Positive' : sentimentScore > 0.3 ? 'Neutral' : 'Negative'} (${sentimentScore})

**📈 Real-Time News Sentiment:**
- Positive: ${positiveNews}%
- Neutral: ${neutralNews}%
- Negative: ${negativeNews}%

**� LIVE Social Media Buzz:**
- **Twitter mentions**: ${twitterMentions.toLocaleString()} (${Math.random() > 0.5 ? '↑' : '↓'}${Math.floor(Math.random() * 20 + 5)}% vs yesterday)
- **Reddit discussions**: ${redditPosts.toLocaleString()} posts (last 24h)
- **Overall tone**: ${sentimentScore > 0.5 ? 'Optimistic' : sentimentScore > 0.3 ? 'Mixed' : 'Cautious'}
- **Trending topics**: ${Math.random() > 0.5 ? 'Earnings, Growth' : 'Volatility, Risk'}

**📰 Current Headlines (Live Feed):**
- "${stockInfo.company} ${Math.random() > 0.5 ? 'beats' : 'meets'} analyst expectations"
- "${Math.random() > 0.5 ? 'Bullish' : 'Mixed'} analyst sentiment on ${stockInfo.ticker}"
- "${stockInfo.sector} sector ${Math.random() > 0.5 ? 'momentum continues' : 'faces headwinds'}"

**🔴 Real-Time Market Mood**:
${sentimentScore > 0.5 ? 'Bullish sentiment driving interest' : 'Mixed sentiment creating uncertainty'}
${isMarketOpen ? ' - Active trading reflects current sentiment' : ' - After-hours sentiment may shift by market open'}

**⚠️ Live Sentiment Disclaimer**: Sentiment analysis reflects current social media and news trends as of ${currentTime}. Sentiment can change rapidly during trading hours.`;

            } else {
                return `# 🚀 Comprehensive Stock Analysis: ${stockInfo.ticker}

## 💰 Current Market Data
- **Current Price**: ${stockInfo.price}
- **Volume**: 45,234,567
- **Market Cap**: $2.89T
- **Company**: ${stockInfo.company} (${stockInfo.sector})

## 📈 Technical Analysis
**RSI (14)**: 58.2 (Neutral)
**MACD**: Bullish crossover detected
**Moving Averages**:
- 50-day: $172.45 (Above)
- 200-day: $168.90 (Above)

## 📊 Market Sentiment
**Overall Sentiment**: Positive (0.72)
- Recent news: 68% positive
- Social media: Bullish trend
- Analyst ratings: 12 Buy, 3 Hold, 1 Sell

## 🎯 Investment Recommendation
**Recommendation**: Buy
**Price Target**: $195.00
**Risk Level**: Medium

## 📚 Understanding This Analysis
- **Technical Analysis**: Based on price patterns and indicators
- **Fundamental Analysis**: Based on company financials and valuation
- **Sentiment Analysis**: Based on news and social media sentiment

**⚠️ Important Disclaimer**: This analysis is generated by AI for informational purposes only and should not be considered as personalized financial advice. Always conduct your own research and consult with a qualified financial advisor before making investment decisions.`;
            }
        }
        
        function sendExample(query) {
            document.getElementById('chatInput').value = query;
            sendMessage();
        }
        
        function handleKeyPress(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }
        
        function determineAnalysisType(query) {
            const queryLower = query.toLowerCase();

            if (queryLower.includes('buy') || queryLower.includes('recommend')) {
                return 'buy-recommendation';
            } else if (queryLower.includes('technical') || queryLower.includes('rsi') || queryLower.includes('macd')) {
                return 'technical-analysis';
            } else if (queryLower.includes('sentiment')) {
                return 'sentiment-analysis';
            } else if (queryLower.includes('risk')) {
                return 'risk-assessment';
            } else if (queryLower.includes('price') || queryLower.includes('cost')) {
                return 'price-analysis';
            } else if (queryLower.includes('analyze everything') || queryLower.includes('complete')) {
                return 'comprehensive';
            } else {
                return 'price-analysis'; // Default
            }
        }

        // Image Analysis Functions
        let currentImage = null;
        let imageAnalysisData = null;

        function toggleImageUpload() {
            const uploadArea = document.getElementById('imageUploadArea');
            const imageButton = document.getElementById('imageButton');
            const isVisible = uploadArea.style.display !== 'none';

            uploadArea.style.display = isVisible ? 'none' : 'block';

            // Update button appearance with smooth transition
            if (isVisible) {
                imageButton.classList.remove('active');
                imageButton.style.background = 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
            } else {
                imageButton.classList.add('active');
                imageButton.style.background = 'linear-gradient(135deg, #10b981 0%, #059669 100%)';
            }
        }

        function allowDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.add('dragover');
        }

        function handleImageDrop(event) {
            event.preventDefault();
            event.currentTarget.classList.remove('dragover');

            const files = event.dataTransfer.files;
            if (files.length > 0) {
                processImageFile(files[0]);
            }
        }

        function handleImageSelect(event) {
            const file = event.target.files[0];
            if (file) {
                processImageFile(file);
            }
        }

        function processImageFile(file) {
            // Validate file
            if (!file.type.startsWith('image/')) {
                addMessage('❌ Please upload a valid image file (PNG, JPG, WebP)');
                return;
            }

            if (file.size > 10 * 1024 * 1024) { // 10MB limit
                addMessage('❌ Image file too large. Please upload an image smaller than 10MB.');
                return;
            }

            // Create preview
            const reader = new FileReader();
            reader.onload = function(e) {
                currentImage = {
                    file: file,
                    dataUrl: e.target.result,
                    name: file.name,
                    size: file.size
                };

                showImagePreview(e.target.result);
            };
            reader.readAsDataURL(file);
        }

        function showImagePreview(dataUrl) {
            const previewDiv = document.getElementById('imagePreview');
            const previewImg = document.getElementById('previewImage');

            previewImg.src = dataUrl;
            previewDiv.style.display = 'block';

            addMessage(`� **Chart uploaded** • ${currentImage.name} • ${(currentImage.size / 1024 / 1024).toFixed(2)} MB\n\nClick **🔍 Analyze** to start AI analysis.`);
        }

        function clearImage() {
            currentImage = null;
            imageAnalysisData = null;

            document.getElementById('imagePreview').style.display = 'none';
            document.getElementById('chartImageInput').value = '';

            addMessage('🗑️ Chart image removed.');
        }

        async function analyzeImage() {
            if (!currentImage) {
                addMessage('❌ No image to analyze. Please upload a chart first.');
                return;
            }

            // Check tokens
            if (!deductTokens('image-analysis')) {
                return; // Insufficient tokens message already shown
            }

            // Show loading
            const loading = document.getElementById('loading');
            loading.style.display = 'block';

            addMessage('🔍 **Analyzing Chart Image...**\n\nAI is processing your chart to identify:\n• Chart patterns and trends\n• Support and resistance levels\n• Technical indicators\n• Price targets and signals\n\nThis may take a few moments...');

            try {
                // Create FormData for file upload
                const formData = new FormData();
                formData.append('file', currentImage.file);

                // Call backend API for real analysis
                const response = await fetch(`${API_BASE}/analyze-image`, {
                    method: 'POST',
                    body: formData
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result = await response.json();

                if (result.success) {
                    const analysis = formatImageAnalysisResult(result.analysis, result.metadata);
                    addMessage(analysis);
                    // Scroll to show the analysis results
                    setTimeout(() => {
                        scrollToLatestMessage();
                    }, 200);
                } else {
                    addMessage('❌ Image analysis failed. Please try again with a different chart image.');
                }

            } catch (error) {
                console.error('Image analysis error:', error);
                // Fallback to mock analysis if API fails
                const analysis = performMockImageAnalysis(currentImage);
                addMessage(analysis);
                // Scroll to show the analysis results
                setTimeout(() => {
                    scrollToLatestMessage();
                }, 200);
            } finally {
                loading.style.display = 'none';
            }
        }

        function formatImageAnalysisResult(analysis, metadata) {
            const patterns = analysis.patterns_detected || [];
            const indicators = analysis.technical_indicators || {};
            const recommendations = analysis.recommendations || {};

            let patternText = '';
            if (patterns.length > 0) {
                const mainPattern = patterns[0];
                patternText = `**Pattern**: ${mainPattern.type}
**Reliability**: ${(mainPattern.confidence * 100).toFixed(0)}%
**Stage**: ${mainPattern.bullish ? 'Bullish' : 'Bearish'} Formation
**Completion**: ${(mainPattern.completion * 100).toFixed(0)}%`;
            }

            return `# 📊 **AI Chart Analysis Results**

## 🖼️ **Image Analysis Summary**
**File**: ${metadata.filename}
**Analysis Confidence**: ${(analysis.confidence_score * 100).toFixed(0)}%
**File Size**: ${(metadata.file_size / 1024 / 1024).toFixed(2)} MB

## 📈 **Technical Pattern Recognition**

### **🎯 Primary Pattern Detected**
${patternText || 'No clear patterns detected'}

### **📊 Key Price Levels**
- **Current Price**: $${indicators.current_price?.toFixed(2) || 'N/A'}
- **Support Levels**: ${analysis.support_levels?.map(s => `$${s.toFixed(2)}`).join(', ') || 'N/A'}
- **Resistance Levels**: ${analysis.resistance_levels?.map(r => `$${r.toFixed(2)}`).join(', ') || 'N/A'}

## 🔍 **Technical Indicators**

### **📈 Key Metrics**
- **RSI (14)**: ${indicators.rsi?.toFixed(1) || 'N/A'} ${indicators.rsi > 70 ? '(Overbought)' : indicators.rsi < 30 ? '(Oversold)' : '(Neutral)'}
- **SMA (5)**: $${indicators.sma_5?.toFixed(2) || 'N/A'}
- **SMA (20)**: $${indicators.sma_20?.toFixed(2) || 'N/A'}
- **MACD**: ${indicators.macd?.toFixed(2) || 'N/A'} ${indicators.macd > 0 ? '(Bullish)' : '(Bearish)'}

## 🎯 **Price Targets & Risk Management**

### **📊 Price Targets**
- **Upside Target**: $${analysis.price_targets?.upside_target?.toFixed(2) || 'N/A'}
- **Extended Target**: $${analysis.price_targets?.extended_target?.toFixed(2) || 'N/A'}

### **⚠️ Risk Management**
- **Stop Loss**: $${analysis.risk_levels?.stop_loss?.toFixed(2) || 'N/A'}
- **Risk Level**: ${analysis.risk_levels?.risk_level || 'Medium'}

## 💼 **AI Recommendation**

**Action**: **${recommendations.action || 'HOLD'}**
**Confidence**: ${(recommendations.confidence * 100).toFixed(0)}%

**Reasoning**:
${recommendations.reasoning?.map(r => `• ${r}`).join('\n') || '• Analysis based on technical patterns and indicators'}

---

**⚠️ Important Disclaimer**: This analysis is generated by AI image recognition and is for educational purposes only. Chart analysis involves interpretation and should be combined with fundamental analysis and risk management. Always consult with a qualified financial advisor before making investment decisions.

**🔄 Want to analyze another chart? Upload a new image above!**`;
        }

        function performMockImageAnalysis(imageData) {
            // Generate realistic chart analysis based on image
            const patterns = ['Head and Shoulders', 'Double Top', 'Ascending Triangle', 'Bull Flag', 'Cup and Handle'];
            const selectedPattern = patterns[Math.floor(Math.random() * patterns.length)];

            const supportLevel = (Math.random() * 50 + 150).toFixed(2);
            const resistanceLevel = (Math.random() * 50 + 200).toFixed(2);
            const currentPrice = (Math.random() * 50 + 175).toFixed(2);

            return `# 📊 **AI Chart Analysis Results**

## 🖼️ **Image Analysis Summary**
**File**: ${imageData.name}
**Analysis Confidence**: 87%
**Processing Time**: 2.3 seconds

## 📈 **Technical Pattern Recognition**

### **🎯 Primary Pattern Detected**
**Pattern**: ${selectedPattern}
**Reliability**: 85%
**Stage**: Formation Complete
**Breakout Probability**: 72%

### **📊 Key Price Levels**
- **Current Price**: $${currentPrice}
- **Support Level**: $${supportLevel} (Strong)
- **Resistance Level**: $${resistanceLevel} (Moderate)
- **Next Target**: $${(parseFloat(resistanceLevel) + 15).toFixed(2)}

## 🔍 **Technical Indicators Extracted**

### **📈 Trend Analysis**
- **Primary Trend**: Bullish (Medium-term)
- **Momentum**: Increasing
- **Volume Pattern**: Above Average
- **Trend Strength**: 7.2/10

### **🎯 Support & Resistance**
- **Major Support**: $${(parseFloat(supportLevel) - 5).toFixed(2)} - $${supportLevel}
- **Minor Resistance**: $${resistanceLevel} - $${(parseFloat(resistanceLevel) + 3).toFixed(2)}
- **Breakout Level**: $${(parseFloat(resistanceLevel) + 2).toFixed(2)}

## 💡 **AI Trading Signals**

### **🚀 Entry Signals**
- ✅ **Pattern Completion**: ${selectedPattern} pattern shows bullish bias
- ✅ **Volume Confirmation**: Above-average volume supports move
- ✅ **Support Hold**: Price holding above key support at $${supportLevel}

### **⚠️ Risk Management**
- **Stop Loss**: $${(parseFloat(supportLevel) - 2).toFixed(2)} (Below support)
- **Take Profit 1**: $${resistanceLevel} (First resistance)
- **Take Profit 2**: $${(parseFloat(resistanceLevel) + 12).toFixed(2)} (Extended target)
- **Risk/Reward Ratio**: 1:2.3

## 🎯 **Price Targets & Projections**

### **📊 Short-term (1-2 weeks)**
- **Target**: $${resistanceLevel}
- **Probability**: 68%
- **Catalyst**: Pattern breakout

### **📈 Medium-term (1-3 months)**
- **Target**: $${(parseFloat(resistanceLevel) + 15).toFixed(2)}
- **Probability**: 45%
- **Catalyst**: Sustained momentum

## 🔬 **Advanced Analysis**

### **📊 Chart Quality Assessment**
- **Image Clarity**: Excellent
- **Data Points Extracted**: 247
- **Timeframe Detected**: Daily Chart
- **Candlestick Pattern**: Clear and readable

### **🤖 AI Confidence Metrics**
- **Pattern Recognition**: 87%
- **Level Identification**: 92%
- **Trend Analysis**: 84%
- **Overall Accuracy**: 88%

## 💼 **Investment Recommendation**

**Action**: **BUY** on breakout above $${resistanceLevel}
**Position Size**: 2-3% of portfolio
**Time Horizon**: 2-8 weeks
**Risk Level**: Medium

**Strategy**: Wait for confirmed breakout with volume, then enter with tight stop loss below pattern support.

---

**⚠️ Important Disclaimer**: This analysis is generated by AI image recognition and is for educational purposes only. Chart analysis involves interpretation and should be combined with fundamental analysis and risk management. Always consult with a qualified financial advisor before making investment decisions.

**🔄 Want to analyze another chart? Upload a new image above!**`;
        }

        // Initialize the chat interface
        window.onload = function() {
            console.log('🚀 FinanceGPT Pro loaded successfully!');
            updateTokenDisplay();
        };
    </script>
</body>
</html>
