"""
Risk Assessment Service for FinanceGPT Pro
Handles risk tolerance questionnaires, scoring, and recommendations
"""

from typing import Dict, List, Any, Optional
import logging
from datetime import datetime

from ..models.user_profile_models import (
    RiskTolerance,
    RiskAssessmentResult,
    RiskAssessmentQuestion,
    RISK_ASSESSMENT_QUESTIONS,
    PortfolioAllocation,
    user_profile_manager
)

logger = logging.getLogger(__name__)

class RiskAssessmentService:
    """Service for conducting risk assessments"""
    
    def __init__(self):
        self.questions = RISK_ASSESSMENT_QUESTIONS
        self.scoring_weights = {
            "experience": 0.25,
            "goals": 0.30,
            "timeline": 0.20,
            "comfort": 0.15,
            "financial": 0.10
        }
    
    def get_assessment_questions(self) -> List[Dict[str, Any]]:
        """Get all risk assessment questions"""
        return [
            {
                "question_id": q.question_id,
                "question_text": q.question_text,
                "question_type": q.question_type,
                "options": q.options,
                "category": q.category
            }
            for q in self.questions
        ]
    
    def calculate_risk_score(self, answers: Dict[str, Any]) -> float:
        """Calculate risk score from assessment answers"""
        total_score = 0.0
        total_weight = 0.0
        
        for question in self.questions:
            if question.question_id not in answers:
                continue
            
            answer = answers[question.question_id]
            question_score = self._score_answer(question, answer)
            weighted_score = question_score * question.weight
            
            total_score += weighted_score
            total_weight += question.weight
        
        if total_weight == 0:
            return 0.0
        
        # Normalize to 0-100 scale
        normalized_score = (total_score / total_weight) * 100
        return min(100.0, max(0.0, normalized_score))
    
    def _score_answer(self, question: RiskAssessmentQuestion, answer: Any) -> float:
        """Score an individual answer (0-1 scale)"""
        if question.question_type == "boolean":
            return 1.0 if answer == "Yes" else 0.0
        
        elif question.question_type == "multiple_choice":
            if answer not in question.options:
                return 0.0
            
            # Score based on position in options (higher index = higher risk tolerance)
            option_index = question.options.index(answer)
            max_index = len(question.options) - 1
            
            if max_index == 0:
                return 0.0
            
            return option_index / max_index
        
        elif question.question_type == "scale":
            # Assume scale answers are numeric
            try:
                scale_value = float(answer)
                # Normalize to 0-1 scale (assuming 1-10 scale)
                return (scale_value - 1) / 9
            except (ValueError, TypeError):
                return 0.0
        
        return 0.0
    
    def determine_risk_tolerance(self, risk_score: float) -> RiskTolerance:
        """Determine risk tolerance category from score"""
        if risk_score < 35:
            return RiskTolerance.CONSERVATIVE
        elif risk_score < 70:
            return RiskTolerance.MODERATE
        else:
            return RiskTolerance.AGGRESSIVE
    
    def get_recommended_allocation(self, risk_tolerance: RiskTolerance) -> Dict[str, int]:
        """Get recommended portfolio allocation based on risk tolerance"""
        allocations = {
            RiskTolerance.CONSERVATIVE: {"stocks": 30, "bonds": 60, "cash": 10},
            RiskTolerance.MODERATE: {"stocks": 60, "bonds": 30, "cash": 10},
            RiskTolerance.AGGRESSIVE: {"stocks": 80, "bonds": 15, "cash": 5}
        }
        return allocations.get(risk_tolerance, allocations[RiskTolerance.MODERATE])
    
    def conduct_assessment(self, user_id: str, answers: Dict[str, Any]) -> RiskAssessmentResult:
        """Conduct complete risk assessment"""
        try:
            # Calculate risk score
            risk_score = self.calculate_risk_score(answers)
            
            # Determine risk tolerance
            risk_tolerance = self.determine_risk_tolerance(risk_score)
            
            # Get recommended allocation
            recommended_allocation = self.get_recommended_allocation(risk_tolerance)
            
            # Create assessment result
            result = RiskAssessmentResult(
                user_id=user_id,
                risk_score=risk_score,
                risk_tolerance=risk_tolerance,
                recommended_allocation=recommended_allocation,
                answers=answers
            )
            
            # Update user profile
            profile = user_profile_manager.get_profile(user_id)
            if profile:
                profile.risk_assessments.append(result)
                profile.risk_tolerance = risk_tolerance
                profile.last_updated = datetime.now()
            
            logger.info(f"Risk assessment completed for user {user_id}: {risk_tolerance.value} (score: {risk_score:.1f})")
            
            return result
            
        except Exception as e:
            logger.error(f"Risk assessment failed for user {user_id}: {str(e)}")
            raise
    
    def get_assessment_insights(self, result: RiskAssessmentResult) -> Dict[str, Any]:
        """Get detailed insights from assessment result"""
        insights = {
            "risk_profile": {
                "score": result.risk_score,
                "tolerance": result.risk_tolerance.value,
                "description": result.get_risk_description()
            },
            "portfolio_recommendations": {
                "allocation": result.recommended_allocation,
                "explanation": self._get_allocation_explanation(result.risk_tolerance)
            },
            "investment_guidelines": self._get_investment_guidelines(result.risk_tolerance),
            "category_scores": self._analyze_category_scores(result.answers),
            "improvement_suggestions": self._get_improvement_suggestions(result)
        }
        
        return insights
    
    def _get_allocation_explanation(self, risk_tolerance: RiskTolerance) -> str:
        """Get explanation for recommended allocation"""
        explanations = {
            RiskTolerance.CONSERVATIVE: "Your allocation emphasizes stability with a higher percentage in bonds and cash. This approach prioritizes capital preservation over growth.",
            RiskTolerance.MODERATE: "Your balanced allocation provides a mix of growth potential and stability. This approach seeks moderate returns with manageable risk.",
            RiskTolerance.AGGRESSIVE: "Your allocation emphasizes growth with a higher percentage in stocks. This approach seeks higher returns but comes with increased volatility."
        }
        return explanations.get(risk_tolerance, "Balanced approach recommended.")
    
    def _get_investment_guidelines(self, risk_tolerance: RiskTolerance) -> List[str]:
        """Get investment guidelines based on risk tolerance"""
        guidelines = {
            RiskTolerance.CONSERVATIVE: [
                "Focus on dividend-paying stocks and high-grade bonds",
                "Consider index funds for diversification",
                "Maintain adequate emergency fund",
                "Avoid speculative investments",
                "Review portfolio quarterly"
            ],
            RiskTolerance.MODERATE: [
                "Mix of growth and value stocks",
                "Include some international exposure",
                "Consider sector diversification",
                "Regular portfolio rebalancing",
                "Monitor market trends monthly"
            ],
            RiskTolerance.AGGRESSIVE: [
                "Focus on growth stocks and emerging markets",
                "Consider small-cap and technology stocks",
                "May include some speculative investments",
                "Active portfolio management",
                "Stay informed on market developments"
            ]
        }
        return guidelines.get(risk_tolerance, [])
    
    def _analyze_category_scores(self, answers: Dict[str, Any]) -> Dict[str, float]:
        """Analyze scores by category"""
        category_scores = {}
        category_counts = {}
        
        for question in self.questions:
            if question.question_id not in answers:
                continue
            
            category = question.category
            score = self._score_answer(question, answers[question.question_id])
            
            if category not in category_scores:
                category_scores[category] = 0.0
                category_counts[category] = 0
            
            category_scores[category] += score
            category_counts[category] += 1
        
        # Calculate averages
        for category in category_scores:
            if category_counts[category] > 0:
                category_scores[category] = (category_scores[category] / category_counts[category]) * 100
        
        return category_scores
    
    def _get_improvement_suggestions(self, result: RiskAssessmentResult) -> List[str]:
        """Get suggestions for improving investment approach"""
        suggestions = []
        category_scores = self._analyze_category_scores(result.answers)
        
        # Experience-based suggestions
        if category_scores.get("experience", 0) < 50:
            suggestions.append("Consider starting with index funds to gain experience")
            suggestions.append("Read investment books and take online courses")
        
        # Financial stability suggestions
        if category_scores.get("financial", 0) < 50:
            suggestions.append("Build an emergency fund before investing")
            suggestions.append("Consider more conservative investments initially")
        
        # Timeline-based suggestions
        if category_scores.get("timeline", 0) < 30:
            suggestions.append("Focus on short-term, liquid investments")
            suggestions.append("Avoid high-volatility investments")
        
        # Comfort level suggestions
        if category_scores.get("comfort", 0) < 40:
            suggestions.append("Start with small investment amounts")
            suggestions.append("Consider dollar-cost averaging strategy")
        
        return suggestions
    
    def compare_assessments(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Compare user's assessment history"""
        profile = user_profile_manager.get_profile(user_id)
        if not profile or len(profile.risk_assessments) < 2:
            return None
        
        # Get last two assessments
        assessments = sorted(profile.risk_assessments, key=lambda x: x.assessment_date)
        current = assessments[-1]
        previous = assessments[-2]
        
        score_change = current.risk_score - previous.risk_score
        tolerance_changed = current.risk_tolerance != previous.risk_tolerance
        
        return {
            "current_assessment": {
                "score": current.risk_score,
                "tolerance": current.risk_tolerance.value,
                "date": current.assessment_date.isoformat()
            },
            "previous_assessment": {
                "score": previous.risk_score,
                "tolerance": previous.risk_tolerance.value,
                "date": previous.assessment_date.isoformat()
            },
            "changes": {
                "score_change": score_change,
                "tolerance_changed": tolerance_changed,
                "trend": "increasing" if score_change > 0 else "decreasing" if score_change < 0 else "stable"
            },
            "interpretation": self._interpret_assessment_changes(score_change, tolerance_changed)
        }
    
    def _interpret_assessment_changes(self, score_change: float, tolerance_changed: bool) -> str:
        """Interpret changes in risk assessment"""
        if abs(score_change) < 5:
            return "Your risk tolerance remains relatively stable."
        elif score_change > 10:
            return "Your risk tolerance has increased significantly. Consider reviewing your portfolio allocation."
        elif score_change < -10:
            return "Your risk tolerance has decreased significantly. You may want to adopt a more conservative approach."
        elif tolerance_changed:
            return "Your risk tolerance category has changed. Consider adjusting your investment strategy accordingly."
        else:
            return "Minor changes in your risk profile detected."

# Global risk assessment service instance
risk_assessment_service = RiskAssessmentService()
