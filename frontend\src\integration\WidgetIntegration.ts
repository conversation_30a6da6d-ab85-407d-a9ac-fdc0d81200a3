import { FinanceGPTWidgetConfig } from '../FinanceGPTWidget';

export interface IntegrationOptions {
  containerId: string;
  config: FinanceGPTWidgetConfig;
  onReady?: () => void;
  onError?: (error: Error) => void;
}

export interface IframeIntegrationOptions {
  containerId: string;
  widgetUrl: string;
  config: FinanceGPTWidgetConfig;
  onMessage?: (data: any) => void;
  onReady?: () => void;
}

export class FinanceGPTIntegration {
  private static instances: Map<string, FinanceGPTIntegration> = new Map();
  private container: HTMLElement;
  private config: FinanceGPTWidgetConfig;
  private iframe?: HTMLIFrameElement;
  private reactRoot?: any;

  constructor(private options: IntegrationOptions) {
    const container = document.getElementById(options.containerId);
    if (!container) {
      throw new Error(`Container with id "${options.containerId}" not found`);
    }
    this.container = container;
    this.config = options.config;
    FinanceGPTIntegration.instances.set(options.containerId, this);
  }

  // Direct React component integration
  async initializeReactComponent(): Promise<void> {
    try {
      // Dynamic import to avoid bundling React if not needed
      const React = await import('react');
      const ReactDOM = await import('react-dom/client');
      const { FinanceGPTWidget } = await import('../FinanceGPTWidget');

      // Create React root and render widget
      this.reactRoot = ReactDOM.createRoot(this.container);
      this.reactRoot.render(
        React.createElement(FinanceGPTWidget, {
          config: this.config,
          className: 'integrated-widget'
        })
      );

      this.options.onReady?.();
    } catch (error) {
      this.options.onError?.(error as Error);
    }
  }

  // Update widget configuration
  updateConfig(newConfig: Partial<FinanceGPTWidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };
    
    if (this.iframe) {
      // Send config update to iframe
      this.iframe.contentWindow?.postMessage({
        type: 'CONFIG_UPDATE',
        config: newConfig
      }, '*');
    } else if (this.reactRoot) {
      // Re-render React component with new config
      const React = require('react');
      const { FinanceGPTWidget } = require('../FinanceGPTWidget');
      
      this.reactRoot.render(
        React.createElement(FinanceGPTWidget, {
          config: this.config,
          className: 'integrated-widget'
        })
      );
    }
  }

  // Destroy widget instance
  destroy(): void {
    if (this.iframe) {
      this.iframe.remove();
    }
    if (this.reactRoot) {
      this.reactRoot.unmount();
    }
    this.container.innerHTML = '';
    FinanceGPTIntegration.instances.delete(this.options.containerId);
  }

  // Get widget instance by container ID
  static getInstance(containerId: string): FinanceGPTIntegration | undefined {
    return FinanceGPTIntegration.instances.get(containerId);
  }
}

export class FinanceGPTIframeIntegration {
  private iframe: HTMLIFrameElement;
  private container: HTMLElement;
  private config: FinanceGPTWidgetConfig;

  constructor(private options: IframeIntegrationOptions) {
    const container = document.getElementById(options.containerId);
    if (!container) {
      throw new Error(`Container with id "${options.containerId}" not found`);
    }
    this.container = container;
    this.config = options.config;
    this.iframe = this.createIframe();
  }

  private createIframe(): HTMLIFrameElement {
    const iframe = document.createElement('iframe');
    
    // Configure iframe
    iframe.src = this.buildIframeUrl();
    iframe.style.width = this.config.width || '100%';
    iframe.style.height = this.config.height || '600px';
    iframe.style.border = 'none';
    iframe.style.borderRadius = this.config.styling?.borderRadius || '12px';
    iframe.allow = 'clipboard-read; clipboard-write';
    iframe.sandbox = 'allow-scripts allow-same-origin allow-forms allow-popups';

    // Setup message listener
    window.addEventListener('message', this.handleMessage.bind(this));

    // Add to container
    this.container.appendChild(iframe);

    return iframe;
  }

  private buildIframeUrl(): string {
    const url = new URL(this.options.widgetUrl);
    url.searchParams.set('config', btoa(JSON.stringify(this.config)));
    url.searchParams.set('mode', 'widget');
    return url.toString();
  }

  private handleMessage(event: MessageEvent): void {
    // Verify origin for security
    const allowedOrigins = [
      new URL(this.options.widgetUrl).origin,
      window.location.origin
    ];
    
    if (!allowedOrigins.includes(event.origin)) {
      return;
    }

    const { type, data } = event.data;

    switch (type) {
      case 'WIDGET_READY':
        this.options.onReady?.();
        break;
      case 'USER_ACTION':
        this.config.onUserAction?.(data.action, data.payload);
        break;
      case 'PLAN_CHANGE':
        this.config.onPlanChange?.(data.newPlan);
        break;
      case 'TOKEN_USAGE':
        this.config.onTokenUsage?.(data.tokensUsed);
        break;
      default:
        this.options.onMessage?.(event.data);
    }
  }

  // Send message to iframe
  sendMessage(type: string, data?: any): void {
    this.iframe.contentWindow?.postMessage({ type, data }, '*');
  }

  // Update configuration
  updateConfig(newConfig: Partial<FinanceGPTWidgetConfig>): void {
    this.config = { ...this.config, ...newConfig };
    this.sendMessage('CONFIG_UPDATE', newConfig);
  }

  // Destroy iframe
  destroy(): void {
    this.iframe.remove();
    window.removeEventListener('message', this.handleMessage);
  }
}

// Global initialization function for script tag integration
declare global {
  interface Window {
    FinanceGPT: {
      init: (options: IntegrationOptions) => FinanceGPTIntegration;
      initIframe: (options: IframeIntegrationOptions) => FinanceGPTIframeIntegration;
      presets: {
        chatOnly: (containerId: string) => FinanceGPTIntegration;
        portfolioOnly: (containerId: string) => FinanceGPTIntegration;
        analyticsOnly: (containerId: string) => FinanceGPTIntegration;
        fullWidget: (containerId: string) => FinanceGPTIntegration;
      };
    };
  }
}

// Initialize global FinanceGPT object
if (typeof window !== 'undefined') {
  window.FinanceGPT = {
    init: (options: IntegrationOptions) => new FinanceGPTIntegration(options),
    
    initIframe: (options: IframeIntegrationOptions) => new FinanceGPTIframeIntegration(options),
    
    presets: {
      chatOnly: (containerId: string) => {
        return new FinanceGPTIntegration({
          containerId,
          config: {
            mode: 'chat',
            height: '500px',
            features: { chat: true, portfolio: false, analytics: false }
          }
        });
      },
      
      portfolioOnly: (containerId: string) => {
        return new FinanceGPTIntegration({
          containerId,
          config: {
            mode: 'portfolio',
            height: '600px',
            features: { chat: false, portfolio: true, analytics: false }
          }
        });
      },
      
      analyticsOnly: (containerId: string) => {
        return new FinanceGPTIntegration({
          containerId,
          config: {
            mode: 'analytics',
            height: '500px',
            features: { chat: false, portfolio: false, analytics: true }
          }
        });
      },
      
      fullWidget: (containerId: string) => {
        return new FinanceGPTIntegration({
          containerId,
          config: {
            mode: 'full',
            height: '700px',
            features: { chat: true, portfolio: true, analytics: true }
          }
        });
      }
    }
  };
}

export default FinanceGPTIntegration;
