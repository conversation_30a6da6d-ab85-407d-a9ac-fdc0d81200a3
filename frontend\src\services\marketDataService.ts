// Real-time Market Data Service
export interface StockData {
  ticker: string;
  currentPrice: number;
  priceChange: number;
  priceChangePercent: number;
  volume: number;
  marketCap: string;
  peRatio: number;
  dividend: number;
  high52Week: number;
  low52Week: number;
  openPrice: number;
  previousClose: number;
  dayHigh: number;
  dayLow: number;
  avgVolume: number;
  beta: number;
  eps: number;
  lastUpdated: string;
}

export interface TechnicalIndicators {
  rsi: number;
  macd: {
    macd: number;
    signal: number;
    histogram: number;
  };
  sma20: number;
  sma50: number;
  sma200: number;
  ema12: number;
  ema26: number;
  bollingerBands: {
    upper: number;
    middle: number;
    lower: number;
  };
  stochastic: {
    k: number;
    d: number;
  };
}

export interface FundamentalData {
  revenue: number;
  netIncome: number;
  totalAssets: number;
  totalDebt: number;
  freeCashFlow: number;
  returnOnEquity: number;
  returnOnAssets: number;
  debtToEquity: number;
  currentRatio: number;
  quickRatio: number;
  grossMargin: number;
  operatingMargin: number;
  profitMargin: number;
  bookValue: number;
  priceToBook: number;
  priceToSales: number;
  pegRatio: number;
  dividendYield: number;
  payoutRatio: number;
  earningsGrowth: number;
  revenueGrowth: number;
}

export interface NewsItem {
  title: string;
  summary: string;
  url: string;
  source: string;
  publishedAt: string;
  sentiment: 'positive' | 'negative' | 'neutral';
  relevanceScore: number;
}

export interface AnalystRating {
  firm: string;
  rating: 'Strong Buy' | 'Buy' | 'Hold' | 'Sell' | 'Strong Sell';
  targetPrice: number;
  date: string;
}

class MarketDataService {
  private baseUrl = 'http://localhost:8000/api';
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 60000; // 1 minute cache

  private isValidTicker(ticker: string): boolean {
    return /^[A-Z]{1,5}$/.test(ticker.toUpperCase());
  }

  private getCachedData(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  private getStockDatabase() {
    // Comprehensive stock database with accurate company names and data
    return {
      // Technology Giants
      'aapl': { ticker: 'AAPL', company: 'Apple Inc.', price: '$189.25', sector: 'Technology', marketCap: '$2.9T', peRatio: 29.8, dividend: 0.5 },
      'apple': { ticker: 'AAPL', company: 'Apple Inc.', price: '$189.25', sector: 'Technology', marketCap: '$2.9T', peRatio: 29.8, dividend: 0.5 },
      'msft': { ticker: 'MSFT', company: 'Microsoft Corporation', price: '$378.85', sector: 'Technology', marketCap: '$2.8T', peRatio: 34.2, dividend: 0.7 },
      'microsoft': { ticker: 'MSFT', company: 'Microsoft Corporation', price: '$378.85', sector: 'Technology', marketCap: '$2.8T', peRatio: 34.2, dividend: 0.7 },
      'googl': { ticker: 'GOOGL', company: 'Alphabet Inc.', price: '$142.56', sector: 'Technology', marketCap: '$1.8T', peRatio: 25.1 },
      'google': { ticker: 'GOOGL', company: 'Alphabet Inc.', price: '$142.56', sector: 'Technology', marketCap: '$1.8T', peRatio: 25.1 },
      'amzn': { ticker: 'AMZN', company: 'Amazon.com Inc.', price: '$145.78', sector: 'E-commerce', marketCap: '$1.5T', peRatio: 42.3 },
      'amazon': { ticker: 'AMZN', company: 'Amazon.com Inc.', price: '$145.78', sector: 'E-commerce', marketCap: '$1.5T', peRatio: 42.3 },
      'meta': { ticker: 'META', company: 'Meta Platforms Inc.', price: '$298.45', sector: 'Social Media', marketCap: '$758B', peRatio: 23.7 },
      'facebook': { ticker: 'META', company: 'Meta Platforms Inc.', price: '$298.45', sector: 'Social Media', marketCap: '$758B', peRatio: 23.7 },
      'tsla': { ticker: 'TSLA', company: 'Tesla Inc.', price: '$248.50', sector: 'Electric Vehicles', marketCap: '$789B', peRatio: 62.1, volatility: 'High' },
      'tesla': { ticker: 'TSLA', company: 'Tesla Inc.', price: '$248.50', sector: 'Electric Vehicles', marketCap: '$789B', peRatio: 62.1, volatility: 'High' },
      'nflx': { ticker: 'NFLX', company: 'Netflix Inc.', price: '$425.89', sector: 'Entertainment', marketCap: '$189B', peRatio: 34.8 },
      'netflix': { ticker: 'NFLX', company: 'Netflix Inc.', price: '$425.89', sector: 'Entertainment', marketCap: '$189B', peRatio: 34.8 },
      'nvda': { ticker: 'NVDA', company: 'NVIDIA Corporation', price: '$875.30', sector: 'Semiconductors', marketCap: '$2.2T', peRatio: 65.8 },
      'nvidia': { ticker: 'NVDA', company: 'NVIDIA Corporation', price: '$875.30', sector: 'Semiconductors', marketCap: '$2.2T', peRatio: 65.8 },

      // Airlines
      'luv': { ticker: 'LUV', company: 'Southwest Airlines Co.', price: '$28.45', sector: 'Airlines', marketCap: '$16.8B', peRatio: 12.4, dividend: 0.72 },
      'southwest': { ticker: 'LUV', company: 'Southwest Airlines Co.', price: '$28.45', sector: 'Airlines', marketCap: '$16.8B', peRatio: 12.4, dividend: 0.72 },
      'dal': { ticker: 'DAL', company: 'Delta Air Lines Inc.', price: '$42.67', sector: 'Airlines', marketCap: '$27.3B', peRatio: 8.9, dividend: 0.15 },
      'delta': { ticker: 'DAL', company: 'Delta Air Lines Inc.', price: '$42.67', sector: 'Airlines', marketCap: '$27.3B', peRatio: 8.9, dividend: 0.15 },
      'aal': { ticker: 'AAL', company: 'American Airlines Group Inc.', price: '$14.23', sector: 'Airlines', marketCap: '$9.2B', peRatio: null, volatility: 'High' },
      'american': { ticker: 'AAL', company: 'American Airlines Group Inc.', price: '$14.23', sector: 'Airlines', marketCap: '$9.2B', peRatio: null, volatility: 'High' },
      'ual': { ticker: 'UAL', company: 'United Airlines Holdings Inc.', price: '$45.89', sector: 'Airlines', marketCap: '$14.9B', peRatio: 6.8 },
      'united': { ticker: 'UAL', company: 'United Airlines Holdings Inc.', price: '$45.89', sector: 'Airlines', marketCap: '$14.9B', peRatio: 6.8 },

      // Financial Services
      'jpm': { ticker: 'JPM', company: 'JPMorgan Chase & Co.', price: '$154.32', sector: 'Financial Services', marketCap: '$452B', peRatio: 11.8, dividend: 2.9 },
      'jpmorgan': { ticker: 'JPM', company: 'JPMorgan Chase & Co.', price: '$154.32', sector: 'Financial Services', marketCap: '$452B', peRatio: 11.8, dividend: 2.9 },
      'bac': { ticker: 'BAC', company: 'Bank of America Corporation', price: '$34.56', sector: 'Financial Services', marketCap: '$278B', peRatio: 12.1, dividend: 2.4 },
      'gs': { ticker: 'GS', company: 'The Goldman Sachs Group Inc.', price: '$389.45', sector: 'Financial Services', marketCap: '$133B', peRatio: 13.2, dividend: 2.8 },
      'ms': { ticker: 'MS', company: 'Morgan Stanley', price: '$87.23', sector: 'Financial Services', marketCap: '$147B', peRatio: 14.5, dividend: 3.1 },

      // Healthcare
      'jnj': { ticker: 'JNJ', company: 'Johnson & Johnson', price: '$162.78', sector: 'Healthcare', marketCap: '$428B', peRatio: 15.6, dividend: 3.0 },
      'johnson': { ticker: 'JNJ', company: 'Johnson & Johnson', price: '$162.78', sector: 'Healthcare', marketCap: '$428B', peRatio: 15.6, dividend: 3.0 },
      'pfizer': { ticker: 'PFE', company: 'Pfizer Inc.', price: '$29.45', sector: 'Healthcare', marketCap: '$165B', peRatio: 13.8, dividend: 5.8 },
      'pfe': { ticker: 'PFE', company: 'Pfizer Inc.', price: '$29.45', sector: 'Healthcare', marketCap: '$165B', peRatio: 13.8, dividend: 5.8 },
      'mrk': { ticker: 'MRK', company: 'Merck & Co. Inc.', price: '$98.67', sector: 'Healthcare', marketCap: '$250B', peRatio: 16.2, dividend: 2.7 },
      'merck': { ticker: 'MRK', company: 'Merck & Co. Inc.', price: '$98.67', sector: 'Healthcare', marketCap: '$250B', peRatio: 16.2, dividend: 2.7 },

      // Energy
      'xom': { ticker: 'XOM', company: 'Exxon Mobil Corporation', price: '$102.34', sector: 'Energy', marketCap: '$425B', peRatio: 14.3, dividend: 5.9 },
      'exxon': { ticker: 'XOM', company: 'Exxon Mobil Corporation', price: '$102.34', sector: 'Energy', marketCap: '$425B', peRatio: 14.3, dividend: 5.9 },
      'cvx': { ticker: 'CVX', company: 'Chevron Corporation', price: '$145.67', sector: 'Energy', marketCap: '$278B', peRatio: 13.8, dividend: 3.4 },
      'chevron': { ticker: 'CVX', company: 'Chevron Corporation', price: '$145.67', sector: 'Energy', marketCap: '$278B', peRatio: 13.8, dividend: 3.4 },

      // Retail
      'wmt': { ticker: 'WMT', company: 'Walmart Inc.', price: '$56.78', sector: 'Retail', marketCap: '$456B', peRatio: 26.4, dividend: 2.3 },
      'walmart': { ticker: 'WMT', company: 'Walmart Inc.', price: '$56.78', sector: 'Retail', marketCap: '$456B', peRatio: 26.4, dividend: 2.3 },
      'hd': { ticker: 'HD', company: 'The Home Depot Inc.', price: '$345.89', sector: 'Retail', marketCap: '$356B', peRatio: 24.1, dividend: 2.5 },
      'homedepot': { ticker: 'HD', company: 'The Home Depot Inc.', price: '$345.89', sector: 'Retail', marketCap: '$356B', peRatio: 24.1, dividend: 2.5 },

      // Aerospace
      'ba': { ticker: 'BA', company: 'The Boeing Company', price: '$189.45', sector: 'Aerospace', marketCap: '$112B', peRatio: null, volatility: 'High' },
      'boeing': { ticker: 'BA', company: 'The Boeing Company', price: '$189.45', sector: 'Aerospace', marketCap: '$112B', peRatio: null, volatility: 'High' },
      'lmt': { ticker: 'LMT', company: 'Lockheed Martin Corporation', price: '$423.67', sector: 'Aerospace', marketCap: '$108B', peRatio: 18.9, dividend: 2.8 },

      // Quantum Computing
      'qbts': { ticker: 'QBTS', company: 'D-Wave Quantum Inc.', price: '$1.23', sector: 'Quantum Computing', marketCap: '$89.5M', peRatio: null, volatility: 'Very High' },
      'ionq': { ticker: 'IONQ', company: 'IonQ Inc.', price: '$8.45', sector: 'Quantum Computing', marketCap: '$1.7B', peRatio: null, volatility: 'Very High' },
      'rgti': { ticker: 'RGTI', company: 'Rigetti Computing Inc.', price: '$2.34', sector: 'Quantum Computing', marketCap: '$456M', peRatio: null, volatility: 'Very High' },

      // Semiconductors
      'amd': { ticker: 'AMD', company: 'Advanced Micro Devices Inc.', price: '$134.56', sector: 'Semiconductors', marketCap: '$217B', peRatio: 45.8 },
      'intc': { ticker: 'INTC', company: 'Intel Corporation', price: '$23.45', sector: 'Semiconductors', marketCap: '$98B', peRatio: 18.9, dividend: 4.8 },
      'intel': { ticker: 'INTC', company: 'Intel Corporation', price: '$23.45', sector: 'Semiconductors', marketCap: '$98B', peRatio: 18.9, dividend: 4.8 }
    };
  }

  // Generate realistic mock data for development using our stock database
  private generateMockStockData(ticker: string): StockData {
    // Use our comprehensive stock database
    const stockDatabase = this.getStockDatabase();

    const tickerLower = ticker.toLowerCase();
    const stockInfo = stockDatabase[tickerLower];

    if (stockInfo) {
      // Use data from our database
      const basePrice = parseFloat(stockInfo.price.replace('$', ''));
      const change = (Math.random() - 0.5) * (basePrice * 0.1); // ±10% of price
      const changePercent = (change / basePrice) * 100;

      // Parse market cap
      let marketCapValue = 0;
      if (stockInfo.marketCap) {
        const capStr = stockInfo.marketCap.replace('$', '').replace(',', '');
        if (capStr.includes('T')) {
          marketCapValue = parseFloat(capStr.replace('T', '')) * 1000;
        } else if (capStr.includes('B')) {
          marketCapValue = parseFloat(capStr.replace('B', ''));
        } else if (capStr.includes('M')) {
          marketCapValue = parseFloat(capStr.replace('M', '')) / 1000;
        }
      }

      return {
        ticker: stockInfo.ticker,
        currentPrice: Number(basePrice.toFixed(2)),
        priceChange: Number(change.toFixed(2)),
        priceChangePercent: Number(changePercent.toFixed(2)),
        volume: Math.floor(Math.random() * 20000000) + 5000000, // 5M-25M volume
        marketCap: stockInfo.marketCap || `$${marketCapValue.toFixed(1)}B`,
        peRatio: stockInfo.peRatio || Number((Math.random() * 30 + 10).toFixed(2)),
        dividend: stockInfo.dividend || Number((Math.random() * 5).toFixed(2)),
        high52Week: Number((basePrice * (1 + Math.random() * 0.4 + 0.1)).toFixed(2)), // 10-50% higher
        low52Week: Number((basePrice * (1 - Math.random() * 0.3 - 0.1)).toFixed(2)), // 10-40% lower
        openPrice: Number((basePrice + (Math.random() - 0.5) * 2).toFixed(2)),
        previousClose: Number((basePrice - change).toFixed(2)),
        dayHigh: Number((basePrice + Math.random() * 3).toFixed(2)),
        dayLow: Number((basePrice - Math.random() * 3).toFixed(2)),
        avgVolume: Math.floor(Math.random() * 15000000) + 8000000, // 8M-23M avg volume
        beta: Number((Math.random() * 1.5 + 0.7).toFixed(2)), // 0.7-2.2 beta
        eps: Number((Math.random() * 8 + 2).toFixed(2)), // $2-$10 EPS
        lastUpdated: new Date().toISOString()
      };
    }

    // Fallback for unknown tickers - generate realistic data
    const basePrice = Math.random() * 150 + 25; // $25-$175
    const change = (Math.random() - 0.5) * 8; // -$4 to +$4
    const changePercent = (change / basePrice) * 100;

    return {
      ticker: ticker.toUpperCase(),
      currentPrice: Number(basePrice.toFixed(2)),
      priceChange: Number(change.toFixed(2)),
      priceChangePercent: Number(changePercent.toFixed(2)),
      volume: Math.floor(Math.random() * 10000000) + 1000000,
      marketCap: `$${(Math.random() * 200 + 5).toFixed(1)}B`,
      peRatio: Number((Math.random() * 25 + 8).toFixed(2)),
      dividend: Number((Math.random() * 4).toFixed(2)),
      high52Week: Number((basePrice * (1 + Math.random() * 0.5)).toFixed(2)),
      low52Week: Number((basePrice * (1 - Math.random() * 0.3)).toFixed(2)),
      openPrice: Number((basePrice + (Math.random() - 0.5) * 2).toFixed(2)),
      previousClose: Number((basePrice - change).toFixed(2)),
      dayHigh: Number((basePrice + Math.random() * 3).toFixed(2)),
      dayLow: Number((basePrice - Math.random() * 3).toFixed(2)),
      avgVolume: Math.floor(Math.random() * 5000000) + 2000000,
      beta: Number((Math.random() * 2 + 0.5).toFixed(2)),
      eps: Number((Math.random() * 10 + 1).toFixed(2)),
      lastUpdated: new Date().toISOString()
    };
  }

  private generateMockTechnicalIndicators(): TechnicalIndicators {
    return {
      rsi: Number((Math.random() * 100).toFixed(2)),
      macd: {
        macd: Number((Math.random() * 4 - 2).toFixed(3)),
        signal: Number((Math.random() * 4 - 2).toFixed(3)),
        histogram: Number((Math.random() * 2 - 1).toFixed(3))
      },
      sma20: Number((Math.random() * 200 + 50).toFixed(2)),
      sma50: Number((Math.random() * 200 + 50).toFixed(2)),
      sma200: Number((Math.random() * 200 + 50).toFixed(2)),
      ema12: Number((Math.random() * 200 + 50).toFixed(2)),
      ema26: Number((Math.random() * 200 + 50).toFixed(2)),
      bollingerBands: {
        upper: Number((Math.random() * 200 + 100).toFixed(2)),
        middle: Number((Math.random() * 200 + 80).toFixed(2)),
        lower: Number((Math.random() * 200 + 60).toFixed(2))
      },
      stochastic: {
        k: Number((Math.random() * 100).toFixed(2)),
        d: Number((Math.random() * 100).toFixed(2))
      }
    };
  }

  private generateMockFundamentalData(): FundamentalData {
    return {
      revenue: Math.floor(Math.random() * 100000000000), // Up to $100B
      netIncome: Math.floor(Math.random() * 20000000000), // Up to $20B
      totalAssets: Math.floor(Math.random() * 500000000000), // Up to $500B
      totalDebt: Math.floor(Math.random() * 100000000000), // Up to $100B
      freeCashFlow: Math.floor(Math.random() * 30000000000), // Up to $30B
      returnOnEquity: Number((Math.random() * 30).toFixed(2)),
      returnOnAssets: Number((Math.random() * 15).toFixed(2)),
      debtToEquity: Number((Math.random() * 2).toFixed(2)),
      currentRatio: Number((Math.random() * 3 + 0.5).toFixed(2)),
      quickRatio: Number((Math.random() * 2 + 0.3).toFixed(2)),
      grossMargin: Number((Math.random() * 50 + 20).toFixed(2)),
      operatingMargin: Number((Math.random() * 30 + 5).toFixed(2)),
      profitMargin: Number((Math.random() * 25 + 2).toFixed(2)),
      bookValue: Number((Math.random() * 100 + 10).toFixed(2)),
      priceToBook: Number((Math.random() * 5 + 0.5).toFixed(2)),
      priceToSales: Number((Math.random() * 10 + 0.5).toFixed(2)),
      pegRatio: Number((Math.random() * 3 + 0.5).toFixed(2)),
      dividendYield: Number((Math.random() * 6).toFixed(2)),
      payoutRatio: Number((Math.random() * 80 + 10).toFixed(2)),
      earningsGrowth: Number((Math.random() * 40 - 10).toFixed(2)),
      revenueGrowth: Number((Math.random() * 30 - 5).toFixed(2))
    };
  }

  private generateMockNews(ticker: string): NewsItem[] {
    const headlines = [
      `${ticker} Reports Strong Q4 Earnings, Beats Expectations`,
      `Analysts Upgrade ${ticker} Following Strategic Partnership`,
      `${ticker} Announces New Product Launch, Stock Rises`,
      `Market Volatility Affects ${ticker} Trading Volume`,
      `${ticker} CEO Discusses Future Growth Plans in Interview`,
      `Institutional Investors Increase ${ticker} Holdings`,
      `${ticker} Faces Regulatory Scrutiny Over New Policies`,
      `${ticker} Stock Shows Resilience Amid Market Downturn`
    ];

    return headlines.slice(0, 5).map((title, index) => ({
      title,
      summary: `Latest developments regarding ${ticker} and its market performance. This news item provides insights into recent company activities and market reactions.`,
      url: `https://example.com/news/${ticker.toLowerCase()}-${index}`,
      source: ['Reuters', 'Bloomberg', 'CNBC', 'MarketWatch', 'Yahoo Finance'][index % 5],
      publishedAt: new Date(Date.now() - Math.random() * ******** * 7).toISOString(), // Last 7 days
      sentiment: ['positive', 'negative', 'neutral'][Math.floor(Math.random() * 3)] as any,
      relevanceScore: Number((Math.random() * 0.4 + 0.6).toFixed(2)) // 0.6-1.0
    }));
  }

  private generateMockAnalystRatings(ticker: string): AnalystRating[] {
    const firms = ['Goldman Sachs', 'Morgan Stanley', 'JP Morgan', 'Bank of America', 'Citigroup'];
    const ratings: Array<'Strong Buy' | 'Buy' | 'Hold' | 'Sell' | 'Strong Sell'> = 
      ['Strong Buy', 'Buy', 'Hold', 'Sell', 'Strong Sell'];

    return firms.map(firm => ({
      firm,
      rating: ratings[Math.floor(Math.random() * ratings.length)],
      targetPrice: Number((Math.random() * 200 + 50).toFixed(2)),
      date: new Date(Date.now() - Math.random() * ******** * 30).toISOString() // Last 30 days
    }));
  }

  async getStockData(ticker: string): Promise<StockData> {
    if (!this.isValidTicker(ticker)) {
      throw new Error('Invalid ticker symbol');
    }

    const cacheKey = `stock-${ticker}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      // In production, this would call a real API
      // const response = await fetch(`${this.baseUrl}/stock/${ticker}`);
      // const data = await response.json();
      
      // For now, generate realistic mock data
      const data = this.generateMockStockData(ticker);
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching stock data:', error);
      // Return mock data as fallback
      return this.generateMockStockData(ticker);
    }
  }

  async getTechnicalIndicators(ticker: string): Promise<TechnicalIndicators> {
    if (!this.isValidTicker(ticker)) {
      throw new Error('Invalid ticker symbol');
    }

    const cacheKey = `technical-${ticker}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const data = this.generateMockTechnicalIndicators();
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching technical indicators:', error);
      return this.generateMockTechnicalIndicators();
    }
  }

  async getFundamentalData(ticker: string): Promise<FundamentalData> {
    if (!this.isValidTicker(ticker)) {
      throw new Error('Invalid ticker symbol');
    }

    const cacheKey = `fundamental-${ticker}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const data = this.generateMockFundamentalData();
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching fundamental data:', error);
      return this.generateMockFundamentalData();
    }
  }

  async getNews(ticker: string): Promise<NewsItem[]> {
    if (!this.isValidTicker(ticker)) {
      throw new Error('Invalid ticker symbol');
    }

    const cacheKey = `news-${ticker}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const data = this.generateMockNews(ticker);
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching news:', error);
      return this.generateMockNews(ticker);
    }
  }

  async getAnalystRatings(ticker: string): Promise<AnalystRating[]> {
    if (!this.isValidTicker(ticker)) {
      throw new Error('Invalid ticker symbol');
    }

    const cacheKey = `ratings-${ticker}`;
    const cached = this.getCachedData(cacheKey);
    if (cached) return cached;

    try {
      const data = this.generateMockAnalystRatings(ticker);
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Error fetching analyst ratings:', error);
      return this.generateMockAnalystRatings(ticker);
    }
  }

  // Get comprehensive analysis data
  async getComprehensiveAnalysis(ticker: string): Promise<{
    stock: StockData;
    technical: TechnicalIndicators;
    fundamental: FundamentalData;
    news: NewsItem[];
    ratings: AnalystRating[];
  }> {
    const [stock, technical, fundamental, news, ratings] = await Promise.all([
      this.getStockData(ticker),
      this.getTechnicalIndicators(ticker),
      this.getFundamentalData(ticker),
      this.getNews(ticker),
      this.getAnalystRatings(ticker)
    ]);

    return { stock, technical, fundamental, news, ratings };
  }

  // Clear cache
  clearCache(): void {
    this.cache.clear();
  }

  // Get cache stats
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

export const marketDataService = new MarketDataService();
