import React, { useState } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { 
  User,
  Target,
  DollarSign,
  Clock,
  Shield,
  TrendingUp,
  BookOpen,
  CheckCircle,
  ArrowRight,
  ArrowLeft,
  Lightbulb,
  AlertCircle
} from 'lucide-react';

export interface UserProfile {
  experienceLevel: 'beginner' | 'intermediate' | 'advanced';
  investmentGoals: string[];
  riskTolerance: 'conservative' | 'moderate' | 'aggressive';
  timeHorizon: 'short' | 'medium' | 'long';
  monthlyBudget: number;
  hasEmergencyFund: boolean;
  investmentKnowledge: string[];
  preferredSectors: string[];
}

interface BeginnerQuestionnaireProps {
  onComplete: (profile: UserProfile) => void;
  onSkip: () => void;
}

export const BeginnerQuestionnaire: React.FC<BeginnerQuestionnaireProps> = ({
  onComplete,
  onSkip
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [answers, setAnswers] = useState<Partial<UserProfile>>({
    experienceLevel: 'beginner',
    investmentGoals: [],
    riskTolerance: 'moderate',
    timeHorizon: 'medium',
    monthlyBudget: 500,
    hasEmergencyFund: false,
    investmentKnowledge: [],
    preferredSectors: []
  });

  const questions = [
    {
      id: 'experience',
      title: 'Investment Experience',
      subtitle: 'How familiar are you with investing?',
      icon: User,
      component: ExperienceQuestion
    },
    {
      id: 'risk',
      title: 'Risk Tolerance',
      subtitle: 'How comfortable are you with ups and downs?',
      icon: Shield,
      component: RiskQuestion
    },
    {
      id: 'budget',
      title: 'Monthly Budget',
      subtitle: 'How much can you invest each month?',
      icon: DollarSign,
      component: BudgetQuestion
    }
  ];

  const handleNext = () => {
    if (currentStep < questions.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Complete questionnaire
      const profile: UserProfile = {
        experienceLevel: answers.experienceLevel || 'beginner',
        investmentGoals: ['wealth'], // Default goal
        riskTolerance: answers.riskTolerance || 'moderate',
        timeHorizon: 'long', // Default to long-term
        monthlyBudget: answers.monthlyBudget || 500,
        hasEmergencyFund: answers.hasEmergencyFund || false,
        investmentKnowledge: ['stocks'], // Default knowledge
        preferredSectors: ['technology'] // Default sector
      };
      onComplete(profile);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateAnswer = (key: keyof UserProfile, value: any) => {
    setAnswers(prev => ({ ...prev, [key]: value }));
  };

  const currentQuestion = questions[currentStep];
  const CurrentComponent = currentQuestion.component;
  const IconComponent = currentQuestion.icon;

  return (
    <div className="min-h-screen bg-gray-900 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-gray-800 border-gray-700">
        <div className="p-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-4">
              <div className="p-3 bg-blue-600 rounded-full">
                <IconComponent className="h-8 w-8 text-white" />
              </div>
            </div>
            <h1 className="text-2xl font-bold text-white mb-2">
              Quick Setup
            </h1>
            <p className="text-gray-300 mb-4">
              Just 3 quick questions to get started
            </p>

            {/* Progress Bar */}
            <div className="flex items-center justify-center space-x-2 mb-4">
              {questions.map((_, index) => (
                <div
                  key={index}
                  className={`h-2 w-12 rounded-full transition-colors ${
                    index <= currentStep ? 'bg-blue-500' : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>

            <div className="text-sm text-gray-400">
              Question {currentStep + 1} of {questions.length}
            </div>
          </div>

          {/* Question Content */}
          <div className="mb-8">
            <h2 className="text-2xl font-semibold text-white mb-2">
              {currentQuestion.title}
            </h2>
            <p className="text-gray-300 mb-6">
              {currentQuestion.subtitle}
            </p>
            
            <CurrentComponent
              answers={answers}
              updateAnswer={updateAnswer}
            />
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between">
            <div className="flex space-x-3">
              {currentStep > 0 && (
                <Button
                  onClick={handlePrevious}
                  variant="outline"
                  className="border-gray-600 text-gray-300 hover:bg-gray-700"
                >
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Previous
                </Button>
              )}
              
              <Button
                onClick={onSkip}
                variant="outline"
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                Skip for now
              </Button>
            </div>

            <Button
              onClick={handleNext}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {currentStep === questions.length - 1 ? (
                <>
                  Complete Setup
                  <CheckCircle className="h-4 w-4 ml-2" />
                </>
              ) : (
                <>
                  Next
                  <ArrowRight className="h-4 w-4 ml-2" />
                </>
              )}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

// Individual Question Components
interface QuestionProps {
  answers: Partial<UserProfile>;
  updateAnswer: (key: keyof UserProfile, value: any) => void;
}

const ExperienceQuestion: React.FC<QuestionProps> = ({ answers, updateAnswer }) => {
  const options = [
    {
      value: 'beginner',
      title: 'Complete Beginner',
      description: 'I\'m new to investing and want to learn the basics',
      icon: '🌱'
    },
    {
      value: 'intermediate',
      title: 'Some Experience',
      description: 'I have some knowledge and maybe a few investments',
      icon: '📈'
    },
    {
      value: 'advanced',
      title: 'Experienced Investor',
      description: 'I actively manage investments and understand markets',
      icon: '🎯'
    }
  ];

  return (
    <div className="space-y-3">
      {options.map((option) => (
        <button
          key={option.value}
          onClick={() => updateAnswer('experienceLevel', option.value)}
          className={`w-full p-4 rounded-lg border-2 text-left transition-all ${
            answers.experienceLevel === option.value
              ? 'border-blue-500 bg-blue-500/10'
              : 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
          }`}
        >
          <div className="flex items-start space-x-3">
            <span className="text-2xl">{option.icon}</span>
            <div>
              <h3 className="text-white font-semibold">{option.title}</h3>
              <p className="text-gray-300 text-sm">{option.description}</p>
            </div>
          </div>
        </button>
      ))}
      
      {answers.experienceLevel === 'beginner' && (
        <div className="mt-4 p-4 bg-blue-900/20 border border-blue-500/30 rounded-lg">
          <div className="flex items-start space-x-3">
            <Lightbulb className="h-5 w-5 text-blue-400 mt-0.5" />
            <div>
              <h4 className="text-blue-300 font-medium">Perfect! We'll help you get started</h4>
              <p className="text-blue-200 text-sm mt-1">
                We'll provide educational content, simple explanations, and beginner-friendly investment suggestions.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};



const RiskQuestion: React.FC<QuestionProps> = ({ answers, updateAnswer }) => {
  const riskLevels = [
    {
      value: 'conservative',
      title: 'Play it Safe',
      description: 'I prefer steady, predictable growth',
      icon: '🛡️'
    },
    {
      value: 'moderate',
      title: 'Balanced Approach',
      description: 'I want some growth with reasonable safety',
      icon: '⚖️'
    },
    {
      value: 'aggressive',
      title: 'Growth Focused',
      description: 'I\'m willing to take risks for higher returns',
      icon: '🚀'
    }
  ];

  return (
    <div className="space-y-3">
      {riskLevels.map((level) => (
        <button
          key={level.value}
          onClick={() => updateAnswer('riskTolerance', level.value)}
          className={`w-full p-4 rounded-lg border-2 text-left transition-all ${
            answers.riskTolerance === level.value
              ? 'border-blue-500 bg-blue-500/10'
              : 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
          }`}
        >
          <div className="flex items-center space-x-3">
            <span className="text-2xl">{level.icon}</span>
            <div>
              <h3 className="text-white font-semibold">{level.title}</h3>
              <p className="text-gray-300 text-sm">{level.description}</p>
            </div>
          </div>
        </button>
      ))}
    </div>
  );
};



const BudgetQuestion: React.FC<QuestionProps> = ({ answers, updateAnswer }) => {
  const budgetRanges = [
    { value: 100, label: '$100 or less' },
    { value: 250, label: '$100 - $500' },
    { value: 750, label: '$500 - $1,000' },
    { value: 1500, label: '$1,000+' }
  ];

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-3">
        {budgetRanges.map((range) => (
          <button
            key={range.value}
            onClick={() => updateAnswer('monthlyBudget', range.value)}
            className={`p-4 rounded-lg border-2 text-center transition-all ${
              answers.monthlyBudget === range.value
                ? 'border-blue-500 bg-blue-500/10'
                : 'border-gray-600 hover:border-gray-500 bg-gray-700/50'
            }`}
          >
            <DollarSign className="h-6 w-6 text-blue-400 mx-auto mb-2" />
            <h3 className="text-white font-semibold text-sm">{range.label}</h3>
          </button>
        ))}
      </div>

      <div className="mt-4 p-3 bg-blue-900/20 border border-blue-500/30 rounded-lg">
        <p className="text-blue-200 text-sm text-center">
          💡 Start small and increase over time as you get comfortable
        </p>
      </div>
    </div>
  );
};


