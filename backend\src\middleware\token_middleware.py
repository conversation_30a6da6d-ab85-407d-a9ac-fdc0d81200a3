"""
Token Usage Tracking Middleware for FinanceGPT Pro
Handles token consumption and usage tracking for API requests
"""

from fastapi import Request, HTTPException, Depends
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from typing import Optional, Dict, Any
import time
import logging
from datetime import datetime

from ..models.token_models import (
    token_manager, 
    AnalysisType, 
    TokenPricing,
    SubscriptionTier
)

logger = logging.getLogger(__name__)

# Security scheme for token authentication
security = HTTPBearer(auto_error=False)

class TokenMiddleware:
    """Middleware for token usage tracking and validation"""
    
    def __init__(self):
        self.request_start_times = {}
    
    async def __call__(self, request: Request, call_next):
        """Process request with token tracking"""
        start_time = time.time()
        request_id = id(request)
        self.request_start_times[request_id] = start_time
        
        # Skip token validation for certain endpoints
        if self._should_skip_token_validation(request.url.path):
            response = await call_next(request)
            return response
        
        try:
            # Extract user information from request
            user_info = await self._extract_user_info(request)
            
            # Validate tokens before processing
            validation_result = await self._validate_tokens(request, user_info)
            if not validation_result["valid"]:
                raise HTTPException(
                    status_code=402,
                    detail=validation_result["error"]
                )
            
            # Add user info to request state
            request.state.user_id = user_info["user_id"]
            request.state.analysis_type = validation_result["analysis_type"]
            request.state.token_cost = validation_result["token_cost"]
            
            # Process the request
            response = await call_next(request)
            
            # Track successful usage
            await self._track_usage(request, response, success=True)
            
            return response
            
        except HTTPException:
            # Re-raise HTTP exceptions
            raise
        except Exception as e:
            logger.error(f"Token middleware error: {str(e)}")
            # Track failed usage
            if hasattr(request.state, 'user_id'):
                await self._track_usage(request, None, success=False)
            raise HTTPException(status_code=500, detail="Internal server error")
        finally:
            # Clean up
            if request_id in self.request_start_times:
                del self.request_start_times[request_id]
    
    def _should_skip_token_validation(self, path: str) -> bool:
        """Check if token validation should be skipped for this path"""
        skip_paths = [
            "/",
            "/health",
            "/docs",
            "/openapi.json",
            "/token/purchase",
            "/auth/login",
            "/auth/register"
        ]
        return any(path.startswith(skip_path) for skip_path in skip_paths)
    
    async def _extract_user_info(self, request: Request) -> Dict[str, Any]:
        """Extract user information from request"""
        # For demo purposes, we'll use a default user
        # In production, this would extract from JWT token or session
        
        # Try to get user from headers
        user_id = request.headers.get("X-User-ID", "demo_user")
        username = request.headers.get("X-Username", "Demo User")
        
        # Ensure demo user exists
        user = token_manager.get_user(user_id)
        if not user:
            user = token_manager.create_user(
                user_id=user_id,
                username=username,
                email=f"{user_id}@demo.com",
                subscription_tier=SubscriptionTier.PROFESSIONAL
            )
        
        return {
            "user_id": user_id,
            "username": username,
            "user": user
        }
    
    async def _validate_tokens(self, request: Request, user_info: Dict) -> Dict[str, Any]:
        """Validate if user has sufficient tokens for the request"""
        user = user_info["user"]
        
        # Determine analysis type from request
        analysis_type = self._determine_analysis_type(request)
        token_cost = TokenPricing.get_cost(analysis_type)
        
        # Check if user can afford the analysis
        if not user.can_afford(token_cost):
            return {
                "valid": False,
                "error": {
                    "message": "Insufficient tokens",
                    "required_tokens": token_cost,
                    "available_tokens": user.current_tokens,
                    "suggestion": "Purchase more tokens to continue"
                }
            }
        
        # Check daily limits
        if not user.has_daily_limit_remaining(token_cost):
            from ..models.token_models import SUBSCRIPTION_TIERS
            config = SUBSCRIPTION_TIERS[user.subscription_tier]
            return {
                "valid": False,
                "error": {
                    "message": "Daily limit exceeded",
                    "daily_limit": config.daily_limit,
                    "daily_usage": user.daily_usage,
                    "suggestion": "Upgrade subscription for higher limits"
                }
            }
        
        return {
            "valid": True,
            "analysis_type": analysis_type,
            "token_cost": token_cost
        }
    
    def _determine_analysis_type(self, request: Request) -> AnalysisType:
        """Determine analysis type from request"""
        path = request.url.path
        
        # Check for specific analysis endpoints
        if "/analyze" in path:
            # Try to determine from request body or query params
            if hasattr(request, 'json') and request.method == "POST":
                # This would need to be extracted from the actual request body
                # For now, we'll use a default
                return AnalysisType.COMPREHENSIVE_ANALYSIS
            else:
                return AnalysisType.CUSTOM_QUERY
        elif "/image" in path:
            return AnalysisType.IMAGE_ANALYSIS
        else:
            return AnalysisType.CUSTOM_QUERY
    
    async def _track_usage(self, request: Request, response, success: bool = True):
        """Track token usage for analytics"""
        if not hasattr(request.state, 'user_id'):
            return
        
        user_id = request.state.user_id
        analysis_type = getattr(request.state, 'analysis_type', AnalysisType.CUSTOM_QUERY)
        token_cost = getattr(request.state, 'token_cost', 0)
        
        if success and token_cost > 0:
            # Process the token consumption
            result = token_manager.process_analysis_request(user_id, analysis_type)
            
            if result["success"]:
                logger.info(f"Tokens consumed: {token_cost} for user {user_id}, analysis: {analysis_type.value}")
            else:
                logger.warning(f"Failed to consume tokens for user {user_id}: {result.get('error')}")
        
        # Log the request for analytics
        request_duration = time.time() - self.request_start_times.get(id(request), time.time())
        
        logger.info(f"Request tracked - User: {user_id}, Analysis: {analysis_type.value}, "
                   f"Tokens: {token_cost}, Success: {success}, Duration: {request_duration:.2f}s")

# Global middleware instance
token_middleware = TokenMiddleware()

# Dependency for getting current user
async def get_current_user(credentials: Optional[HTTPAuthorizationCredentials] = Depends(security)):
    """Dependency to get current user from token"""
    # For demo purposes, return a default user
    # In production, this would validate JWT token
    
    user_id = "demo_user"
    user = token_manager.get_user(user_id)
    
    if not user:
        user = token_manager.create_user(
            user_id=user_id,
            username="Demo User",
            email="<EMAIL>",
            subscription_tier=SubscriptionTier.PROFESSIONAL
        )
    
    return user

# Dependency for checking token requirements
def require_tokens(analysis_type: AnalysisType):
    """Dependency factory for requiring specific token amounts"""
    
    async def check_tokens(user = Depends(get_current_user)):
        token_cost = TokenPricing.get_cost(analysis_type)
        
        if not user.can_afford(token_cost):
            raise HTTPException(
                status_code=402,
                detail={
                    "message": "Insufficient tokens",
                    "required_tokens": token_cost,
                    "available_tokens": user.current_tokens
                }
            )
        
        if not user.has_daily_limit_remaining(token_cost):
            from ..models.token_models import SUBSCRIPTION_TIERS
            config = SUBSCRIPTION_TIERS[user.subscription_tier]
            raise HTTPException(
                status_code=429,
                detail={
                    "message": "Daily limit exceeded",
                    "daily_limit": config.daily_limit,
                    "daily_usage": user.daily_usage
                }
            )
        
        return user
    
    return check_tokens

# Utility functions for token management
class TokenUtils:
    """Utility functions for token operations"""
    
    @staticmethod
    def get_user_token_status(user_id: str) -> Dict[str, Any]:
        """Get comprehensive token status for a user"""
        user = token_manager.get_user(user_id)
        if not user:
            return {"error": "User not found"}
        
        subscription_status = user.get_subscription_status()
        
        return {
            "user_id": user_id,
            "current_tokens": user.current_tokens,
            "subscription": subscription_status,
            "usage_stats": {
                "total_tokens_used": user.total_tokens_used,
                "total_tokens_purchased": user.total_tokens_purchased,
                "daily_usage": user.daily_usage
            },
            "limits": {
                "daily_limit": subscription_status["daily_limit"],
                "daily_remaining": subscription_status["daily_remaining"]
            }
        }
    
    @staticmethod
    def estimate_analysis_cost(analysis_types: list) -> Dict[str, int]:
        """Estimate token cost for multiple analysis types"""
        costs = {}
        total = 0
        
        for analysis_type_str in analysis_types:
            try:
                analysis_type = AnalysisType(analysis_type_str)
                cost = TokenPricing.get_cost(analysis_type)
                costs[analysis_type_str] = cost
                total += cost
            except ValueError:
                costs[analysis_type_str] = TokenPricing.CUSTOM_QUERY
                total += TokenPricing.CUSTOM_QUERY
        
        costs["total"] = total
        return costs
    
    @staticmethod
    def get_pricing_info() -> Dict[str, Any]:
        """Get current pricing information"""
        return {
            "analysis_costs": {
                "price_check": TokenPricing.PRICE_CHECK,
                "technical_analysis": TokenPricing.COMPREHENSIVE_TECHNICAL,
                "sentiment_analysis": TokenPricing.BASIC_SENTIMENT,
                "fundamental_analysis": TokenPricing.FUNDAMENTAL_ANALYSIS,
                "investment_recommendation": TokenPricing.INVESTMENT_RECOMMENDATION,
                "comprehensive_analysis": TokenPricing.COMPREHENSIVE_ANALYSIS,
                "image_analysis": TokenPricing.IMAGE_ANALYSIS,
                "custom_query": TokenPricing.CUSTOM_QUERY
            },
            "token_packages": {
                "starter": {"tokens": 500, "bonus": 0, "price": 4.99},
                "professional": {"tokens": 2000, "bonus": 200, "price": 14.99},
                "enterprise": {"tokens": 5000, "bonus": 1000, "price": 29.99},
                "unlimited": {"tokens": 999999, "bonus": 0, "price": 99.99}
            }
        }

# Export utilities
token_utils = TokenUtils()
