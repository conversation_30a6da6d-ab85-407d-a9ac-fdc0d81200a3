"""
Educational Content Service for FinanceGPT Pro
Provides educational resources, tutorials, and learning materials
"""

import logging
from typing import Dict, List, Any, Optional
from datetime import datetime
from enum import Enum
from dataclasses import dataclass

logger = logging.getLogger(__name__)

class EducationLevel(Enum):
    """Education levels for content targeting"""
    BEGINNER = "beginner"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"

class ContentType(Enum):
    """Types of educational content"""
    ARTICLE = "article"
    VIDEO = "video"
    TUTORIAL = "tutorial"
    GLOSSARY = "glossary"
    CASE_STUDY = "case_study"
    QUIZ = "quiz"

@dataclass
class EducationalContent:
    """Educational content item"""
    content_id: str
    title: str
    content_type: ContentType
    level: EducationLevel
    category: str
    description: str
    content: str
    duration_minutes: int
    tags: List[str]
    created_date: datetime
    updated_date: datetime

class EducationalService:
    """Service for managing educational content"""
    
    def __init__(self):
        self.content_library = self._initialize_content_library()
        self.user_progress = {}  # In production, this would be in a database
    
    def _initialize_content_library(self) -> List[EducationalContent]:
        """Initialize the educational content library"""
        content = [
            EducationalContent(
                content_id="basics_001",
                title="Stock Market Basics: What Are Stocks?",
                content_type=ContentType.ARTICLE,
                level=EducationLevel.BEGINNER,
                category="fundamentals",
                description="Learn the fundamental concepts of stocks and how the stock market works",
                content="""
# Stock Market Basics: What Are Stocks?

## Introduction
Stocks represent ownership shares in a company. When you buy stock, you become a partial owner of that business.

## Key Concepts
- **Share**: A unit of ownership in a company
- **Dividend**: Payments made to shareholders from company profits
- **Market Cap**: Total value of all company shares
- **Volatility**: How much a stock's price fluctuates

## How Stock Prices Work
Stock prices are determined by supply and demand. When more people want to buy than sell, prices go up. When more want to sell than buy, prices go down.

## Types of Stocks
1. **Common Stock**: Voting rights, potential dividends
2. **Preferred Stock**: Fixed dividends, no voting rights
3. **Growth Stocks**: Companies expected to grow faster than average
4. **Value Stocks**: Undervalued companies trading below their intrinsic value

## Getting Started
1. Open a brokerage account
2. Research companies you understand
3. Start with small investments
4. Diversify your portfolio
5. Think long-term

Remember: All investing involves risk, and you can lose money.
                """,
                duration_minutes=10,
                tags=["stocks", "basics", "investing", "beginner"],
                created_date=datetime.now(),
                updated_date=datetime.now()
            ),
            
            EducationalContent(
                content_id="technical_001",
                title="Understanding Technical Analysis",
                content_type=ContentType.TUTORIAL,
                level=EducationLevel.INTERMEDIATE,
                category="technical_analysis",
                description="Learn how to read charts and use technical indicators",
                content="""
# Understanding Technical Analysis

## What is Technical Analysis?
Technical analysis is the study of price movements and trading volume to predict future price direction.

## Key Principles
1. **Price discounts everything**: All information is reflected in the price
2. **Prices move in trends**: Trends tend to continue until broken
3. **History repeats**: Past patterns often repeat in the future

## Essential Chart Types
- **Line Charts**: Simple price movement over time
- **Candlestick Charts**: Show open, high, low, close prices
- **Bar Charts**: Similar to candlesticks but different format

## Popular Technical Indicators
1. **Moving Averages**: Smooth out price data to identify trends
2. **RSI (Relative Strength Index)**: Measures overbought/oversold conditions
3. **MACD**: Shows relationship between two moving averages
4. **Bollinger Bands**: Indicate volatility and potential price levels

## Support and Resistance
- **Support**: Price level where buying interest emerges
- **Resistance**: Price level where selling pressure appears

## Chart Patterns
- **Head and Shoulders**: Reversal pattern
- **Double Top/Bottom**: Reversal patterns
- **Triangles**: Continuation patterns
- **Flags and Pennants**: Short-term continuation patterns

## Risk Management
- Always use stop-loss orders
- Don't risk more than 2% per trade
- Diversify your analysis methods
                """,
                duration_minutes=25,
                tags=["technical_analysis", "charts", "indicators", "patterns"],
                created_date=datetime.now(),
                updated_date=datetime.now()
            ),
            
            EducationalContent(
                content_id="portfolio_001",
                title="Building a Diversified Portfolio",
                content_type=ContentType.ARTICLE,
                level=EducationLevel.INTERMEDIATE,
                category="portfolio_management",
                description="Learn how to build and manage a well-diversified investment portfolio",
                content="""
# Building a Diversified Portfolio

## What is Diversification?
Diversification means spreading your investments across different assets to reduce risk.

## Types of Diversification
1. **Asset Class Diversification**: Stocks, bonds, real estate, commodities
2. **Geographic Diversification**: Domestic and international investments
3. **Sector Diversification**: Technology, healthcare, finance, etc.
4. **Company Size Diversification**: Large-cap, mid-cap, small-cap stocks

## Portfolio Allocation Strategies
### Conservative Portfolio (Low Risk)
- 60% Bonds
- 30% Large-cap stocks
- 10% International stocks

### Moderate Portfolio (Medium Risk)
- 40% Bonds
- 40% Large-cap stocks
- 15% International stocks
- 5% Small-cap stocks

### Aggressive Portfolio (High Risk)
- 20% Bonds
- 50% Large-cap stocks
- 20% International stocks
- 10% Small-cap/Growth stocks

## Rebalancing Your Portfolio
- Review quarterly
- Rebalance when allocations drift 5% from targets
- Consider tax implications
- Use new money to rebalance when possible

## Common Mistakes to Avoid
1. Over-diversification (too many similar investments)
2. Under-diversification (too concentrated)
3. Emotional investing
4. Trying to time the market
5. Ignoring fees and expenses
                """,
                duration_minutes=20,
                tags=["portfolio", "diversification", "allocation", "risk_management"],
                created_date=datetime.now(),
                updated_date=datetime.now()
            ),
            
            EducationalContent(
                content_id="glossary_001",
                title="Investment Glossary",
                content_type=ContentType.GLOSSARY,
                level=EducationLevel.BEGINNER,
                category="reference",
                description="Essential investment terms and definitions",
                content="""
# Investment Glossary

## A
**Ask Price**: The lowest price a seller is willing to accept for a security.

**Asset**: Anything of value that can be owned or controlled to produce value.

**Asset Allocation**: The process of dividing investments among different asset categories.

## B
**Bear Market**: A market condition where prices are falling or expected to fall.

**Beta**: A measure of a stock's volatility relative to the overall market.

**Bid Price**: The highest price a buyer is willing to pay for a security.

**Blue Chip**: Stocks of large, well-established companies with a history of reliable performance.

**Bull Market**: A market condition where prices are rising or expected to rise.

## C
**Capital Gains**: Profit from the sale of an asset for more than its purchase price.

**Compound Interest**: Interest calculated on the initial principal and accumulated interest.

## D
**Dividend**: A payment made by companies to shareholders from profits.

**Dividend Yield**: Annual dividend per share divided by the stock price.

## E
**Earnings Per Share (EPS)**: Company's profit divided by outstanding shares.

**ETF (Exchange-Traded Fund)**: A fund that trades on stock exchanges like individual stocks.

## F
**Fundamental Analysis**: Evaluating securities by examining financial and economic factors.

## M
**Market Capitalization**: Total value of a company's shares (shares × price per share).

**Mutual Fund**: A pooled investment vehicle managed by professionals.

## P
**P/E Ratio**: Price-to-earnings ratio, comparing stock price to earnings per share.

**Portfolio**: A collection of investments held by an individual or institution.

## R
**Risk Tolerance**: An investor's ability and willingness to lose some or all of their investment.

**ROI (Return on Investment)**: A measure of investment efficiency.

## V
**Volatility**: The degree of variation in a security's price over time.

**Volume**: The number of shares traded during a specific period.
                """,
                duration_minutes=15,
                tags=["glossary", "definitions", "terms", "reference"],
                created_date=datetime.now(),
                updated_date=datetime.now()
            )
        ]
        
        return content
    
    def get_content_by_level(self, level: EducationLevel) -> List[Dict[str, Any]]:
        """Get educational content filtered by level"""
        filtered_content = [
            content for content in self.content_library 
            if content.level == level
        ]
        
        return [self._content_to_dict(content) for content in filtered_content]
    
    def get_content_by_category(self, category: str) -> List[Dict[str, Any]]:
        """Get educational content filtered by category"""
        filtered_content = [
            content for content in self.content_library 
            if content.category == category
        ]
        
        return [self._content_to_dict(content) for content in filtered_content]
    
    def search_content(self, query: str) -> List[Dict[str, Any]]:
        """Search educational content by query"""
        query_lower = query.lower()
        matching_content = []
        
        for content in self.content_library:
            if (query_lower in content.title.lower() or 
                query_lower in content.description.lower() or
                any(tag.lower() == query_lower for tag in content.tags)):
                matching_content.append(content)
        
        return [self._content_to_dict(content) for content in matching_content]
    
    def get_content_by_id(self, content_id: str) -> Optional[Dict[str, Any]]:
        """Get specific educational content by ID"""
        for content in self.content_library:
            if content.content_id == content_id:
                return self._content_to_dict(content)
        return None
    
    def get_recommended_content(self, user_level: EducationLevel, 
                              interests: List[str] = None) -> List[Dict[str, Any]]:
        """Get recommended content based on user level and interests"""
        recommended = []
        
        # Start with content at user's level
        level_content = self.get_content_by_level(user_level)
        
        if interests:
            # Filter by interests
            for content in level_content:
                if any(interest.lower() in content['tags'] for interest in interests):
                    recommended.append(content)
        else:
            recommended = level_content
        
        # Sort by relevance (for now, just by creation date)
        recommended.sort(key=lambda x: x['created_date'], reverse=True)
        
        return recommended[:5]  # Return top 5 recommendations
    
    def _content_to_dict(self, content: EducationalContent) -> Dict[str, Any]:
        """Convert EducationalContent to dictionary"""
        return {
            "content_id": content.content_id,
            "title": content.title,
            "content_type": content.content_type.value,
            "level": content.level.value,
            "category": content.category,
            "description": content.description,
            "content": content.content,
            "duration_minutes": content.duration_minutes,
            "tags": content.tags,
            "created_date": content.created_date.isoformat(),
            "updated_date": content.updated_date.isoformat()
        }
    
    def get_learning_path(self, user_level: EducationLevel) -> List[Dict[str, Any]]:
        """Get a structured learning path for the user"""
        if user_level == EducationLevel.BEGINNER:
            path_order = ["basics_001", "glossary_001", "portfolio_001"]
        elif user_level == EducationLevel.INTERMEDIATE:
            path_order = ["portfolio_001", "technical_001"]
        else:  # Advanced
            path_order = ["technical_001"]
        
        learning_path = []
        for content_id in path_order:
            content = self.get_content_by_id(content_id)
            if content:
                learning_path.append(content)
        
        return learning_path

# Global service instance
educational_service = EducationalService()
