from fastapi import FastAP<PERSON>, HTTPException, UploadFile, File, Depends, Request, WebSocket, WebSocketDisconnect
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager
import uuid
import asyncio
import os
import tempfile
from pathlib import Path
from .graph import graph
from .configuration import get_config
from .token_aware_graph import token_aware_engine
from ..models.token_models import token_manager, AnalysisType
from ..middleware.token_middleware import get_current_user
from ..api.token_endpoints import router as token_router
from ..api.portfolio_endpoints import router as portfolio_router
from ..services.websocket_service import websocket_service
from ..services.market_data_service import market_data_service

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Lifespan event handler for startup and shutdown"""
    # Startup
    try:
        await market_data_service.start()
        print("✅ Market data service started")
    except Exception as e:
        print(f"❌ Failed to start market data service: {str(e)}")

    yield

    # Shutdown
    try:
        await market_data_service.stop()
        print("✅ Market data service stopped")
    except Exception as e:
        print(f"❌ Error stopping market data service: {str(e)}")

# Initialize FastAPI app with lifespan
app = FastAPI(
    title="FinanceGPT Pro API",
    description="Professional stock analysis with token-based usage tracking and real-time data",
    version="2.0.0",
    lifespan=lifespan
)

# Add CORS middleware for frontend integration
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins for development
    allow_credentials=False,  # Set to False for wildcard origins
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allow_headers=["*"],
)

# Include token management routes
app.include_router(token_router)

# Include portfolio management routes
app.include_router(portfolio_router)

# Pydantic models for request/response
class AnalysisRequest(BaseModel):
    query: str
    thread_id: Optional[str] = None
    analysis_type: Optional[str] = None
    user_id: Optional[str] = None

class Message(BaseModel):
    role: str
    content: str

class TokenInfo(BaseModel):
    tokens_consumed: int
    remaining_tokens: int
    daily_usage: int
    daily_remaining: int
    analysis_type: str

class AnalysisResponse(BaseModel):
    thread_id: str
    messages: List[Message]
    status: str
    token_info: Optional[TokenInfo] = None
    analysis_info: Optional[Dict[str, Any]] = None
    user_info: Optional[Dict[str, Any]] = None

@app.get("/")
async def root():
    return {
        "message": "🚀 FinanceGPT Pro API is running!",
        "version": "2.0.0",
        "features": [
            "Token-based usage tracking",
            "Professional subscription tiers",
            "Real-time stock price data",
            "Technical analysis with indicators",
            "Market sentiment analysis",
            "Comprehensive research reports",
            "Multi-step AI reasoning",
            "Image chart analysis",
            "Portfolio optimization"
        ],
        "endpoints": {
            "analysis": "/analyze",
            "tokens": "/api/tokens",
            "portfolio": "/api/portfolio",
            "health": "/health"
        }
    }

@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint for production monitoring"""
    import time
    from datetime import datetime

    health_status = {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "2.0.0",
        "environment": os.getenv("ENVIRONMENT", "development"),
        "checks": {}
    }

    # Check API responsiveness
    start_time = time.time()
    health_status["checks"]["api"] = {
        "status": "healthy",
        "response_time_ms": round((time.time() - start_time) * 1000, 2)
    }

    # Check database connection (if configured)
    try:
        database_url = os.getenv("DATABASE_URL")
        if database_url:
            # Simple database check would go here
            health_status["checks"]["database"] = {"status": "healthy"}
        else:
            health_status["checks"]["database"] = {"status": "not_configured"}
    except Exception as e:
        health_status["checks"]["database"] = {"status": "unhealthy", "error": str(e)}

    # Check Redis connection (if configured)
    try:
        redis_url = os.getenv("REDIS_URL")
        if redis_url:
            # Simple Redis check would go here
            health_status["checks"]["redis"] = {"status": "healthy"}
        else:
            health_status["checks"]["redis"] = {"status": "not_configured"}
    except Exception as e:
        health_status["checks"]["redis"] = {"status": "unhealthy", "error": str(e)}

    # Check external APIs
    health_status["checks"]["external_apis"] = {
        "google_api": "configured" if os.getenv("GOOGLE_API_KEY") else "not_configured",
        "stocks_api": "configured" if os.getenv("STOCKS_API_KEY") else "not_configured"
    }

    # Overall health determination
    unhealthy_checks = [
        check for check in health_status["checks"].values()
        if isinstance(check, dict) and check.get("status") == "unhealthy"
    ]

    if unhealthy_checks:
        health_status["status"] = "unhealthy"

    return health_status

# WebSocket endpoint for real-time data
@app.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket endpoint for real-time market data and notifications"""
    await websocket_service.handle_websocket(websocket, user_id)

@app.get("/ws/stats")
async def websocket_stats():
    """Get WebSocket service statistics"""
    return {
        "websocket_stats": websocket_service.get_stats(),
        "market_data_stats": market_data_service.get_stats()
    }

@app.post("/market/subscribe")
async def subscribe_to_ticker(request: dict):
    """Subscribe to real-time updates for a ticker (HTTP fallback)"""
    user_id = request.get("user_id")
    ticker = request.get("ticker", "").upper()

    if not user_id or not ticker:
        raise HTTPException(status_code=400, detail="user_id and ticker are required")

    # This would typically be handled via WebSocket, but providing HTTP fallback
    success = websocket_service.manager.subscribe_to_ticker(user_id, ticker)

    return {
        "success": success,
        "message": f"{'Subscribed to' if success else 'Failed to subscribe to'} {ticker}",
        "ticker": ticker
    }

@app.get("/market/price/{ticker}")
async def get_current_price(ticker: str):
    """Get current price for a ticker"""
    ticker = ticker.upper()

    try:
        price_update = await market_data_service.get_current_price(ticker)
        if price_update:
            return {
                "success": True,
                "data": {
                    "ticker": price_update.ticker,
                    "price": price_update.price,
                    "change": price_update.change,
                    "change_percent": price_update.change_percent,
                    "volume": price_update.volume,
                    "timestamp": price_update.timestamp,
                    "market_status": price_update.market_status
                }
            }
        else:
            raise HTTPException(status_code=404, detail=f"Price data not available for {ticker}")
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get price for {ticker}: {str(e)}")

# Real-time data endpoints are now integrated with lifespan management

@app.post("/analyze")
async def analyze_stock(request: AnalysisRequest):
    """
    Analyze a stock using the token-aware LangGraph agent
    """
    try:
        # Generate thread ID if not provided
        thread_id = request.thread_id or str(uuid.uuid4())

        # Get user ID (in production, this would come from JWT token)
        user_id = request.user_id or "demo_user"

        # Use token-aware analysis engine
        result = await token_aware_engine.analyze_with_tokens(
            query=request.query,
            user_id=user_id,
            thread_id=thread_id,
            force_analysis_type=request.analysis_type
        )

        if not result["success"]:
            # Handle token-related errors
            if "token_info" in result:
                raise HTTPException(
                    status_code=402,
                    detail={
                        "message": result["error"],
                        "token_info": result["token_info"]
                    }
                )
            else:
                raise HTTPException(status_code=500, detail=result["error"])

        # Extract messages from result
        messages = []
        for msg in result.get("messages", []):
            messages.append(Message(
                role=msg.get("role", "assistant"),
                content=msg.get("content", "")
            ))

        # Create token info
        token_info = None
        if "token_info" in result:
            token_info = TokenInfo(
                tokens_consumed=result["token_info"]["tokens_consumed"],
                remaining_tokens=result["token_info"]["remaining_tokens"],
                daily_usage=result["token_info"]["daily_usage"],
                daily_remaining=result["token_info"]["daily_remaining"],
                analysis_type=result["token_info"]["analysis_type"]
            )

        return AnalysisResponse(
            thread_id=thread_id,
            messages=messages,
            status="success",
            token_info=token_info,
            analysis_info=result.get("analysis_info"),
            user_info=result.get("user_info")
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@app.get("/analyze")
async def analyze_stock_get(
    query: str = "Hello",
    thread_id: Optional[str] = None,
    user_id: Optional[str] = None,
    analysis_type: Optional[str] = None
):
    """
    GET endpoint for simple analysis (for backward compatibility)
    """
    request = AnalysisRequest(
        query=query,
        thread_id=thread_id,
        user_id=user_id,
        analysis_type=analysis_type
    )
    return await analyze_stock(request)

@app.post("/analyze/estimate")
async def estimate_analysis_cost(request: AnalysisRequest):
    """
    Get cost estimate for an analysis without performing it
    """
    try:
        estimate = token_aware_engine.get_analysis_cost_estimate(
            query=request.query,
            analysis_type=request.analysis_type
        )

        return {
            "success": True,
            "estimate": estimate
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Cost estimation failed: {str(e)}")

@app.get("/analyze/history/{user_id}")
async def get_analysis_history(user_id: str, limit: int = 10):
    """
    Get analysis history for a user
    """
    try:
        if limit < 1 or limit > 100:
            raise HTTPException(status_code=400, detail="Limit must be between 1 and 100")

        history = token_aware_engine.get_user_analysis_history(user_id, limit)

        return {
            "success": True,
            "history": history,
            "total_count": len(history)
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get analysis history: {str(e)}")

@app.post("/analyze-image")
async def analyze_chart_image(file: UploadFile = File(...)):
    """
    Analyze uploaded stock chart image using AI computer vision
    """
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")

        # Check file size (10MB limit)
        if file.size > 10 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="File too large. Maximum size is 10MB")

        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.png') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # For now, return mock analysis (real implementation would use ChartImageAnalyzer)
            response = {
                "success": True,
                "analysis": {
                    "confidence_score": 0.87,
                    "patterns_detected": [
                        {
                            "type": "Ascending Triangle",
                            "confidence": 0.82,
                            "bullish": True,
                            "completion": 0.7
                        }
                    ],
                    "technical_indicators": {
                        "rsi": 58.2,
                        "sma_5": 175.43,
                        "sma_20": 172.15,
                        "macd": 3.28,
                        "current_price": 175.43
                    },
                    "support_levels": [168.50, 172.20, 174.80],
                    "resistance_levels": [178.90, 182.45, 185.20],
                    "price_targets": {
                        "upside_target": 189.46,
                        "extended_target": 201.74
                    },
                    "risk_levels": {
                        "stop_loss": 166.66,
                        "risk_level": "medium"
                    },
                    "recommendations": {
                        "action": "BUY",
                        "confidence": 0.75,
                        "reasoning": ["Bullish patterns detected", "RSI neutral", "MACD bullish crossover"]
                    }
                },
                "metadata": {
                    "filename": file.filename,
                    "file_size": file.size,
                    "content_type": file.content_type
                }
            }

            return response

        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)