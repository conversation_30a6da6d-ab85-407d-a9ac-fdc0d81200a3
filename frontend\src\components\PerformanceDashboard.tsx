import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { PortfolioCharts } from './PortfolioCharts';
import { AIPortfolioInsights } from './AIPortfolioInsights';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  Target,
  Calendar,
  DollarSign,
  Percent,
  Activity,
  Award,
  AlertTriangle
} from 'lucide-react';

interface PortfolioHolding {
  ticker: string;
  company_name: string;
  shares: number;
  average_cost: number;
  current_price: number;
  market_value: number;
  unrealized_gain_loss: number;
  unrealized_gain_loss_percent: number;
  sector: string;
}

interface Portfolio {
  portfolio_id: string;
  name: string;
  total_value: number;
  total_gain_loss: number;
  total_gain_loss_percent: number;
  holdings: PortfolioHolding[];
  cash_balance: number;
  created_date: string;
  last_updated: string;
}

interface PerformanceMetrics {
  totalReturn: number;
  totalReturnPercent: number;
  dayChange: number;
  dayChangePercent: number;
  weekChange: number;
  weekChangePercent: number;
  monthChange: number;
  monthChangePercent: number;
  yearChange: number;
  yearChangePercent: number;
  volatility: number;
  sharpeRatio: number;
  maxDrawdown: number;
  winRate: number;
}

interface PerformanceDashboardProps {
  portfolio: Portfolio;
  userTier: 'Basic' | 'Pro';
  onTokenDeduct: (amount: number) => void;
}

export const PerformanceDashboard: React.FC<PerformanceDashboardProps> = ({
  portfolio,
  userTier,
  onTokenDeduct
}) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState<'1D' | '1W' | '1M' | '3M' | '1Y' | 'ALL'>('1M');
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics | null>(null);

  // Demo performance metrics
  useEffect(() => {
    const demoMetrics: PerformanceMetrics = {
      totalReturn: portfolio.total_gain_loss,
      totalReturnPercent: portfolio.total_gain_loss_percent,
      dayChange: 1250.75,
      dayChangePercent: 0.99,
      weekChange: -875.25,
      weekChangePercent: -0.69,
      monthChange: 3420.50,
      monthChangePercent: 2.79,
      yearChange: 8750.50,
      yearChangePercent: 7.48,
      volatility: 18.5,
      sharpeRatio: 1.24,
      maxDrawdown: -12.3,
      winRate: 68.5
    };
    setPerformanceMetrics(demoMetrics);
  }, [portfolio]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  };

  const getSectorAllocation = () => {
    const sectorMap = new Map<string, number>();
    portfolio.holdings.forEach(holding => {
      const current = sectorMap.get(holding.sector) || 0;
      sectorMap.set(holding.sector, current + holding.market_value);
    });
    
    const total = portfolio.holdings.reduce((sum, holding) => sum + holding.market_value, 0);
    return Array.from(sectorMap.entries()).map(([sector, value]) => ({
      sector,
      value,
      percentage: (value / total) * 100
    }));
  };

  const getTopPerformers = () => {
    return portfolio.holdings
      .filter(h => h.unrealized_gain_loss_percent > 0)
      .sort((a, b) => b.unrealized_gain_loss_percent - a.unrealized_gain_loss_percent)
      .slice(0, 5);
  };

  const getWorstPerformers = () => {
    return portfolio.holdings
      .filter(h => h.unrealized_gain_loss_percent < 0)
      .sort((a, b) => a.unrealized_gain_loss_percent - b.unrealized_gain_loss_percent)
      .slice(0, 5);
  };

  const getPortfolioHealthScore = () => {
    if (!performanceMetrics) return 0;
    
    // Simple scoring algorithm based on multiple factors
    let score = 50; // Base score
    
    // Performance factor (40% weight)
    if (performanceMetrics.totalReturnPercent > 10) score += 20;
    else if (performanceMetrics.totalReturnPercent > 5) score += 15;
    else if (performanceMetrics.totalReturnPercent > 0) score += 10;
    else if (performanceMetrics.totalReturnPercent > -5) score += 5;
    else score -= 10;
    
    // Diversification factor (30% weight)
    const sectorCount = getSectorAllocation().length;
    if (sectorCount >= 5) score += 15;
    else if (sectorCount >= 3) score += 10;
    else if (sectorCount >= 2) score += 5;
    
    // Risk factor (30% weight)
    if (performanceMetrics.sharpeRatio > 1.5) score += 15;
    else if (performanceMetrics.sharpeRatio > 1.0) score += 10;
    else if (performanceMetrics.sharpeRatio > 0.5) score += 5;
    
    return Math.min(Math.max(score, 0), 100);
  };

  const timeframes = [
    { key: '1D', label: '1 Day' },
    { key: '1W', label: '1 Week' },
    { key: '1M', label: '1 Month' },
    { key: '3M', label: '3 Months' },
    { key: '1Y', label: '1 Year' },
    { key: 'ALL', label: 'All Time' }
  ];

  const sectorAllocation = getSectorAllocation();
  const topPerformers = getTopPerformers();
  const worstPerformers = getWorstPerformers();
  const healthScore = getPortfolioHealthScore();

  if (!performanceMetrics) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Performance Overview */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
          <BarChart3 className="h-5 w-5 mr-2 text-blue-400" />
          Performance Overview
        </h3>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center p-4 bg-white/5 rounded-lg">
            <div className="text-2xl font-bold text-white mb-1">
              {formatCurrency(performanceMetrics.totalReturn)}
            </div>
            <div className={`text-sm mb-2 ${performanceMetrics.totalReturnPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPercent(performanceMetrics.totalReturnPercent)}
            </div>
            <div className="text-xs text-gray-400">Total Return</div>
          </div>

          <div className="text-center p-4 bg-white/5 rounded-lg">
            <div className="text-2xl font-bold text-white mb-1">
              {formatCurrency(performanceMetrics.dayChange)}
            </div>
            <div className={`text-sm mb-2 ${performanceMetrics.dayChangePercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPercent(performanceMetrics.dayChangePercent)}
            </div>
            <div className="text-xs text-gray-400">Today's Change</div>
          </div>

          <div className="text-center p-4 bg-white/5 rounded-lg">
            <div className="text-2xl font-bold text-white mb-1">
              {formatCurrency(performanceMetrics.monthChange)}
            </div>
            <div className={`text-sm mb-2 ${performanceMetrics.monthChangePercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPercent(performanceMetrics.monthChangePercent)}
            </div>
            <div className="text-xs text-gray-400">This Month</div>
          </div>
        </div>
      </Card>



      {/* Holdings Performance */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-white mb-6 flex items-center">
          <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
          Holdings Performance
        </h3>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Top Performers */}
          <div>
            <h4 className="text-lg font-medium text-white mb-4 flex items-center">
              <Award className="h-4 w-4 mr-2 text-green-400" />
              Best Performers
            </h4>
            <div className="space-y-3">
              {topPerformers.slice(0, 3).map((holding) => (
                <div key={holding.ticker} className="flex items-center justify-between p-3 bg-green-900/20 rounded-lg">
                  <div>
                    <span className="font-semibold text-white">{holding.ticker}</span>
                    <p className="text-gray-300 text-sm">{holding.company_name}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-green-400 font-medium">
                      {formatPercent(holding.unrealized_gain_loss_percent)}
                    </p>
                    <p className="text-green-300 text-sm">
                      {formatCurrency(holding.unrealized_gain_loss)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Worst Performers */}
          <div>
            <h4 className="text-lg font-medium text-white mb-4 flex items-center">
              <AlertTriangle className="h-4 w-4 mr-2 text-red-400" />
              Needs Attention
            </h4>
            <div className="space-y-3">
              {worstPerformers.slice(0, 3).map((holding) => (
                <div key={holding.ticker} className="flex items-center justify-between p-3 bg-red-900/20 rounded-lg">
                  <div>
                    <span className="font-semibold text-white">{holding.ticker}</span>
                    <p className="text-gray-300 text-sm">{holding.company_name}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-red-400 font-medium">
                      {formatPercent(holding.unrealized_gain_loss_percent)}
                    </p>
                    <p className="text-red-300 text-sm">
                      {formatCurrency(holding.unrealized_gain_loss)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </Card>

      {/* Portfolio Summary */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-xl font-semibold text-white flex items-center">
            <Activity className="h-5 w-5 mr-2 text-blue-400" />
            Portfolio Summary
          </h3>
          <Badge className={`text-white px-3 py-1 ${
            healthScore >= 80 ? 'bg-green-600' :
            healthScore >= 60 ? 'bg-blue-600' :
            healthScore >= 40 ? 'bg-yellow-600' : 'bg-red-600'
          }`}>
            Health: {healthScore}/100
          </Badge>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="text-center p-3 bg-white/5 rounded-lg">
            <p className="text-gray-400 text-sm">Diversification</p>
            <p className="text-white font-semibold">{sectorAllocation.length} sectors</p>
          </div>
          <div className="text-center p-3 bg-white/5 rounded-lg">
            <p className="text-gray-400 text-sm">Risk Level</p>
            <p className="text-white font-semibold">{performanceMetrics.volatility.toFixed(1)}% volatility</p>
          </div>
          <div className="text-center p-3 bg-white/5 rounded-lg">
            <p className="text-gray-400 text-sm">Performance</p>
            <p className={`font-semibold ${performanceMetrics.totalReturnPercent >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPercent(performanceMetrics.totalReturnPercent)}
            </p>
          </div>
        </div>

        <p className="text-gray-300 text-sm">
          {healthScore >= 80 && '✅ Your portfolio is performing well with good diversification.'}
          {healthScore >= 60 && healthScore < 80 && '⚠️ Good performance, but consider improving diversification.'}
          {healthScore >= 40 && healthScore < 60 && '⚠️ Consider rebalancing and adding more diversification.'}
          {healthScore < 40 && '🚨 Your portfolio needs attention - consider professional advice.'}
        </p>
      </Card>
    </div>
  );
};
