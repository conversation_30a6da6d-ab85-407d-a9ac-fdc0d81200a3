/**
 * Portfolio API Service
 * Handles all portfolio-related API calls to the backend
 */

const API_BASE_URL = '/api/portfolio';

export interface PortfolioHolding {
  ticker: string;
  company_name: string;
  shares: number;
  average_cost: number;
  current_price: number;
  market_value: number;
  unrealized_gain_loss: number;
  unrealized_gain_loss_percent: number;
  sector: string;
}

export interface Portfolio {
  portfolio_id: string;
  name: string;
  total_value: number;
  total_gain_loss: number;
  total_gain_loss_percent: number;
  holdings: PortfolioHolding[];
  cash_balance: number;
  created_date: string;
  last_updated: string;
  holdings_count?: number;
}

export interface CreatePortfolioRequest {
  name: string;
}

export interface AddHoldingRequest {
  ticker: string;
  shares: number;
  purchase_price: number;
}

export interface UpdateHoldingRequest {
  shares: number;
  purchase_price: number;
}

export interface PerformanceData {
  portfolio_id: string;
  name: string;
  summary: {
    total_value: number;
    total_cost: number;
    total_gain_loss: number;
    total_gain_loss_percent: number;
    cash_balance: number;
    holdings_count: number;
  };
  holdings: PortfolioHolding[];
  sector_allocation: Record<string, { value: number; percentage: number }>;
  top_performers: PortfolioHolding[];
  bottom_performers: PortfolioHolding[];
  risk_metrics: {
    risk_score: number;
    volatility: number;
    beta: number;
    sharpe_ratio: number;
  };
  recommendations: string[];
}

export interface RiskAnalysis {
  portfolio_id: string;
  risk_score: number;
  volatility: number;
  beta: number;
  sharpe_ratio: number;
  max_drawdown: number;
  var_95: number;
  correlation_analysis: {
    market_correlation: number;
    sector_concentration: number;
    diversification_score: number;
  };
  risk_factors: string[];
  recommendations: string[];
}

class PortfolioApiService {
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
    };

    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(errorData.detail || `HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message || 'API request failed');
    }

    return data.data;
  }

  // Portfolio Management
  async getUserPortfolios(): Promise<Portfolio[]> {
    return this.makeRequest<Portfolio[]>('/list');
  }

  async createPortfolio(request: CreatePortfolioRequest): Promise<{ portfolio_id: string; name: string; created_date: string }> {
    return this.makeRequest('/create', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getPortfolioDetails(portfolioId: string): Promise<PerformanceData> {
    return this.makeRequest<PerformanceData>(`/${portfolioId}`);
  }

  async deletePortfolio(portfolioId: string): Promise<void> {
    await this.makeRequest(`/${portfolioId}`, {
      method: 'DELETE',
    });
  }

  // Holdings Management
  async addHolding(portfolioId: string, request: AddHoldingRequest): Promise<{
    ticker: string;
    shares: number;
    average_cost: number;
    market_value: number;
  }> {
    return this.makeRequest(`/${portfolioId}/holdings`, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async updateHolding(
    portfolioId: string,
    ticker: string,
    request: UpdateHoldingRequest
  ): Promise<void> {
    await this.makeRequest(`/${portfolioId}/holdings/${ticker}`, {
      method: 'PUT',
      body: JSON.stringify(request),
    });
  }

  async removeHolding(portfolioId: string, ticker: string): Promise<void> {
    await this.makeRequest(`/${portfolioId}/holdings/${ticker}`, {
      method: 'DELETE',
    });
  }

  // Performance Analysis
  async getPortfolioPerformance(portfolioId: string): Promise<PerformanceData> {
    return this.makeRequest<PerformanceData>(`/${portfolioId}/performance`);
  }

  async getPortfolioComparison(portfolioId: string): Promise<any> {
    return this.makeRequest(`/${portfolioId}/comparison`);
  }

  async getPortfolioOptimization(portfolioId: string): Promise<any> {
    return this.makeRequest(`/${portfolioId}/optimization`);
  }

  // Risk Analysis
  async getRiskAnalysis(portfolioId: string): Promise<RiskAnalysis> {
    return this.makeRequest<RiskAnalysis>(`/${portfolioId}/risk-analysis`);
  }

  // Utility methods
  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  formatPercent(percent: number): string {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  }

  // Demo data fallback for development
  getDemoPortfolio(): Portfolio {
    return {
      portfolio_id: 'demo-portfolio-1',
      name: 'My Investment Portfolio',
      total_value: 125750.50,
      total_gain_loss: 8750.50,
      total_gain_loss_percent: 7.48,
      cash_balance: 5000.00,
      created_date: '2024-01-15T00:00:00Z',
      last_updated: '2024-06-20T12:00:00Z',
      holdings_count: 5,
      holdings: [
        {
          ticker: 'AAPL',
          company_name: 'Apple Inc.',
          shares: 50,
          average_cost: 180.25,
          current_price: 195.50,
          market_value: 9775.00,
          unrealized_gain_loss: 762.50,
          unrealized_gain_loss_percent: 8.45,
          sector: 'Technology'
        },
        {
          ticker: 'MSFT',
          company_name: 'Microsoft Corporation',
          shares: 30,
          average_cost: 320.00,
          current_price: 335.75,
          market_value: 10072.50,
          unrealized_gain_loss: 472.50,
          unrealized_gain_loss_percent: 4.92,
          sector: 'Technology'
        },
        {
          ticker: 'GOOGL',
          company_name: 'Alphabet Inc.',
          shares: 25,
          average_cost: 2650.00,
          current_price: 2720.25,
          market_value: 68006.25,
          unrealized_gain_loss: 1756.25,
          unrealized_gain_loss_percent: 2.65,
          sector: 'Technology'
        },
        {
          ticker: 'TSLA',
          company_name: 'Tesla Inc.',
          shares: 40,
          average_cost: 220.00,
          current_price: 185.75,
          market_value: 7430.00,
          unrealized_gain_loss: -1370.00,
          unrealized_gain_loss_percent: -15.57,
          sector: 'Automotive'
        },
        {
          ticker: 'NVDA',
          company_name: 'NVIDIA Corporation',
          shares: 15,
          average_cost: 450.00,
          current_price: 485.50,
          market_value: 7282.50,
          unrealized_gain_loss: 532.50,
          unrealized_gain_loss_percent: 7.89,
          sector: 'Technology'
        }
      ]
    };
  }
}

export const portfolioApi = new PortfolioApiService();
