"""
Comprehensive Financial Analysis
Provides fundamental analysis, portfolio insights, and financial metrics
"""

import requests
import logging
from typing import Dict, List, Optional
from functools import lru_cache
from ..agent.configuration import get_config

logger = logging.getLogger(__name__)

class FinancialAnalyzer:
    """Comprehensive financial analysis for stocks"""
    
    def __init__(self, ticker: str):
        self.ticker = ticker.upper()
        self.config = get_config()
    
    @lru_cache(maxsize=50)
    def get_fundamental_data(self) -> Dict:
        """Get fundamental financial data"""
        try:
            api_key = self.config.api_keys.stocks_api_key
            if not api_key:
                return self._get_mock_fundamental_data()
            
            # Alpha Vantage Fundamental Data
            url = f"https://www.alphavantage.co/query?function=OVERVIEW&symbol={self.ticker}&apikey={api_key}"
            
            response = requests.get(url, timeout=15)
            response.raise_for_status()
            data = response.json()
            
            if "Error Message" in data or not data:
                return self._get_mock_fundamental_data()
            
            return self._process_fundamental_data(data)
            
        except Exception as e:
            logger.error(f"Error getting fundamental data for {self.ticker}: {str(e)}")
            return self._get_mock_fundamental_data()
    
    def _process_fundamental_data(self, raw_data: Dict) -> Dict:
        """Process raw fundamental data into structured format"""
        try:
            return {
                "ticker": self.ticker,
                "company_name": raw_data.get("Name", "N/A"),
                "sector": raw_data.get("Sector", "N/A"),
                "industry": raw_data.get("Industry", "N/A"),
                "market_cap": self._safe_float(raw_data.get("MarketCapitalization")),
                "pe_ratio": self._safe_float(raw_data.get("PERatio")),
                "peg_ratio": self._safe_float(raw_data.get("PEGRatio")),
                "price_to_book": self._safe_float(raw_data.get("PriceToBookRatio")),
                "dividend_yield": self._safe_float(raw_data.get("DividendYield")),
                "eps": self._safe_float(raw_data.get("EPS")),
                "revenue_ttm": self._safe_float(raw_data.get("RevenueTTM")),
                "profit_margin": self._safe_float(raw_data.get("ProfitMargin")),
                "operating_margin": self._safe_float(raw_data.get("OperatingMarginTTM")),
                "return_on_assets": self._safe_float(raw_data.get("ReturnOnAssetsTTM")),
                "return_on_equity": self._safe_float(raw_data.get("ReturnOnEquityTTM")),
                "debt_to_equity": self._safe_float(raw_data.get("DebtToEquityRatio")),
                "current_ratio": self._safe_float(raw_data.get("CurrentRatio")),
                "book_value": self._safe_float(raw_data.get("BookValue")),
                "shares_outstanding": self._safe_float(raw_data.get("SharesOutstanding")),
                "beta": self._safe_float(raw_data.get("Beta")),
                "52_week_high": self._safe_float(raw_data.get("52WeekHigh")),
                "52_week_low": self._safe_float(raw_data.get("52WeekLow")),
                "analyst_target_price": self._safe_float(raw_data.get("AnalystTargetPrice")),
                "description": raw_data.get("Description", "")[:500]  # Limit description length
            }
        except Exception as e:
            logger.error(f"Error processing fundamental data: {str(e)}")
            return self._get_mock_fundamental_data()
    
    def _safe_float(self, value) -> Optional[float]:
        """Safely convert value to float"""
        try:
            if value is None or value == "None" or value == "-":
                return None
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def _get_mock_fundamental_data(self) -> Dict:
        """Mock fundamental data for demonstration"""
        mock_data = {
            "AAPL": {
                "company_name": "Apple Inc.",
                "sector": "Technology",
                "industry": "Consumer Electronics",
                "market_cap": 3000000000000,  # 3T
                "pe_ratio": 28.5,
                "peg_ratio": 2.1,
                "price_to_book": 45.2,
                "dividend_yield": 0.0044,
                "eps": 6.05,
                "revenue_ttm": 394328000000,
                "profit_margin": 0.253,
                "operating_margin": 0.298,
                "return_on_assets": 0.204,
                "return_on_equity": 1.479,
                "debt_to_equity": 1.73,
                "current_ratio": 0.94,
                "beta": 1.24
            },
            "TSLA": {
                "company_name": "Tesla, Inc.",
                "sector": "Consumer Cyclical",
                "industry": "Auto Manufacturers",
                "market_cap": 800000000000,  # 800B
                "pe_ratio": 85.2,
                "peg_ratio": 3.8,
                "price_to_book": 12.4,
                "dividend_yield": 0.0,
                "eps": 3.62,
                "revenue_ttm": 96773000000,
                "profit_margin": 0.076,
                "operating_margin": 0.096,
                "return_on_assets": 0.056,
                "return_on_equity": 0.193,
                "debt_to_equity": 0.17,
                "current_ratio": 1.29,
                "beta": 2.11
            }
        }
        
        base_data = mock_data.get(self.ticker, mock_data["AAPL"])
        base_data["ticker"] = self.ticker
        return base_data
    
    def analyze_financial_health(self) -> Dict:
        """Analyze financial health of the company"""
        try:
            fundamental_data = self.get_fundamental_data()
            
            health_score = 0
            health_factors = []
            warnings = []
            
            # Profitability analysis
            profit_margin = fundamental_data.get("profit_margin")
            if profit_margin:
                if profit_margin > 0.15:
                    health_score += 20
                    health_factors.append("Strong profit margins")
                elif profit_margin > 0.05:
                    health_score += 10
                    health_factors.append("Adequate profit margins")
                else:
                    warnings.append("Low profit margins")
            
            # Debt analysis
            debt_to_equity = fundamental_data.get("debt_to_equity")
            if debt_to_equity:
                if debt_to_equity < 0.5:
                    health_score += 20
                    health_factors.append("Low debt levels")
                elif debt_to_equity < 1.0:
                    health_score += 10
                    health_factors.append("Moderate debt levels")
                else:
                    warnings.append("High debt levels")
            
            # Liquidity analysis
            current_ratio = fundamental_data.get("current_ratio")
            if current_ratio:
                if current_ratio > 1.5:
                    health_score += 15
                    health_factors.append("Strong liquidity position")
                elif current_ratio > 1.0:
                    health_score += 10
                    health_factors.append("Adequate liquidity")
                else:
                    warnings.append("Potential liquidity concerns")
            
            # ROE analysis
            roe = fundamental_data.get("return_on_equity")
            if roe:
                if roe > 0.15:
                    health_score += 15
                    health_factors.append("Excellent return on equity")
                elif roe > 0.10:
                    health_score += 10
                    health_factors.append("Good return on equity")
                else:
                    warnings.append("Low return on equity")
            
            # PE ratio analysis
            pe_ratio = fundamental_data.get("pe_ratio")
            if pe_ratio:
                if 10 <= pe_ratio <= 25:
                    health_score += 10
                    health_factors.append("Reasonable valuation")
                elif pe_ratio > 50:
                    warnings.append("High valuation - may be overpriced")
            
            # Determine overall health rating
            if health_score >= 70:
                health_rating = "Excellent"
            elif health_score >= 50:
                health_rating = "Good"
            elif health_score >= 30:
                health_rating = "Fair"
            else:
                health_rating = "Poor"
            
            return {
                "ticker": self.ticker,
                "health_score": health_score,
                "health_rating": health_rating,
                "positive_factors": health_factors,
                "warning_factors": warnings,
                "fundamental_data": fundamental_data,
                "analysis_summary": self._generate_health_summary(health_rating, health_factors, warnings)
            }
            
        except Exception as e:
            logger.error(f"Error analyzing financial health: {str(e)}")
            return {"error": str(e)}
    
    def _generate_health_summary(self, rating: str, positives: List[str], warnings: List[str]) -> str:
        """Generate a summary of financial health analysis"""
        summary = f"{self.ticker} has {rating.lower()} financial health. "
        
        if positives:
            summary += f"Strengths include: {', '.join(positives[:3])}. "
        
        if warnings:
            summary += f"Areas of concern: {', '.join(warnings[:3])}. "
        
        summary += "This analysis is based on fundamental financial metrics and should be considered alongside other factors."
        
        return summary
    
    def get_valuation_analysis(self) -> Dict:
        """Perform valuation analysis"""
        try:
            fundamental_data = self.get_fundamental_data()
            
            pe_ratio = fundamental_data.get("pe_ratio")
            peg_ratio = fundamental_data.get("peg_ratio")
            price_to_book = fundamental_data.get("price_to_book")
            
            valuation_signals = []
            
            # PE ratio analysis
            if pe_ratio:
                if pe_ratio < 15:
                    valuation_signals.append("PE ratio suggests potential undervaluation")
                elif pe_ratio > 30:
                    valuation_signals.append("PE ratio suggests potential overvaluation")
                else:
                    valuation_signals.append("PE ratio indicates fair valuation")
            
            # PEG ratio analysis
            if peg_ratio:
                if peg_ratio < 1.0:
                    valuation_signals.append("PEG ratio suggests good value relative to growth")
                elif peg_ratio > 2.0:
                    valuation_signals.append("PEG ratio suggests expensive relative to growth")
            
            # Price-to-book analysis
            if price_to_book:
                if price_to_book < 1.0:
                    valuation_signals.append("Trading below book value - potential value opportunity")
                elif price_to_book > 5.0:
                    valuation_signals.append("High price-to-book ratio - premium valuation")
            
            return {
                "ticker": self.ticker,
                "pe_ratio": pe_ratio,
                "peg_ratio": peg_ratio,
                "price_to_book": price_to_book,
                "valuation_signals": valuation_signals,
                "overall_valuation": self._determine_overall_valuation(pe_ratio, peg_ratio, price_to_book)
            }
            
        except Exception as e:
            logger.error(f"Error in valuation analysis: {str(e)}")
            return {"error": str(e)}
    
    def _determine_overall_valuation(self, pe: Optional[float], peg: Optional[float], pb: Optional[float]) -> str:
        """Determine overall valuation assessment"""
        undervalued_signals = 0
        overvalued_signals = 0
        
        if pe and pe < 15:
            undervalued_signals += 1
        elif pe and pe > 30:
            overvalued_signals += 1
        
        if peg and peg < 1.0:
            undervalued_signals += 1
        elif peg and peg > 2.0:
            overvalued_signals += 1
        
        if pb and pb < 1.0:
            undervalued_signals += 1
        elif pb and pb > 5.0:
            overvalued_signals += 1
        
        if undervalued_signals > overvalued_signals:
            return "Potentially Undervalued"
        elif overvalued_signals > undervalued_signals:
            return "Potentially Overvalued"
        else:
            return "Fairly Valued"
    
    def get_investment_recommendation(self) -> Dict:
        """Generate investment recommendation based on comprehensive analysis"""
        try:
            health_analysis = self.analyze_financial_health()
            valuation_analysis = self.get_valuation_analysis()
            
            # Simple scoring system
            score = 0
            factors = []
            
            # Health score contribution
            health_score = health_analysis.get("health_score", 0)
            score += health_score * 0.4  # 40% weight
            
            # Valuation contribution
            valuation = valuation_analysis.get("overall_valuation", "")
            if "Undervalued" in valuation:
                score += 30
                factors.append("Attractive valuation")
            elif "Overvalued" in valuation:
                score -= 20
                factors.append("High valuation concern")
            
            # Determine recommendation
            if score >= 70:
                recommendation = "Strong Buy"
            elif score >= 50:
                recommendation = "Buy"
            elif score >= 30:
                recommendation = "Hold"
            elif score >= 10:
                recommendation = "Weak Hold"
            else:
                recommendation = "Sell"
            
            return {
                "ticker": self.ticker,
                "recommendation": recommendation,
                "confidence_score": min(100, max(0, int(score))),
                "key_factors": factors,
                "health_analysis": health_analysis,
                "valuation_analysis": valuation_analysis,
                "disclaimer": "This is not financial advice. Please consult with a qualified financial advisor before making investment decisions."
            }
            
        except Exception as e:
            logger.error(f"Error generating investment recommendation: {str(e)}")
            return {"error": str(e)}
