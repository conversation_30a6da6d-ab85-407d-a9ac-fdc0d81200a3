version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: financegpt-postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-financegpt}
      POSTGRES_USER: ${POSTGRES_USER:-financegpt}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_change_me}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - financegpt-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-financegpt}"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: financegpt-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password_change_me}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - financegpt-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: financegpt-backend
    environment:
      - ENVIRONMENT=production
      - DATABASE_URL=postgresql://${POSTGRES_USER:-financegpt}:${POSTGRES_PASSWORD:-secure_password_change_me}@postgres:5432/${POSTGRES_DB:-financegpt}
      - REDIS_URL=redis://:${REDIS_PASSWORD:-redis_password_change_me}@redis:6379/0
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GOOGLE_SEARCH_KEY=${GOOGLE_SEARCH_KEY}
      - GOOGLE_SEARCH_CSE_ID=${GOOGLE_SEARCH_CSE_ID}
      - STOCKS_API_KEY=${STOCKS_API_KEY}
      - JWT_SECRET=${JWT_SECRET:-your_jwt_secret_change_me}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000,https://yourdomain.com}
    volumes:
      - ./backend/logs:/app/logs
      - ./backend/data:/app/data
    ports:
      - "8000:8000"
    networks:
      - financegpt-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: financegpt-frontend
    environment:
      - REACT_APP_API_URL=${REACT_APP_API_URL:-http://localhost:8000}
      - REACT_APP_ENVIRONMENT=production
    ports:
      - "3000:80"
    networks:
      - financegpt-network
    depends_on:
      backend:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 5

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

networks:
  financegpt-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
