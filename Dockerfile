# Stage 1: Build React Frontend
FROM node:22-alpine3.21 AS frontend-builder

# Update Alpine packages to reduce vulnerabilities
RUN apk update && apk upgrade --no-cache

# Set working directory for frontend
WORKDIR /app/frontend

# Copy frontend package files and install dependencies
COPY frontend/package.json ./
COPY frontend/package-lock.json ./
RUN npm config set fetch-timeout 600000 && npm config set fetch-retries 3 && npm install

# Copy the rest of the frontend source code
COPY frontend/ ./

# Build the frontend
RUN npm run build

# Stage 2: Python Backend - Updated to latest secure version
FROM docker.io/langchain/langgraph-api:3.11

# -- Install UV --
RUN apt-get update && apt-get install -y curl && \
    curl -LsSf https://astral.sh/uv/install.sh | sh && \
    apt-get clean && rm -rf /var/lib/apt/lists/*
ENV PATH="/root/.local/bin:$PATH"
# -- End of UV installation --

# -- Copy built frontend from builder stage --
COPY --from=frontend-builder /app/frontend/dist /deps/frontend/dist
# -- End of copying built frontend --

# -- Adding local package --
ADD backend/ /deps/backend
# -- End of local package --

# -- Create necessary __init__.py files --
RUN mkdir -p /deps/backend/src && \
    touch /deps/backend/src/__init__.py
# -- End of creating __init__.py files --

# -- Installing all local dependencies using pip from requirements.txt --
COPY backend/requirements.txt /deps/backend/requirements.txt
RUN pip install --no-cache-dir -r /deps/backend/requirements.txt
# -- End of local dependencies install --

# Set Python path to include the backend source directory
ENV PYTHONPATH="/deps/backend/src"
ENV LANGGRAPH_HTTP='{"app": "/deps/backend/src/agent/app.py:app"}'
ENV LANGSERVE_GRAPHS='{"agent": "/deps/backend/src/agent/graph.py:graph"}'

# -- Ensure user deps didn't inadvertently overwrite langgraph-api --
RUN mkdir -p /api/langgraph_api /api/langgraph_runtime /api/langgraph_license /api/langgraph_storage && \
    touch /api/langgraph_api/__init__.py /api/langgraph_runtime/__init__.py /api/langgraph_license/__init__.py /api/langgraph_storage/__init__.py
RUN PYTHONDONTWRITEBYTECODE=1 pip install --no-cache-dir --no-deps -e /api
# -- End of ensuring user deps didn't inadvertently overwrite langgraph-api --

# -- Removing pip from the final image (but keeping UV) --
RUN uv pip uninstall --system pip setuptools wheel && \
    rm -rf /usr/local/lib/python*/site-packages/pip* /usr/local/lib/python*/site-packages/setuptools* /usr/local/lib/python*/site-packages/wheel* && \
    find /usr/local/bin -name "pip*" -delete
# -- End of pip removal --

WORKDIR /deps/backend

# Command to run when container starts
CMD ["uvicorn", "src.agent.app:app", "--host", "0.0.0.0", "--port", "8000"]