"""
Performance Optimization System for FinanceGPT Pro
Provides caching, request batching, and performance monitoring
"""

import time
import asyncio
import logging
from typing import Dict, Any, List, Optional, Callable, Union
from functools import wraps, lru_cache
from datetime import datetime, timedelta
from collections import defaultdict
import json
import hashlib
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

logger = logging.getLogger(__name__)

class PerformanceCache:
    """Advanced caching system with TTL and intelligent invalidation"""
    
    def __init__(self, default_ttl: int = 300):  # 5 minutes default
        self.cache = {}
        self.ttl_cache = {}
        self.access_count = defaultdict(int)
        self.default_ttl = default_ttl
        self.lock = threading.RLock()
    
    def _generate_key(self, func_name: str, args: tuple, kwargs: dict) -> str:
        """Generate a unique cache key"""
        key_data = {
            'func': func_name,
            'args': args,
            'kwargs': sorted(kwargs.items())
        }
        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache if not expired"""
        with self.lock:
            if key not in self.cache:
                return None
            
            # Check if expired
            if key in self.ttl_cache:
                if datetime.now() > self.ttl_cache[key]:
                    del self.cache[key]
                    del self.ttl_cache[key]
                    return None
            
            self.access_count[key] += 1
            return self.cache[key]
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with TTL"""
        with self.lock:
            self.cache[key] = value
            if ttl is None:
                ttl = self.default_ttl
            self.ttl_cache[key] = datetime.now() + timedelta(seconds=ttl)
    
    def invalidate_pattern(self, pattern: str) -> int:
        """Invalidate all keys matching pattern"""
        with self.lock:
            keys_to_remove = [k for k in self.cache.keys() if pattern in k]
            for key in keys_to_remove:
                del self.cache[key]
                if key in self.ttl_cache:
                    del self.ttl_cache[key]
            return len(keys_to_remove)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        with self.lock:
            return {
                "total_keys": len(self.cache),
                "total_accesses": sum(self.access_count.values()),
                "most_accessed": sorted(self.access_count.items(), key=lambda x: x[1], reverse=True)[:5]
            }

class RequestBatcher:
    """Batch similar requests to reduce API calls"""
    
    def __init__(self, batch_size: int = 5, batch_timeout: float = 1.0):
        self.batch_size = batch_size
        self.batch_timeout = batch_timeout
        self.pending_requests = defaultdict(list)
        self.batch_timers = {}
        self.lock = threading.RLock()
    
    async def add_request(self, request_type: str, request_data: Dict[str, Any], 
                         batch_processor: Callable) -> Any:
        """Add request to batch"""
        with self.lock:
            request_id = f"{request_type}_{len(self.pending_requests[request_type])}"
            future = asyncio.Future()
            
            self.pending_requests[request_type].append({
                'id': request_id,
                'data': request_data,
                'future': future
            })
            
            # Start timer if this is the first request
            if len(self.pending_requests[request_type]) == 1:
                self._start_batch_timer(request_type, batch_processor)
            
            # Process immediately if batch is full
            if len(self.pending_requests[request_type]) >= self.batch_size:
                await self._process_batch(request_type, batch_processor)
            
            return await future
    
    def _start_batch_timer(self, request_type: str, batch_processor: Callable):
        """Start timer for batch processing"""
        async def timer_callback():
            await asyncio.sleep(self.batch_timeout)
            if self.pending_requests[request_type]:
                await self._process_batch(request_type, batch_processor)
        
        self.batch_timers[request_type] = asyncio.create_task(timer_callback())
    
    async def _process_batch(self, request_type: str, batch_processor: Callable):
        """Process a batch of requests"""
        with self.lock:
            if not self.pending_requests[request_type]:
                return
            
            batch = self.pending_requests[request_type].copy()
            self.pending_requests[request_type].clear()
            
            # Cancel timer
            if request_type in self.batch_timers:
                self.batch_timers[request_type].cancel()
                del self.batch_timers[request_type]
        
        try:
            # Process batch
            results = await batch_processor([req['data'] for req in batch])
            
            # Distribute results
            for i, request in enumerate(batch):
                if i < len(results):
                    request['future'].set_result(results[i])
                else:
                    request['future'].set_exception(Exception("Batch processing failed"))
        
        except Exception as e:
            # Set exception for all requests in batch
            for request in batch:
                request['future'].set_exception(e)

class PerformanceMonitor:
    """Monitor and track performance metrics"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self.function_stats = defaultdict(lambda: {
            'call_count': 0,
            'total_time': 0.0,
            'avg_time': 0.0,
            'min_time': float('inf'),
            'max_time': 0.0,
            'error_count': 0
        })
        self.lock = threading.RLock()
    
    def record_call(self, func_name: str, duration: float, success: bool = True):
        """Record function call metrics"""
        with self.lock:
            stats = self.function_stats[func_name]
            stats['call_count'] += 1
            stats['total_time'] += duration
            stats['avg_time'] = stats['total_time'] / stats['call_count']
            stats['min_time'] = min(stats['min_time'], duration)
            stats['max_time'] = max(stats['max_time'], duration)
            
            if not success:
                stats['error_count'] += 1
            
            # Keep recent metrics for trending
            self.metrics[func_name].append({
                'timestamp': datetime.now(),
                'duration': duration,
                'success': success
            })
            
            # Keep only last 100 calls
            if len(self.metrics[func_name]) > 100:
                self.metrics[func_name] = self.metrics[func_name][-100:]
    
    def get_performance_report(self) -> Dict[str, Any]:
        """Get comprehensive performance report"""
        with self.lock:
            report = {
                'summary': {
                    'total_functions': len(self.function_stats),
                    'total_calls': sum(stats['call_count'] for stats in self.function_stats.values()),
                    'total_errors': sum(stats['error_count'] for stats in self.function_stats.values())
                },
                'function_stats': dict(self.function_stats),
                'slowest_functions': sorted(
                    [(name, stats['avg_time']) for name, stats in self.function_stats.items()],
                    key=lambda x: x[1], reverse=True
                )[:10],
                'most_called_functions': sorted(
                    [(name, stats['call_count']) for name, stats in self.function_stats.items()],
                    key=lambda x: x[1], reverse=True
                )[:10]
            }
            return report

# Global instances
performance_cache = PerformanceCache()
request_batcher = RequestBatcher()
performance_monitor = PerformanceMonitor()

def cached(ttl: int = 300, cache_key_func: Optional[Callable] = None):
    """Decorator for caching function results"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Generate cache key
            if cache_key_func:
                cache_key = cache_key_func(*args, **kwargs)
            else:
                cache_key = performance_cache._generate_key(func.__name__, args, kwargs)
            
            # Try to get from cache
            cached_result = performance_cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Cache hit for {func.__name__}")
                return cached_result
            
            # Execute function and cache result
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                performance_cache.set(cache_key, result, ttl)
                performance_monitor.record_call(func.__name__, time.time() - start_time, True)
                logger.debug(f"Cache miss for {func.__name__}, result cached")
                return result
            except Exception as e:
                performance_monitor.record_call(func.__name__, time.time() - start_time, False)
                raise
        
        return wrapper
    return decorator

def timed(func: Callable) -> Callable:
    """Decorator to time function execution"""
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            performance_monitor.record_call(func.__name__, time.time() - start_time, True)
            return result
        except Exception as e:
            performance_monitor.record_call(func.__name__, time.time() - start_time, False)
            raise
    
    return wrapper

async def batch_api_calls(api_calls: List[Callable], max_concurrent: int = 5) -> List[Any]:
    """Execute API calls concurrently with rate limiting"""
    semaphore = asyncio.Semaphore(max_concurrent)
    
    async def limited_call(call):
        async with semaphore:
            return await call()
    
    tasks = [limited_call(call) for call in api_calls]
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    return results

def optimize_data_structure(data: Any) -> Any:
    """Optimize data structures for better performance"""
    if isinstance(data, dict):
        # Remove None values and empty structures
        return {k: optimize_data_structure(v) for k, v in data.items() 
                if v is not None and v != {} and v != []}
    elif isinstance(data, list):
        return [optimize_data_structure(item) for item in data if item is not None]
    else:
        return data

class PerformanceProfiler:
    """Context manager for profiling code blocks"""
    
    def __init__(self, name: str):
        self.name = name
        self.start_time = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        duration = time.time() - self.start_time
        success = exc_type is None
        performance_monitor.record_call(self.name, duration, success)
        
        if duration > 1.0:  # Log slow operations
            logger.warning(f"Slow operation detected: {self.name} took {duration:.2f}s")

def get_performance_summary() -> Dict[str, Any]:
    """Get comprehensive performance summary"""
    return {
        'cache_stats': performance_cache.get_stats(),
        'performance_report': performance_monitor.get_performance_report(),
        'timestamp': datetime.now().isoformat()
    }
