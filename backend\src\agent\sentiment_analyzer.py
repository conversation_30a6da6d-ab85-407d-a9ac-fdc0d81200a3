"""
Advanced Sentiment Analysis for Stock Market
Analyzes sentiment from news, social media, and financial reports
"""

import requests
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
import re
from functools import lru_cache
from ..agent.configuration import get_config

logger = logging.getLogger(__name__)

class SentimentAnalyzer:
    """Advanced sentiment analysis for stocks"""
    
    def __init__(self, ticker: str):
        self.ticker = ticker.upper()
        self.config = get_config()
    
    @lru_cache(maxsize=50)
    def get_news_sentiment(self, days_back: int = 7) -> Dict:
        """Get sentiment from financial news"""
        try:
            # Use NewsAPI or similar service
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # Search for news about the ticker
            query = f"{self.ticker} stock OR {self.ticker} earnings OR {self.ticker} financial"
            
            # Mock implementation - replace with actual NewsAPI
            news_data = self._fetch_news_data(query, start_date, end_date)
            
            if not news_data:
                return {"sentiment_score": 0.0, "confidence": 0.0, "article_count": 0}
            
            # Analyze sentiment using Gemini
            sentiment_analysis = self._analyze_news_with_ai(news_data)
            
            return sentiment_analysis
            
        except Exception as e:
            logger.error(f"Error getting news sentiment for {self.ticker}: {str(e)}")
            return {"error": str(e), "sentiment_score": 0.0}
    
    def _fetch_news_data(self, query: str, start_date: datetime, end_date: datetime) -> List[Dict]:
        """Fetch news data from various sources"""
        try:
            # Try Google Search API first
            if self.config.api_keys.google_search_key:
                return self._fetch_from_google_search(query)
            
            # Fallback to mock data for demonstration
            return self._get_mock_news_data()
            
        except Exception as e:
            logger.error(f"Error fetching news data: {str(e)}")
            return []
    
    def _fetch_from_google_search(self, query: str) -> List[Dict]:
        """Fetch news using Google Custom Search API"""
        try:
            api_key = self.config.api_keys.google_search_key
            cse_id = self.config.api_keys.google_search_cse_id
            
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': api_key,
                'cx': cse_id,
                'q': query,
                'num': 10,
                'dateRestrict': 'd7',  # Last 7 days
                'sort': 'date'
            }
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            data = response.json()
            articles = []
            
            for item in data.get('items', []):
                articles.append({
                    'title': item.get('title', ''),
                    'snippet': item.get('snippet', ''),
                    'link': item.get('link', ''),
                    'date': item.get('pagemap', {}).get('metatags', [{}])[0].get('article:published_time', '')
                })
            
            return articles
            
        except Exception as e:
            logger.error(f"Error with Google Search API: {str(e)}")
            return []
    
    def _get_mock_news_data(self) -> List[Dict]:
        """Mock news data for demonstration"""
        return [
            {
                'title': f'{self.ticker} Reports Strong Quarterly Earnings',
                'snippet': f'{self.ticker} exceeded analyst expectations with strong revenue growth and positive outlook.',
                'sentiment': 'positive'
            },
            {
                'title': f'{self.ticker} Faces Market Headwinds',
                'snippet': f'Analysts express concerns about {self.ticker} amid market volatility and sector challenges.',
                'sentiment': 'negative'
            }
        ]
    
    def _analyze_news_with_ai(self, news_data: List[Dict]) -> Dict:
        """Analyze news sentiment using Gemini AI"""
        try:
            if not news_data:
                return {"sentiment_score": 0.0, "confidence": 0.0, "article_count": 0}
            
            # Prepare news text for analysis
            news_text = ""
            for article in news_data[:10]:  # Limit to 10 articles
                title = article.get('title', '')
                snippet = article.get('snippet', '')
                news_text += f"Title: {title}\nSummary: {snippet}\n\n"
            
            # Create prompt for Gemini
            prompt = f"""
            Analyze the sentiment of the following news articles about {self.ticker} stock.
            
            News Articles:
            {news_text}
            
            Please provide:
            1. Overall sentiment score from -1.0 (very negative) to +1.0 (very positive)
            2. Confidence level from 0.0 to 1.0
            3. Brief explanation of the sentiment
            4. Key positive and negative factors mentioned
            
            Respond in this exact JSON format:
            {{
                "sentiment_score": 0.0,
                "confidence": 0.0,
                "explanation": "Brief explanation",
                "positive_factors": ["factor1", "factor2"],
                "negative_factors": ["factor1", "factor2"],
                "article_count": {len(news_data)}
            }}
            """
            
            # Get response from Gemini
            response = self.config.gemini_model.invoke(prompt)
            
            # Parse JSON response
            import json
            try:
                result = json.loads(response.content)
                result['article_count'] = len(news_data)
                return result
            except json.JSONDecodeError:
                # Fallback parsing
                return self._parse_sentiment_response(response.content, len(news_data))
                
        except Exception as e:
            logger.error(f"Error analyzing news with AI: {str(e)}")
            return {
                "sentiment_score": 0.0,
                "confidence": 0.0,
                "explanation": f"Error in analysis: {str(e)}",
                "article_count": len(news_data)
            }
    
    def _parse_sentiment_response(self, response_text: str, article_count: int) -> Dict:
        """Parse sentiment response if JSON parsing fails"""
        try:
            # Extract sentiment score using regex
            score_match = re.search(r'sentiment[_\s]*score["\s]*:?\s*(-?\d*\.?\d+)', response_text, re.IGNORECASE)
            confidence_match = re.search(r'confidence["\s]*:?\s*(\d*\.?\d+)', response_text, re.IGNORECASE)
            
            sentiment_score = float(score_match.group(1)) if score_match else 0.0
            confidence = float(confidence_match.group(1)) if confidence_match else 0.5
            
            # Ensure values are in valid ranges
            sentiment_score = max(-1.0, min(1.0, sentiment_score))
            confidence = max(0.0, min(1.0, confidence))
            
            return {
                "sentiment_score": round(sentiment_score, 2),
                "confidence": round(confidence, 2),
                "explanation": "Parsed from AI response",
                "article_count": article_count
            }
            
        except Exception as e:
            logger.error(f"Error parsing sentiment response: {str(e)}")
            return {
                "sentiment_score": 0.0,
                "confidence": 0.0,
                "explanation": "Unable to parse sentiment",
                "article_count": article_count
            }
    
    def get_social_media_sentiment(self) -> Dict:
        """Get sentiment from social media (Twitter/X, Reddit)"""
        try:
            # Mock implementation - in production, integrate with Twitter API v2 or Reddit API
            social_sentiment = {
                "platform": "social_media",
                "sentiment_score": 0.3,  # Mock positive sentiment
                "confidence": 0.7,
                "post_count": 150,
                "explanation": f"Social media sentiment for {self.ticker} is moderately positive based on recent posts",
                "trending_topics": ["earnings", "growth", "innovation"],
                "risk_factors": ["market volatility", "competition"]
            }
            
            return social_sentiment
            
        except Exception as e:
            logger.error(f"Error getting social media sentiment: {str(e)}")
            return {"error": str(e), "sentiment_score": 0.0}
    
    def get_comprehensive_sentiment(self) -> Dict:
        """Get comprehensive sentiment analysis from all sources"""
        try:
            news_sentiment = self.get_news_sentiment()
            social_sentiment = self.get_social_media_sentiment()
            
            # Combine sentiments with weights
            news_weight = 0.6
            social_weight = 0.4
            
            news_score = news_sentiment.get('sentiment_score', 0.0)
            social_score = social_sentiment.get('sentiment_score', 0.0)
            
            combined_score = (news_score * news_weight) + (social_score * social_weight)
            
            # Determine overall sentiment category
            if combined_score > 0.3:
                category = "Positive"
            elif combined_score < -0.3:
                category = "Negative"
            else:
                category = "Neutral"
            
            return {
                "ticker": self.ticker,
                "overall_sentiment_score": round(combined_score, 2),
                "sentiment_category": category,
                "news_sentiment": news_sentiment,
                "social_sentiment": social_sentiment,
                "analysis_timestamp": datetime.now().isoformat(),
                "interpretation": self._interpret_sentiment(combined_score),
                "risk_assessment": self._assess_sentiment_risk(combined_score, news_sentiment, social_sentiment)
            }
            
        except Exception as e:
            logger.error(f"Error in comprehensive sentiment analysis: {str(e)}")
            return {"error": str(e), "sentiment_score": 0.0}
    
    def _interpret_sentiment(self, score: float) -> str:
        """Provide user-friendly interpretation of sentiment score"""
        if score > 0.6:
            return "Very positive market sentiment - high optimism and bullish outlook"
        elif score > 0.3:
            return "Positive market sentiment - generally optimistic with some caution"
        elif score > -0.3:
            return "Neutral market sentiment - mixed opinions and balanced outlook"
        elif score > -0.6:
            return "Negative market sentiment - concerns and bearish outlook prevail"
        else:
            return "Very negative market sentiment - significant pessimism and risk aversion"
    
    def _assess_sentiment_risk(self, overall_score: float, news_data: Dict, social_data: Dict) -> Dict:
        """Assess risk based on sentiment analysis"""
        risk_level = "Low"
        risk_factors = []
        
        # Check for sentiment volatility
        news_score = news_data.get('sentiment_score', 0.0)
        social_score = social_data.get('sentiment_score', 0.0)
        
        sentiment_divergence = abs(news_score - social_score)
        
        if sentiment_divergence > 0.5:
            risk_level = "High"
            risk_factors.append("High divergence between news and social sentiment")
        
        if overall_score < -0.5:
            risk_level = "High"
            risk_factors.append("Very negative overall sentiment")
        
        if news_data.get('confidence', 0) < 0.3:
            risk_level = "Medium" if risk_level == "Low" else risk_level
            risk_factors.append("Low confidence in sentiment analysis")
        
        return {
            "risk_level": risk_level,
            "risk_factors": risk_factors,
            "sentiment_volatility": round(sentiment_divergence, 2)
        }
