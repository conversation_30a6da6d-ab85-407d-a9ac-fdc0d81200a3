# FinanceGPT Pro - Production Environment Configuration

# Application Settings
ENVIRONMENT=production
APP_NAME=FinanceGPT Pro
APP_VERSION=2.0.0
DEBUG=false

# Database Configuration
POSTGRES_DB=financegpt
POSTGRES_USER=financegpt
POSTGRES_PASSWORD=secure_password_change_me_in_production
DATABASE_URL=*****************************************************************************/financegpt

# Redis Configuration
REDIS_PASSWORD=redis_password_change_me_in_production
REDIS_URL=redis://:redis_password_change_me_in_production@redis:6379/0

# API Keys (Set these in your production environment)
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_SEARCH_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_CSE_ID=your_google_search_cse_id_here
STOCKS_API_KEY=your_alpha_vantage_api_key_here

# Security
JWT_SECRET=your_super_secure_jwt_secret_change_me_in_production
CORS_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Frontend Configuration
REACT_APP_API_URL=https://api.yourdomain.com
REACT_APP_ENVIRONMENT=production

# Monitoring (Optional)
GRAFANA_PASSWORD=admin_change_me_in_production

# SSL/TLS (For production with HTTPS)
SSL_CERT_PATH=/etc/nginx/ssl/cert.pem
SSL_KEY_PATH=/etc/nginx/ssl/key.pem

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Performance
WORKER_PROCESSES=4
MAX_CONNECTIONS=1000
KEEPALIVE_TIMEOUT=65

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Cache Settings
CACHE_TTL=300
CACHE_MAX_SIZE=1000
