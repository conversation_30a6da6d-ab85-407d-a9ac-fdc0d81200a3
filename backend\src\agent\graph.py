from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver
from typing import TypedDict, List, Dict, Optional
import re
from .tools_and_schemas import (
    google_search, get_stock_data, analyze_sentiment, get_technical_analysis,
    get_fundamental_analysis, get_investment_recommendation
)

# Enhanced state for stock analysis
class GraphState(TypedDict, total=False):
    messages: List[Dict[str, str]]
    query: Optional[str]
    stock_ticker: Optional[str]
    analysis_type: Optional[str]  # "price", "sentiment", "technical", "comprehensive"
    stock_data: Optional[Dict]
    sentiment_data: Optional[str]
    technical_data: Optional[str]
    search_results: Optional[Dict]
    final_analysis: Optional[str]

def extract_ticker_from_query(query: str) -> Optional[str]:
    """Extract stock ticker from user query using improved regex patterns and company mapping"""
    if not query:
        return None

    query_upper = query.upper()

    # Enhanced company name to ticker mapping (more comprehensive)
    company_tickers = {
        "APPLE": "AAPL", "APPLE INC": "AAPL", "APPLE INCORPORATED": "AAPL",
        "MICROSOFT": "MSFT", "MICROSOFT CORP": "MSFT", "MICROSOFT CORPORATION": "MSFT",
        "GOOGLE": "GOOGL", "ALPHABET": "GOOGL", "ALPHABET INC": "GOOGL",
        "AMAZON": "AMZN", "AMAZON.COM": "AMZN", "AMAZON COM": "AMZN",
        "TESLA": "TSLA", "TESLA INC": "TSLA", "TESLA MOTORS": "TSLA",
        "META": "META", "FACEBOOK": "META", "META PLATFORMS": "META",
        "NVIDIA": "NVDA", "NVIDIA CORP": "NVDA", "NVIDIA CORPORATION": "NVDA",
        "NETFLIX": "NFLX", "NETFLIX INC": "NFLX",
        "SPOTIFY": "SPOT", "SPOTIFY TECHNOLOGY": "SPOT",
        "INTEL": "INTC", "INTEL CORP": "INTC", "INTEL CORPORATION": "INTC",
        "AMD": "AMD", "ADVANCED MICRO DEVICES": "AMD",
        "WALMART": "WMT", "WAL-MART": "WMT", "WALMART INC": "WMT",
        "DISNEY": "DIS", "WALT DISNEY": "DIS", "THE WALT DISNEY COMPANY": "DIS",
        "COCA-COLA": "KO", "COCA COLA": "KO", "COKE": "KO",
        "PEPSI": "PEP", "PEPSICO": "PEP", "PEPSI CO": "PEP",
        "MCDONALD'S": "MCD", "MCDONALDS": "MCD", "MCDONALD": "MCD",
        "STARBUCKS": "SBUX", "STARBUCKS CORP": "SBUX",
        "BOEING": "BA", "BOEING CO": "BA", "BOEING COMPANY": "BA",
        "GENERAL ELECTRIC": "GE", "GE": "GE",
        "IBM": "IBM", "INTERNATIONAL BUSINESS MACHINES": "IBM",
        "ORACLE": "ORCL", "ORACLE CORP": "ORCL", "ORACLE CORPORATION": "ORCL",
        "SALESFORCE": "CRM", "SALESFORCE.COM": "CRM", "SALESFORCE COM": "CRM"
    }

    # First, check for company names (prioritize longer matches)
    sorted_companies = sorted(company_tickers.keys(), key=len, reverse=True)
    for company in sorted_companies:
        if company in query_upper:
            return company_tickers[company]

    # Improved ticker patterns - more specific and accurate
    patterns = [
        r'\$([A-Z]{1,5})\b',  # $AAPL (dollar sign prefix - highest priority)
        r'(?:ticker|symbol)[\s:]+([A-Z]{1,5})\b',  # ticker: AAPL or symbol AAPL
        r'\b([A-Z]{2,5})(?:\s+(?:stock|shares?|equity|corp|inc|corporation|company))\b',  # AAPL stock
        r'(?:analyze|check|look\s+at|tell\s+me\s+about)\s+([A-Z]{2,5})\b',  # analyze AAPL
        r'(?:buy|sell|invest\s+in|purchase)\s+([A-Z]{2,5})\b',  # buy AAPL
        r'(?:price\s+of|value\s+of)\s+([A-Z]{2,5})\b',  # price of AAPL
        r'\b([A-Z]{2,5})\s+(?:price|value|analysis|data|info|information)\b',  # AAPL price
        r'(?:how\s+is|what\s+about)\s+([A-Z]{2,5})\b',  # how is AAPL
        r'\b([A-Z]{2,5})(?:\s+doing|\s+performing)\b',  # AAPL doing
    ]

    # Known common tickers for validation
    known_tickers = {
        "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "SPOT",
        "INTC", "AMD", "WMT", "DIS", "KO", "PEP", "MCD", "SBUX", "BA", "GE", "IBM",
        "ORCL", "CRM", "V", "MA", "JPM", "BAC", "WFC", "C", "GS", "MS", "BRK.A", "BRK.B",
        "JNJ", "PFE", "UNH", "CVS", "ABBV", "MRK", "LLY", "TMO", "DHR", "ABT",
        "XOM", "CVX", "COP", "SLB", "EOG", "PXD", "KMI", "OKE", "WMB", "EPD"
    }

    for pattern in patterns:
        matches = re.findall(pattern, query_upper)
        for match in matches:
            ticker = match.strip()
            # Validate ticker: 2-5 chars, known ticker, or passes basic validation
            if (2 <= len(ticker) <= 5 and
                (ticker in known_tickers or _is_valid_ticker_format(ticker))):
                return ticker

    return None

def _is_valid_ticker_format(ticker: str) -> bool:
    """Validate if a string looks like a valid ticker symbol"""
    if not ticker or len(ticker) < 2 or len(ticker) > 5:
        return False

    # Must be all uppercase letters
    if not ticker.isalpha():
        return False

    # Avoid common English words that might be mistaken for tickers
    common_words = {
        "THE", "AND", "FOR", "ARE", "BUT", "NOT", "YOU", "ALL", "CAN", "HER", "WAS",
        "ONE", "OUR", "OUT", "DAY", "GET", "HAS", "HIM", "HIS", "HOW", "ITS", "MAY",
        "NEW", "NOW", "OLD", "SEE", "TWO", "WHO", "BOY", "DID", "ITS", "LET", "PUT",
        "SAY", "SHE", "TOO", "USE", "WHAT", "WHEN", "WHERE", "WHY", "WILL", "WITH",
        "ABOUT", "AFTER", "AGAIN", "BEFORE", "BEING", "BELOW", "BETWEEN", "BOTH",
        "DURING", "EACH", "FURTHER", "HERE", "MYSELF", "OTHER", "SHOULD", "SUCH",
        "THAN", "THAT", "THEIR", "THEM", "THESE", "THEY", "THIS", "THOSE", "THROUGH",
        "UNTIL", "VERY", "WERE", "WHAT", "WHEN", "WHERE", "WHICH", "WHILE", "WITH",
        "WOULD", "YOUR", "YOURS", "YOURSELF", "YOURSELVES"
    }

    return ticker not in common_words

def determine_analysis_type(query: str) -> str:
    """Determine what type of analysis the user wants using improved pattern matching"""
    if not query:
        return "comprehensive"

    query_lower = query.lower()

    # Use scoring system for better accuracy
    scores = {
        "price": 0,
        "sentiment": 0,
        "technical": 0,
        "fundamental": 0,
        "recommendation": 0,
        "comprehensive": 0
    }

    # Price-related patterns (higher weight for specific phrases)
    price_patterns = [
        (r'\b(?:what\'?s|what is|show me|tell me|get|find)\s+(?:the\s+)?(?:current\s+)?price', 3),
        (r'\bhow much\b.*\b(?:cost|worth|trading|valued)', 3),
        (r'\b(?:price|cost|value|worth)\b', 2),
        (r'\btrading at\b', 2),
        (r'\bcurrent price\b', 3),
        (r'\bstock price\b', 2),
        (r'\bmarket value\b', 2)
    ]

    # Sentiment analysis patterns
    sentiment_patterns = [
        (r'\b(?:sentiment|feeling|mood|opinion|buzz)\b', 3),
        (r'\b(?:bullish|bearish|optimistic|pessimistic)\b', 3),
        (r'\b(?:news|media|social)\b.*\b(?:sentiment|opinion)\b', 2),
        (r'\bhow (?:do people feel|does the market feel)\b', 3),
        (r'\bmarket sentiment\b', 3),
        (r'\bpublic opinion\b', 2)
    ]

    # Technical analysis patterns
    technical_patterns = [
        (r'\b(?:technical|chart|trend|pattern)\b', 2),
        (r'\b(?:rsi|macd|bollinger|moving average|sma|ema)\b', 3),
        (r'\b(?:overbought|oversold|support|resistance)\b', 3),
        (r'\b(?:indicators|oscillators|momentum)\b', 2),
        (r'\bis.*overbought\b', 3),
        (r'\bis.*oversold\b', 3),
        (r'\btechnical analysis\b', 3)
    ]

    # Fundamental analysis patterns
    fundamental_patterns = [
        (r'\b(?:fundamental|financial health|financials)\b', 2),
        (r'\b(?:pe ratio|p/e|price.to.earnings|earnings)\b', 3),
        (r'\b(?:revenue|profit|debt|balance sheet|income statement)\b', 2),
        (r'\b(?:cash flow|dividend|yield|payout)\b', 2),
        (r'\bhow healthy\b.*\bfinancially\b', 3),
        (r'\bfundamental analysis\b', 3),
        (r'\bfinancial strength\b', 2),
        (r'\bcompany performance\b', 2)
    ]

    # Investment recommendation patterns
    recommendation_patterns = [
        (r'\bshould i (?:buy|sell|invest|purchase)\b', 3),
        (r'\b(?:recommend|recommendation|advice)\b', 2),
        (r'\b(?:good investment|worth buying|worth investing)\b', 3),
        (r'\bbuy or sell\b', 3),
        (r'\binvestment advice\b', 3),
        (r'\bis it a good\b.*\b(?:buy|investment)\b', 3),
        (r'\bwhat do you think\b.*\b(?:buy|invest)\b', 2)
    ]

    # Comprehensive analysis patterns
    comprehensive_patterns = [
        (r'\b(?:analyze|analysis|research|study)\b.*\b(?:completely|fully|everything)\b', 3),
        (r'\b(?:comprehensive|complete|full|detailed)\b.*\b(?:analysis|report|review)\b', 3),
        (r'\beverything about\b', 3),
        (r'\bfull report\b', 2),
        (r'\bcomplete analysis\b', 3),
        (r'\btell me everything\b', 2),
        (r'\banalyze.*(?:stock|company)\b', 2)
    ]

    # Score each category
    pattern_groups = [
        (price_patterns, "price"),
        (sentiment_patterns, "sentiment"),
        (technical_patterns, "technical"),
        (fundamental_patterns, "fundamental"),
        (recommendation_patterns, "recommendation"),
        (comprehensive_patterns, "comprehensive")
    ]

    for patterns, category in pattern_groups:
        for pattern, weight in patterns:
            if re.search(pattern, query_lower):
                scores[category] += weight

    # Find the category with the highest score
    max_score = max(scores.values())
    if max_score == 0:
        return "comprehensive"  # Default for ambiguous queries

    # Return the category with the highest score
    for category, score in scores.items():
        if score == max_score:
            return category

    return "comprehensive"

# Node functions
def parse_query_node(state: GraphState) -> GraphState:
    """Parse the user query to extract ticker and analysis type"""
    messages = state.get("messages", [])
    if not messages:
        return state

    last_message = messages[-1].get("content", "")
    ticker = extract_ticker_from_query(last_message)
    analysis_type = determine_analysis_type(last_message)

    return {
        **state,
        "query": last_message,
        "stock_ticker": ticker,
        "analysis_type": analysis_type
    }

def get_stock_price_node(state: GraphState) -> GraphState:
    """Get current stock price and basic data"""
    ticker = state.get("stock_ticker")
    if not ticker:
        return {**state, "stock_data": {"error": "No ticker found"}}

    try:
        stock_data = get_stock_data(ticker)
        return {**state, "stock_data": stock_data}
    except Exception as e:
        return {**state, "stock_data": {"error": str(e)}}

def get_sentiment_node(state: GraphState) -> GraphState:
    """Get sentiment analysis for the stock"""
    ticker = state.get("stock_ticker")
    if not ticker:
        return {**state, "sentiment_data": "No ticker available for sentiment analysis"}

    try:
        sentiment = analyze_sentiment(ticker)
        return {**state, "sentiment_data": sentiment}
    except Exception as e:
        return {**state, "sentiment_data": f"Sentiment analysis failed: {str(e)}"}

def get_technical_analysis_node(state: GraphState) -> GraphState:
    """Get technical analysis for the stock"""
    ticker = state.get("stock_ticker")
    if not ticker:
        return {**state, "technical_data": "No ticker available for technical analysis"}

    try:
        technical = get_technical_analysis(ticker)
        return {**state, "technical_data": technical}
    except Exception as e:
        return {**state, "technical_data": f"Technical analysis failed: {str(e)}"}

def search_additional_info_node(state: GraphState) -> GraphState:
    """Search for additional information about the stock"""
    ticker = state.get("stock_ticker")
    if not ticker:
        return {**state, "search_results": {"error": "No ticker for search"}}

    try:
        search_query = f"{ticker} stock analysis financial performance"
        results = google_search(search_query)
        return {**state, "search_results": results}
    except Exception as e:
        return {**state, "search_results": {"error": str(e)}}

def synthesize_analysis_node(state: GraphState) -> GraphState:
    """Synthesize all collected data into a comprehensive analysis"""
    ticker = state.get("stock_ticker", "Unknown")
    analysis_type = state.get("analysis_type", "comprehensive")
    stock_data = state.get("stock_data", {})
    sentiment_data = state.get("sentiment_data", "")
    technical_data = state.get("technical_data", "")
    fundamental_data = state.get("fundamental_data", "")
    recommendation_data = state.get("recommendation_data", "")
    search_results = state.get("search_results", {})

    # Build analysis based on type
    if analysis_type == "price":
        analysis = f"## 💰 Stock Price Analysis for {ticker}\n\n"
        if stock_data and "error" not in stock_data:
            current_price = stock_data.get('current_price')
            volume = stock_data.get('volume')
            market_cap = stock_data.get('market_cap')
            pe_ratio = stock_data.get('pe_ratio')

            analysis += f"**Current Price**: ${current_price if current_price else 'N/A'}\n"
            analysis += f"**Volume**: {volume:,} if volume else 'N/A'\n"
            analysis += f"**Market Cap**: ${market_cap:,} if market_cap else 'N/A'\n"
            analysis += f"**P/E Ratio**: {pe_ratio if pe_ratio else 'N/A'}\n"
            analysis += f"**52-Week Range**: ${stock_data.get('52_week_low', 'N/A')} - ${stock_data.get('52_week_high', 'N/A')}\n\n"
        else:
            analysis += f"Price data unavailable: {stock_data.get('error', 'Unknown error')}\n\n"

    elif analysis_type == "sentiment":
        analysis = f"## 📊 Sentiment Analysis for {ticker}\n\n"
        analysis += sentiment_data + "\n\n"

    elif analysis_type == "technical":
        analysis = f"## 📈 Technical Analysis for {ticker}\n\n"
        analysis += technical_data + "\n\n"

    elif analysis_type == "fundamental":
        analysis = f"## 💼 Fundamental Analysis for {ticker}\n\n"
        analysis += fundamental_data + "\n\n"

    elif analysis_type == "recommendation":
        analysis = f"## 🎯 Investment Recommendation for {ticker}\n\n"
        analysis += recommendation_data + "\n\n"

    else:  # comprehensive
        analysis = f"# 🚀 Comprehensive Stock Analysis: {ticker}\n\n"

        # Price section
        analysis += "## 💰 Current Market Data\n"
        if stock_data and "error" not in stock_data:
            current_price = stock_data.get('current_price')
            volume = stock_data.get('volume')
            market_cap = stock_data.get('market_cap')

            analysis += f"- **Current Price**: ${current_price if current_price else 'N/A'}\n"
            analysis += f"- **Volume**: {volume:,} if volume else 'N/A'\n"
            analysis += f"- **Market Cap**: ${market_cap:,} if market_cap else 'N/A'\n"
            analysis += f"- **Company**: {stock_data.get('company_name', 'N/A')} ({stock_data.get('sector', 'N/A')})\n\n"
        else:
            analysis += f"Price data unavailable: {stock_data.get('error', 'Unknown error')}\n\n"

        # Technical analysis section
        if technical_data:
            analysis += "## 📈 Technical Analysis\n"
            analysis += technical_data + "\n\n"

        # Fundamental analysis section
        if fundamental_data:
            analysis += "## 💼 Fundamental Analysis\n"
            analysis += fundamental_data + "\n\n"

        # Sentiment section
        if sentiment_data:
            analysis += "## 📊 Market Sentiment\n"
            analysis += sentiment_data + "\n\n"

        # Investment recommendation
        if recommendation_data:
            analysis += "## 🎯 Investment Recommendation\n"
            analysis += recommendation_data + "\n\n"

        # Additional insights from search
        if search_results and "error" not in search_results:
            analysis += "## 🔍 Additional Market Insights\n"
            items = search_results.get("items", [])[:3]  # Top 3 results
            for item in items:
                title = item.get("title", "")
                snippet = item.get("snippet", "")
                link = item.get("link", "")
                analysis += f"- **{title}**: {snippet} [Read more]({link})\n"
            analysis += "\n"

        # Educational note
        analysis += "---\n"
        analysis += "## 📚 Understanding This Analysis\n"
        analysis += "- **Technical Analysis**: Based on price patterns and indicators\n"
        analysis += "- **Fundamental Analysis**: Based on company financials and valuation\n"
        analysis += "- **Sentiment Analysis**: Based on news and social media sentiment\n"
        analysis += "- **Investment Recommendation**: AI-generated based on multiple factors\n\n"

        # Disclaimer
        analysis += "**⚠️ Important Disclaimer**: This analysis is generated by AI for informational purposes only and should not be considered as personalized financial advice. Always conduct your own research and consult with a qualified financial advisor before making investment decisions. Past performance does not guarantee future results.\n"

    # Update messages with the analysis
    messages = state.get("messages", [])
    new_message = {"role": "assistant", "content": analysis}

    return {
        **state,
        "final_analysis": analysis,
        "messages": messages + [new_message]
    }

def route_analysis(state: GraphState) -> str:
    """Route to appropriate analysis based on type and available ticker"""
    ticker = state.get("stock_ticker")
    analysis_type = state.get("analysis_type", "comprehensive")

    if not ticker:
        return "no_ticker_response"

    # Route to specific analysis types
    if analysis_type == "price":
        return "price_only"
    elif analysis_type == "sentiment":
        return "sentiment_only"
    elif analysis_type == "technical":
        return "technical_only"
    elif analysis_type == "fundamental":
        return "fundamental_only"
    elif analysis_type == "recommendation":
        return "recommendation_only"
    else:
        return "comprehensive_analysis"

def price_only_node(state: GraphState) -> GraphState:
    """Get price data and go directly to synthesis"""
    state = get_stock_price_node(state)
    return synthesize_analysis_node(state)

def sentiment_only_node(state: GraphState) -> GraphState:
    """Get sentiment data and go directly to synthesis"""
    state = get_sentiment_node(state)
    return synthesize_analysis_node(state)

def technical_only_node(state: GraphState) -> GraphState:
    """Get technical data and go directly to synthesis"""
    state = get_technical_analysis_node(state)
    return synthesize_analysis_node(state)

def get_fundamental_analysis_node(state: GraphState) -> GraphState:
    """Get fundamental analysis for the stock"""
    ticker = state.get("stock_ticker")
    if not ticker:
        return {**state, "fundamental_data": "No ticker available for fundamental analysis"}

    try:
        fundamental = get_fundamental_analysis(ticker)
        return {**state, "fundamental_data": fundamental}
    except Exception as e:
        return {**state, "fundamental_data": f"Fundamental analysis failed: {str(e)}"}

def fundamental_only_node(state: GraphState) -> GraphState:
    """Get fundamental data and go directly to synthesis"""
    state = get_fundamental_analysis_node(state)
    return synthesize_analysis_node(state)

def get_investment_recommendation_node(state: GraphState) -> GraphState:
    """Get investment recommendation for the stock"""
    ticker = state.get("stock_ticker")
    if not ticker:
        return {**state, "recommendation_data": "No ticker available for investment recommendation"}

    try:
        recommendation = get_investment_recommendation(ticker)
        return {**state, "recommendation_data": recommendation}
    except Exception as e:
        return {**state, "recommendation_data": f"Investment recommendation failed: {str(e)}"}

def recommendation_only_node(state: GraphState) -> GraphState:
    """Get investment recommendation and go directly to synthesis"""
    state = get_investment_recommendation_node(state)
    return synthesize_analysis_node(state)

def no_ticker_response_node(state: GraphState) -> GraphState:
    """Handle cases where no ticker is found"""
    query = state.get("query", "")

    response = f"""I couldn't identify a specific stock ticker in your query: "{query}"

Please specify a stock ticker (e.g., AAPL, MSFT, GOOGL) or company name for analysis.

## 🎯 **What I Can Analyze For You:**

### 💰 **Price & Market Data**
- "What's AAPL's current price?"
- "Show me Tesla's market cap"

### 📈 **Technical Analysis**
- "Is NVDA overbought?" (RSI analysis)
- "What's Microsoft's MACD signal?"
- "Show me TSLA's moving averages"

### 📊 **Sentiment Analysis**
- "What's the sentiment for Apple?"
- "How do people feel about Tesla stock?"

### 💼 **Fundamental Analysis**
- "How healthy is Amazon financially?"
- "What's Google's P/E ratio?"
- "Analyze Meta's debt levels"

### 🎯 **Investment Recommendations**
- "Should I buy Tesla stock?"
- "Is Apple a good investment?"
- "Give me a recommendation for Microsoft"

### 🚀 **Comprehensive Analysis**
- "Analyze everything about AAPL"
- "Give me a full report on Tesla"
- "Research Amazon stock completely"

## 📚 **Educational Features**
I also explain financial concepts and provide disclaimers to help you make informed decisions!

**Try asking about any stock with its ticker symbol (like AAPL, TSLA, MSFT) or company name!**
"""

    messages = state.get("messages", [])
    new_message = {"role": "assistant", "content": response}

    return {
        **state,
        "messages": messages + [new_message]
    }

# Build the graph
builder = StateGraph(GraphState)

# Add all nodes
builder.add_node("parse_query", parse_query_node)
builder.add_node("no_ticker_response", no_ticker_response_node)
builder.add_node("get_price", get_stock_price_node)
builder.add_node("get_sentiment", get_sentiment_node)
builder.add_node("get_technical", get_technical_analysis_node)
builder.add_node("get_fundamental", get_fundamental_analysis_node)
builder.add_node("get_recommendation", get_investment_recommendation_node)
builder.add_node("search_info", search_additional_info_node)
builder.add_node("synthesize", synthesize_analysis_node)
builder.add_node("price_only", price_only_node)
builder.add_node("sentiment_only", sentiment_only_node)
builder.add_node("technical_only", technical_only_node)
builder.add_node("fundamental_only", fundamental_only_node)
builder.add_node("recommendation_only", recommendation_only_node)

# Set entry point
builder.set_entry_point("parse_query")

# Add conditional routing from parse_query
builder.add_conditional_edges(
    "parse_query",
    route_analysis,
    {
        "no_ticker_response": "no_ticker_response",
        "price_only": "price_only",
        "sentiment_only": "sentiment_only",
        "technical_only": "technical_only",
        "fundamental_only": "fundamental_only",
        "recommendation_only": "recommendation_only",
        "comprehensive_analysis": "get_price"  # Start comprehensive with price
    }
)

# For comprehensive analysis, chain the nodes
builder.add_edge("get_price", "get_fundamental")
builder.add_edge("get_fundamental", "get_sentiment")
builder.add_edge("get_sentiment", "get_technical")
builder.add_edge("get_technical", "get_recommendation")
builder.add_edge("get_recommendation", "search_info")
builder.add_edge("search_info", "synthesize")

# For single analysis types, they handle synthesis internally and go to END
builder.add_edge("price_only", END)
builder.add_edge("sentiment_only", END)
builder.add_edge("technical_only", END)
builder.add_edge("fundamental_only", END)
builder.add_edge("recommendation_only", END)

# All paths end at synthesize or no_ticker_response
builder.add_edge("synthesize", END)
builder.add_edge("no_ticker_response", END)

# Create memory saver for conversation history
memory = MemorySaver()

# Compile the graph
graph = builder.compile(checkpointer=memory)