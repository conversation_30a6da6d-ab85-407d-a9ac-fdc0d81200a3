"""
Advanced Technical Analysis Indicators
Provides RSI, MACD, SMA, EMA, Bollinger Bands, and other indicators
"""

# import numpy as np
# import pandas as pd
# Using built-in Python instead for compatibility
from typing import Dict, List, Optional, Tuple
import requests
import logging
from functools import lru_cache
from ..agent.configuration import get_config
from ..utils.performance_optimizer import cached, timed, PerformanceProfiler, performance_cache

logger = logging.getLogger(__name__)

class TechnicalAnalyzer:
    """Advanced technical analysis with multiple indicators"""
    
    def __init__(self, ticker: str):
        self.ticker = ticker.upper()
        self.data = None
        
    @cached(ttl=300)  # Cache for 5 minutes
    @timed
    def fetch_stock_data(self, period: str = "daily") -> Optional[Dict]:
        """Fetch stock data from Alpha Vantage - optimized version"""
        with PerformanceProfiler(f"fetch_stock_data_{self.ticker}_{period}"):
            try:
                config = get_config()
                api_key = config.api_keys.stocks_api_key

                if not api_key:
                    logger.warning("No Alpha Vantage API key configured - using mock data")
                    return self._get_mock_stock_data()

                # Check if we have cached data first
                cache_key = f"stock_data_{self.ticker}_{period}"
                cached_data = performance_cache.get(cache_key)
                if cached_data:
                    self.data = cached_data
                    return cached_data

                if period == "daily":
                    url = f"https://www.alphavantage.co/query?function=TIME_SERIES_DAILY&symbol={self.ticker}&outputsize=compact&apikey={api_key}"
                    time_key = "Time Series (Daily)"
                else:
                    url = f"https://www.alphavantage.co/query?function=TIME_SERIES_INTRADAY&symbol={self.ticker}&interval=5min&outputsize=compact&apikey={api_key}"
                    time_key = "Time Series (5min)"

                response = requests.get(url, timeout=15)
                response.raise_for_status()
                data = response.json()

                if "Error Message" in data:
                    logger.error(f"Alpha Vantage error: {data['Error Message']}")
                    return self._get_mock_stock_data()

                if "Note" in data:
                    logger.warning(f"Alpha Vantage rate limit: {data['Note']}")
                    return self._get_mock_stock_data()

                time_series = data.get(time_key, {})
                if not time_series:
                    logger.error(f"No time series data found for {self.ticker}")
                    return self._get_mock_stock_data()

                # Convert to simple list format with optimized processing
                stock_data = []
                for date_str, values in sorted(time_series.items()):
                    try:
                        stock_data.append({
                            'date': date_str,
                            'open': float(values['1. open']),
                            'high': float(values['2. high']),
                            'low': float(values['3. low']),
                            'close': float(values['4. close']),
                            'volume': float(values['5. volume'])
                        })
                    except (ValueError, KeyError):
                        continue

                # Cache the processed data
                performance_cache.set(cache_key, stock_data, ttl=300)
                self.data = stock_data
                return stock_data

            except Exception as e:
                logger.error(f"Error fetching data for {self.ticker}: {str(e)}")
                return self._get_mock_stock_data()

    def _get_mock_stock_data(self) -> List[Dict]:
        """Generate mock stock data for demonstration"""
        import random
        base_price = 150.0
        data = []

        for i in range(50):  # 50 days of data
            price = base_price + random.uniform(-10, 10)
            data.append({
                'date': f"2024-{6-i//30:02d}-{(30-i%30):02d}",
                'open': round(price + random.uniform(-2, 2), 2),
                'high': round(price + random.uniform(0, 5), 2),
                'low': round(price - random.uniform(0, 5), 2),
                'close': round(price, 2),
                'volume': random.randint(1000000, 10000000)
            })

        return data
    
    @cached(ttl=60)  # Cache for 1 minute
    def calculate_rsi(self, period: int = 14) -> Optional[float]:
        """Calculate Relative Strength Index - optimized version"""
        if not self.data or len(self.data) < period + 1:
            return None

        try:
            with PerformanceProfiler(f"calculate_rsi_{self.ticker}"):
                # Get close prices more efficiently
                close_prices = [item['close'] for item in self.data[-period-1:]]

                # Calculate price changes in one pass
                deltas = [close_prices[i] - close_prices[i-1] for i in range(1, len(close_prices))]

                # Separate gains and losses more efficiently
                gains = [max(0, delta) for delta in deltas]
                losses = [max(0, -delta) for delta in deltas]

                # Calculate average gain and loss
                avg_gain = sum(gains) / len(gains) if gains else 0
                avg_loss = sum(losses) / len(losses) if losses else 0

                if avg_loss == 0:
                    return 100.0  # No losses means RSI = 100

                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))

                return round(rsi, 2)
        except Exception as e:
            logger.error(f"Error calculating RSI: {str(e)}")
            return None
    
    def calculate_macd(self, fast: int = 12, slow: int = 26, signal: int = 9) -> Optional[Dict]:
        """Calculate MACD (Moving Average Convergence Divergence) - simplified version"""
        if not self.data or len(self.data) < slow + signal:
            return None

        try:
            close_prices = [item['close'] for item in self.data]

            # Simple EMA calculation
            def calculate_ema(prices, period):
                multiplier = 2 / (period + 1)
                ema = [prices[0]]  # Start with first price
                for price in prices[1:]:
                    ema.append((price * multiplier) + (ema[-1] * (1 - multiplier)))
                return ema

            ema_fast = calculate_ema(close_prices, fast)
            ema_slow = calculate_ema(close_prices, slow)

            # Calculate MACD line
            macd_line = [ema_fast[i] - ema_slow[i] for i in range(len(ema_slow))]

            # Calculate signal line (EMA of MACD)
            signal_line = calculate_ema(macd_line, signal)

            # Calculate histogram
            histogram = macd_line[-1] - signal_line[-1]

            return {
                'macd': round(macd_line[-1], 4),
                'signal': round(signal_line[-1], 4),
                'histogram': round(histogram, 4)
            }
        except Exception as e:
            logger.error(f"Error calculating MACD: {str(e)}")
            return None
    
    def calculate_moving_averages(self) -> Optional[Dict]:
        """Calculate Simple and Exponential Moving Averages - simplified version"""
        if not self.data:
            return None

        try:
            close_prices = [item['close'] for item in self.data]
            current_price = close_prices[-1]

            def calculate_sma(prices, period):
                if len(prices) < period:
                    return None
                return sum(prices[-period:]) / period

            def calculate_ema(prices, period):
                if len(prices) < period:
                    return None
                multiplier = 2 / (period + 1)
                ema = prices[0]
                for price in prices[1:]:
                    ema = (price * multiplier) + (ema * (1 - multiplier))
                return ema

            sma_20 = calculate_sma(close_prices, 20)
            sma_50 = calculate_sma(close_prices, 50)
            sma_200 = calculate_sma(close_prices, 200)

            ema_20 = calculate_ema(close_prices, 20)
            ema_50 = calculate_ema(close_prices, 50)

            return {
                'current_price': round(current_price, 2),
                'sma_20': round(sma_20, 2) if sma_20 is not None else None,
                'sma_50': round(sma_50, 2) if sma_50 is not None else None,
                'sma_200': round(sma_200, 2) if sma_200 is not None else None,
                'ema_20': round(ema_20, 2) if ema_20 is not None else None,
                'ema_50': round(ema_50, 2) if ema_50 is not None else None,
            }
        except Exception as e:
            logger.error(f"Error calculating moving averages: {str(e)}")
            return None
    
    def calculate_bollinger_bands(self, period: int = 20, std_dev: int = 2) -> Optional[Dict]:
        """Calculate Bollinger Bands - simplified version"""
        if not self.data or len(self.data) < period:
            return None

        try:
            close_prices = [item['close'] for item in self.data]
            recent_prices = close_prices[-period:]

            # Calculate SMA
            sma = sum(recent_prices) / len(recent_prices)

            # Calculate standard deviation
            variance = sum((price - sma) ** 2 for price in recent_prices) / len(recent_prices)
            std = variance ** 0.5

            upper_band = sma + (std * std_dev)
            lower_band = sma - (std * std_dev)

            current_price = close_prices[-1]

            return {
                'upper_band': round(upper_band, 2),
                'middle_band': round(sma, 2),
                'lower_band': round(lower_band, 2),
                'current_price': round(current_price, 2),
                'position': self._get_bb_position(current_price, upper_band, lower_band)
            }
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {str(e)}")
            return None
    
    def _get_bb_position(self, price: float, upper: float, lower: float) -> str:
        """Determine position relative to Bollinger Bands"""
        if price > upper:
            return "above_upper"
        elif price < lower:
            return "below_lower"
        else:
            return "within_bands"
    
    @cached(ttl=120)  # Cache for 2 minutes
    @timed
    def get_comprehensive_analysis(self) -> Dict:
        """Get comprehensive technical analysis - optimized version"""
        with PerformanceProfiler(f"comprehensive_analysis_{self.ticker}"):
            # Fetch data first
            data = self.fetch_stock_data()
            if not data:
                return {"error": "Unable to fetch stock data"}

            try:
                # Calculate all indicators in parallel-like fashion
                # (Note: In a real async environment, these could be truly parallel)
                analysis = {
                    'ticker': self.ticker,
                    'rsi': None,
                    'macd': None,
                    'moving_averages': None,
                    'bollinger_bands': None,
                    'interpretation': {}
                }

                # Calculate indicators with error handling for each
                try:
                    analysis['rsi'] = self.calculate_rsi()
                except Exception as e:
                    logger.warning(f"RSI calculation failed: {str(e)}")

                try:
                    analysis['macd'] = self.calculate_macd()
                except Exception as e:
                    logger.warning(f"MACD calculation failed: {str(e)}")

                try:
                    analysis['moving_averages'] = self.calculate_moving_averages()
                except Exception as e:
                    logger.warning(f"Moving averages calculation failed: {str(e)}")

                try:
                    analysis['bollinger_bands'] = self.calculate_bollinger_bands()
                except Exception as e:
                    logger.warning(f"Bollinger bands calculation failed: {str(e)}")

                # Add interpretations
                analysis['interpretation'] = self._interpret_indicators(analysis)

                return analysis

            except Exception as e:
                logger.error(f"Error in comprehensive analysis: {str(e)}")
                return {"error": str(e)}
    
    def _interpret_indicators(self, analysis: Dict) -> Dict:
        """Interpret technical indicators for user-friendly explanations"""
        interpretations = {}
        
        # RSI interpretation
        rsi = analysis.get('rsi')
        if rsi:
            if rsi > 70:
                interpretations['rsi'] = f"RSI of {rsi} suggests the stock may be overbought"
            elif rsi < 30:
                interpretations['rsi'] = f"RSI of {rsi} suggests the stock may be oversold"
            else:
                interpretations['rsi'] = f"RSI of {rsi} indicates neutral momentum"
        
        # MACD interpretation
        macd_data = analysis.get('macd')
        if macd_data:
            macd_line = macd_data.get('macd', 0)
            signal_line = macd_data.get('signal', 0)
            if macd_line > signal_line:
                interpretations['macd'] = "MACD shows bullish momentum"
            else:
                interpretations['macd'] = "MACD shows bearish momentum"
        
        # Moving averages interpretation
        ma_data = analysis.get('moving_averages')
        if ma_data:
            current = ma_data.get('current_price')
            sma_50 = ma_data.get('sma_50')
            sma_200 = ma_data.get('sma_200')
            
            if current and sma_50 and sma_200:
                if current > sma_50 > sma_200:
                    interpretations['trend'] = "Strong uptrend (price above both 50 and 200 SMA)"
                elif current < sma_50 < sma_200:
                    interpretations['trend'] = "Strong downtrend (price below both 50 and 200 SMA)"
                else:
                    interpretations['trend'] = "Mixed trend signals"
        
        # Bollinger Bands interpretation
        bb_data = analysis.get('bollinger_bands')
        if bb_data:
            position = bb_data.get('position')
            if position == "above_upper":
                interpretations['bollinger'] = "Price is above upper Bollinger Band, potentially overbought"
            elif position == "below_lower":
                interpretations['bollinger'] = "Price is below lower Bollinger Band, potentially oversold"
            else:
                interpretations['bollinger'] = "Price is within normal Bollinger Band range"
        
        return interpretations
