import React, { useState, useRef } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { 
  Download,
  Upload,
  FileText,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
  X,
  Eye
} from 'lucide-react';

interface PortfolioHolding {
  ticker: string;
  company_name: string;
  shares: number;
  average_cost: number;
  current_price: number;
  market_value: number;
  unrealized_gain_loss: number;
  unrealized_gain_loss_percent: number;
  sector: string;
}

interface Portfolio {
  portfolio_id: string;
  name: string;
  total_value: number;
  total_gain_loss: number;
  total_gain_loss_percent: number;
  holdings: PortfolioHolding[];
  cash_balance: number;
  created_date: string;
  last_updated: string;
}

interface ImportedHolding {
  ticker: string;
  shares: number;
  average_cost: number;
  valid: boolean;
  error?: string;
}

interface PortfolioExportImportProps {
  portfolio: Portfolio;
  onImportHoldings: (holdings: { ticker: string; shares: number; price: number }[]) => void;
  userTier: 'Basic' | 'Pro';
}

export const PortfolioExportImport: React.FC<PortfolioExportImportProps> = ({
  portfolio,
  onImportHoldings,
  userTier
}) => {
  const [showImport, setShowImport] = useState(false);
  const [importedData, setImportedData] = useState<ImportedHolding[]>([]);
  const [importStatus, setImportStatus] = useState<'idle' | 'processing' | 'success' | 'error'>('idle');
  const [importError, setImportError] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercent = (percent: number) => {
    return `${percent >= 0 ? '+' : ''}${percent.toFixed(2)}%`;
  };

  // Export to CSV
  const exportToCSV = () => {
    const headers = [
      'Ticker',
      'Company Name',
      'Shares',
      'Average Cost',
      'Current Price',
      'Market Value',
      'Unrealized Gain/Loss',
      'Gain/Loss %',
      'Sector'
    ];

    const csvData = [
      headers.join(','),
      ...portfolio.holdings.map(holding => [
        holding.ticker,
        `"${holding.company_name}"`,
        holding.shares,
        holding.average_cost.toFixed(2),
        holding.current_price.toFixed(2),
        holding.market_value.toFixed(2),
        holding.unrealized_gain_loss.toFixed(2),
        holding.unrealized_gain_loss_percent.toFixed(2),
        holding.sector
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${portfolio.name.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  // Export to PDF (simplified version)
  const exportToPDF = () => {
    // In a real implementation, you would use a library like jsPDF
    // For now, we'll create a printable HTML version
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;

    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>${portfolio.name} - Portfolio Report</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; margin-bottom: 30px; }
          .summary { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
          th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
          th { background-color: #f2f2f2; }
          .positive { color: green; }
          .negative { color: red; }
          .footer { margin-top: 30px; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>${portfolio.name}</h1>
          <p>Portfolio Report - ${new Date().toLocaleDateString()}</p>
        </div>
        
        <div class="summary">
          <h2>Portfolio Summary</h2>
          <p><strong>Total Value:</strong> ${formatCurrency(portfolio.total_value)}</p>
          <p><strong>Total Gain/Loss:</strong> <span class="${portfolio.total_gain_loss >= 0 ? 'positive' : 'negative'}">${formatCurrency(portfolio.total_gain_loss)} (${formatPercent(portfolio.total_gain_loss_percent)})</span></p>
          <p><strong>Cash Balance:</strong> ${formatCurrency(portfolio.cash_balance)}</p>
          <p><strong>Number of Holdings:</strong> ${portfolio.holdings.length}</p>
        </div>
        
        <h2>Holdings</h2>
        <table>
          <thead>
            <tr>
              <th>Ticker</th>
              <th>Company</th>
              <th>Shares</th>
              <th>Avg Cost</th>
              <th>Current Price</th>
              <th>Market Value</th>
              <th>Gain/Loss</th>
              <th>Gain/Loss %</th>
              <th>Sector</th>
            </tr>
          </thead>
          <tbody>
            ${portfolio.holdings.map(holding => `
              <tr>
                <td>${holding.ticker}</td>
                <td>${holding.company_name}</td>
                <td>${holding.shares.toLocaleString()}</td>
                <td>${formatCurrency(holding.average_cost)}</td>
                <td>${formatCurrency(holding.current_price)}</td>
                <td>${formatCurrency(holding.market_value)}</td>
                <td class="${holding.unrealized_gain_loss >= 0 ? 'positive' : 'negative'}">${formatCurrency(holding.unrealized_gain_loss)}</td>
                <td class="${holding.unrealized_gain_loss_percent >= 0 ? 'positive' : 'negative'}">${formatPercent(holding.unrealized_gain_loss_percent)}</td>
                <td>${holding.sector}</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        
        <div class="footer">
          <p>Generated by FinanceGPT Pro - ${new Date().toLocaleString()}</p>
          <p>This report is for informational purposes only and should not be considered as investment advice.</p>
        </div>
      </body>
      </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.print();
  };

  // Handle file import
  const handleFileImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setImportStatus('processing');
    setImportError('');

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const lines = content.split('\n').filter(line => line.trim());
        
        if (lines.length < 2) {
          throw new Error('File must contain at least a header row and one data row');
        }

        // Parse CSV (simple implementation)
        const headers = lines[0].split(',').map(h => h.trim().toLowerCase());
        const tickerIndex = headers.findIndex(h => h.includes('ticker') || h.includes('symbol'));
        const sharesIndex = headers.findIndex(h => h.includes('shares') || h.includes('quantity'));
        const priceIndex = headers.findIndex(h => h.includes('price') || h.includes('cost'));

        if (tickerIndex === -1 || sharesIndex === -1 || priceIndex === -1) {
          throw new Error('CSV must contain columns for ticker/symbol, shares/quantity, and price/cost');
        }

        const holdings: ImportedHolding[] = [];
        
        for (let i = 1; i < lines.length; i++) {
          const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
          
          if (values.length < Math.max(tickerIndex, sharesIndex, priceIndex) + 1) {
            continue; // Skip incomplete rows
          }

          const ticker = values[tickerIndex].toUpperCase();
          const shares = parseFloat(values[sharesIndex]);
          const price = parseFloat(values[priceIndex]);

          let valid = true;
          let error = '';

          if (!ticker || ticker.length > 10) {
            valid = false;
            error = 'Invalid ticker symbol';
          } else if (isNaN(shares) || shares <= 0) {
            valid = false;
            error = 'Invalid shares quantity';
          } else if (isNaN(price) || price <= 0) {
            valid = false;
            error = 'Invalid price';
          }

          holdings.push({
            ticker,
            shares,
            average_cost: price,
            valid,
            error
          });
        }

        setImportedData(holdings);
        setImportStatus('success');
      } catch (error) {
        setImportError(error instanceof Error ? error.message : 'Failed to parse file');
        setImportStatus('error');
      }
    };

    reader.readAsText(file);
  };

  // Confirm import
  const confirmImport = () => {
    const validHoldings = importedData.filter(h => h.valid);
    const holdingsToImport = validHoldings.map(h => ({
      ticker: h.ticker,
      shares: h.shares,
      price: h.average_cost
    }));

    onImportHoldings(holdingsToImport);
    setShowImport(false);
    setImportedData([]);
    setImportStatus('idle');
  };

  return (
    <div className="space-y-6">
      {/* Export/Import Controls */}
      <Card className="p-6">
        <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
          <FileText className="h-5 w-5 mr-2 text-blue-400" />
          Portfolio Export & Import
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Export Section */}
          <div>
            <h4 className="text-lg font-medium text-white mb-3">Export Portfolio</h4>
            <div className="space-y-3">
              <Button
                onClick={exportToCSV}
                className="w-full bg-green-600 hover:bg-green-700 text-white"
              >
                <FileSpreadsheet className="h-4 w-4 mr-2" />
                Export to CSV
              </Button>
              
              <Button
                onClick={exportToPDF}
                className="w-full bg-red-600 hover:bg-red-700 text-white"
                disabled={userTier === 'Basic'}
              >
                <FileText className="h-4 w-4 mr-2" />
                Export to PDF {userTier === 'Basic' && '(Pro Only)'}
              </Button>
            </div>
          </div>

          {/* Import Section */}
          <div>
            <h4 className="text-lg font-medium text-white mb-3">Import Holdings</h4>
            <div className="space-y-3">
              <Button
                onClick={() => setShowImport(true)}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Upload className="h-4 w-4 mr-2" />
                Import from CSV
              </Button>
              
              <p className="text-gray-400 text-sm">
                Upload a CSV file with columns: Ticker, Shares, Price
              </p>
            </div>
          </div>
        </div>
      </Card>

      {/* Import Modal */}
      {showImport && (
        <Card className="p-6 bg-white/5 border border-white/20">
          <div className="flex items-center justify-between mb-4">
            <h4 className="text-lg font-semibold text-white">Import Holdings from CSV</h4>
            <Button
              onClick={() => {
                setShowImport(false);
                setImportedData([]);
                setImportStatus('idle');
              }}
              variant="outline"
              size="sm"
              className="border-gray-500 text-gray-300 hover:bg-gray-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>

          {importStatus === 'idle' && (
            <div>
              <div className="border-2 border-dashed border-gray-500 rounded-lg p-8 text-center">
                <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-white mb-2">Upload CSV File</p>
                <p className="text-gray-400 text-sm mb-4">
                  File should contain columns: Ticker, Shares, Price/Cost
                </p>
                <input
                  ref={fileInputRef}
                  type="file"
                  accept=".csv"
                  onChange={handleFileImport}
                  className="hidden"
                />
                <Button
                  onClick={() => fileInputRef.current?.click()}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Choose File
                </Button>
              </div>
            </div>
          )}

          {importStatus === 'processing' && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className="text-white">Processing file...</p>
            </div>
          )}

          {importStatus === 'error' && (
            <div className="bg-red-900/20 border border-red-500/30 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <AlertCircle className="h-5 w-5 text-red-400" />
                <span className="text-red-400 font-medium">Import Error</span>
              </div>
              <p className="text-gray-300">{importError}</p>
            </div>
          )}

          {importStatus === 'success' && importedData.length > 0 && (
            <div>
              <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4 mb-4">
                <div className="flex items-center space-x-2">
                  <CheckCircle className="h-5 w-5 text-green-400" />
                  <span className="text-green-400 font-medium">
                    Found {importedData.length} holdings ({importedData.filter(h => h.valid).length} valid)
                  </span>
                </div>
              </div>

              <div className="max-h-64 overflow-y-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-white/20">
                      <th className="text-left py-2 text-gray-300">Status</th>
                      <th className="text-left py-2 text-gray-300">Ticker</th>
                      <th className="text-right py-2 text-gray-300">Shares</th>
                      <th className="text-right py-2 text-gray-300">Price</th>
                      <th className="text-left py-2 text-gray-300">Error</th>
                    </tr>
                  </thead>
                  <tbody>
                    {importedData.map((holding, index) => (
                      <tr key={index} className="border-b border-white/10">
                        <td className="py-2">
                          {holding.valid ? (
                            <CheckCircle className="h-4 w-4 text-green-400" />
                          ) : (
                            <AlertCircle className="h-4 w-4 text-red-400" />
                          )}
                        </td>
                        <td className="py-2 text-white">{holding.ticker}</td>
                        <td className="py-2 text-right text-white">{holding.shares.toLocaleString()}</td>
                        <td className="py-2 text-right text-white">${holding.average_cost.toFixed(2)}</td>
                        <td className="py-2 text-red-400 text-sm">{holding.error || ''}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              <div className="flex justify-end space-x-3 mt-4">
                <Button
                  onClick={() => {
                    setShowImport(false);
                    setImportedData([]);
                    setImportStatus('idle');
                  }}
                  variant="outline"
                  className="border-gray-500 text-gray-300 hover:bg-gray-700"
                >
                  Cancel
                </Button>
                <Button
                  onClick={confirmImport}
                  disabled={importedData.filter(h => h.valid).length === 0}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  Import {importedData.filter(h => h.valid).length} Holdings
                </Button>
              </div>
            </div>
          )}
        </Card>
      )}
    </div>
  );
};
