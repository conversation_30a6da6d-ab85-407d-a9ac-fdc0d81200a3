import React, { useState, useEffect } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Badge } from './ui/badge';
import { marketDataService } from '../services/marketDataService';
import { 
  Plus, 
  Trash2, 
  Edit3, 
  Save, 
  X, 
  TrendingUp, 
  TrendingDown,
  DollarSign,
  Percent,
  BarChart3,
  PieChart,
  RefreshCw,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface PortfolioHolding {
  id: string;
  ticker: string;
  shares: number;
  avgCost: number;
  currentPrice: number;
  totalValue: number;
  gainLoss: number;
  gainLossPercent: number;
  lastUpdated: string;
}

interface EditablePortfolioProps {
  userTier: 'Basic' | 'Pro';
}

export const EditablePortfolio: React.FC<EditablePortfolioProps> = ({ userTier }) => {
  const [holdings, setHoldings] = useState<PortfolioHolding[]>([]);
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [isAdding, setIsAdding] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [newHolding, setNewHolding] = useState({
    ticker: '',
    shares: '',
    avgCost: ''
  });
  const [editingHolding, setEditingHolding] = useState({
    shares: '',
    avgCost: ''
  });

  // Load portfolio from localStorage on mount
  useEffect(() => {
    const savedPortfolio = localStorage.getItem('userPortfolio');
    if (savedPortfolio) {
      try {
        const parsed = JSON.parse(savedPortfolio);
        setHoldings(parsed);
        refreshPrices(parsed);
      } catch (error) {
        console.error('Error loading portfolio:', error);
      }
    }
  }, []);

  // Save portfolio to localStorage whenever holdings change
  useEffect(() => {
    if (holdings.length > 0) {
      localStorage.setItem('userPortfolio', JSON.stringify(holdings));
    }
  }, [holdings]);

  const refreshPrices = async (holdingsToRefresh = holdings) => {
    if (holdingsToRefresh.length === 0) return;
    
    setIsRefreshing(true);
    try {
      const updatedHoldings = await Promise.all(
        holdingsToRefresh.map(async (holding) => {
          try {
            const stockData = await marketDataService.getStockData(holding.ticker);
            const totalValue = holding.shares * stockData.currentPrice;
            const totalCost = holding.shares * holding.avgCost;
            const gainLoss = totalValue - totalCost;
            const gainLossPercent = (gainLoss / totalCost) * 100;

            return {
              ...holding,
              currentPrice: stockData.currentPrice,
              totalValue,
              gainLoss,
              gainLossPercent,
              lastUpdated: new Date().toISOString()
            };
          } catch (error) {
            console.error(`Error updating ${holding.ticker}:`, error);
            return holding; // Return unchanged if update fails
          }
        })
      );
      
      setHoldings(updatedHoldings);
    } catch (error) {
      console.error('Error refreshing prices:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const addHolding = async () => {
    if (!newHolding.ticker || !newHolding.shares || !newHolding.avgCost) {
      return;
    }

    try {
      const stockData = await marketDataService.getStockData(newHolding.ticker.toUpperCase());
      const shares = parseFloat(newHolding.shares);
      const avgCost = parseFloat(newHolding.avgCost);
      const totalValue = shares * stockData.currentPrice;
      const totalCost = shares * avgCost;
      const gainLoss = totalValue - totalCost;
      const gainLossPercent = (gainLoss / totalCost) * 100;

      const holding: PortfolioHolding = {
        id: Date.now().toString(),
        ticker: newHolding.ticker.toUpperCase(),
        shares,
        avgCost,
        currentPrice: stockData.currentPrice,
        totalValue,
        gainLoss,
        gainLossPercent,
        lastUpdated: new Date().toISOString()
      };

      setHoldings(prev => [...prev, holding]);
      setNewHolding({ ticker: '', shares: '', avgCost: '' });
      setIsAdding(false);
    } catch (error) {
      console.error('Error adding holding:', error);
    }
  };

  const updateHolding = async (id: string) => {
    const holding = holdings.find(h => h.id === id);
    if (!holding || !editingHolding.shares || !editingHolding.avgCost) return;

    try {
      const shares = parseFloat(editingHolding.shares);
      const avgCost = parseFloat(editingHolding.avgCost);
      const totalValue = shares * holding.currentPrice;
      const totalCost = shares * avgCost;
      const gainLoss = totalValue - totalCost;
      const gainLossPercent = (gainLoss / totalCost) * 100;

      setHoldings(prev => prev.map(h => 
        h.id === id 
          ? { 
              ...h, 
              shares, 
              avgCost, 
              totalValue, 
              gainLoss, 
              gainLossPercent,
              lastUpdated: new Date().toISOString()
            }
          : h
      ));
      
      setIsEditing(null);
      setEditingHolding({ shares: '', avgCost: '' });
    } catch (error) {
      console.error('Error updating holding:', error);
    }
  };

  const removeHolding = (id: string) => {
    setHoldings(prev => prev.filter(h => h.id !== id));
  };

  const startEditing = (holding: PortfolioHolding) => {
    setIsEditing(holding.id);
    setEditingHolding({
      shares: holding.shares.toString(),
      avgCost: holding.avgCost.toString()
    });
  };

  const cancelEditing = () => {
    setIsEditing(null);
    setEditingHolding({ shares: '', avgCost: '' });
  };

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(value);
  };

  const formatPercent = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`;
  };

  const totalPortfolioValue = holdings.reduce((sum, holding) => sum + holding.totalValue, 0);
  const totalGainLoss = holdings.reduce((sum, holding) => sum + holding.gainLoss, 0);
  const totalGainLossPercent = totalPortfolioValue > 0 ? (totalGainLoss / (totalPortfolioValue - totalGainLoss)) * 100 : 0;

  // Simple pie chart data
  const chartData = holdings.map(holding => ({
    name: holding.ticker,
    value: holding.totalValue,
    percentage: (holding.totalValue / totalPortfolioValue) * 100
  }));

  return (
    <div className="space-y-6">
      {/* Portfolio Summary */}
      <Card className="p-6 bg-gradient-to-br from-gray-900/95 to-gray-800/95 backdrop-blur-sm border-gray-700/50">
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-white flex items-center">
            <BarChart3 className="h-6 w-6 mr-2 text-green-400" />
            My Portfolio
          </h2>
          <div className="flex items-center space-x-2">
            <Button
              onClick={() => refreshPrices()}
              disabled={isRefreshing || holdings.length === 0}
              variant="outline"
              size="sm"
              className="text-gray-300"
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button
              onClick={() => setIsAdding(true)}
              className="bg-green-600 hover:bg-green-700 text-white"
              size="sm"
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Stock
            </Button>
          </div>
        </div>

        {/* Portfolio Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Total Value</span>
              <DollarSign className="h-4 w-4 text-green-400" />
            </div>
            <div className="text-2xl font-bold text-white">{formatCurrency(totalPortfolioValue)}</div>
          </div>

          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Total Gain/Loss</span>
              {totalGainLoss >= 0 ? 
                <TrendingUp className="h-4 w-4 text-green-400" /> : 
                <TrendingDown className="h-4 w-4 text-red-400" />
              }
            </div>
            <div className={`text-2xl font-bold ${totalGainLoss >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatCurrency(totalGainLoss)}
            </div>
            <div className={`text-sm ${totalGainLoss >= 0 ? 'text-green-400' : 'text-red-400'}`}>
              {formatPercent(totalGainLossPercent)}
            </div>
          </div>

          <div className="bg-white/5 rounded-lg p-4">
            <div className="flex items-center justify-between mb-2">
              <span className="text-gray-400 text-sm">Holdings</span>
              <PieChart className="h-4 w-4 text-blue-400" />
            </div>
            <div className="text-2xl font-bold text-white">{holdings.length}</div>
            <div className="text-sm text-gray-400">Positions</div>
          </div>
        </div>

        {/* Simple Allocation Chart */}
        {holdings.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-semibold text-white mb-3">Portfolio Allocation</h3>
            <div className="space-y-2">
              {chartData.map((item, index) => (
                <div key={item.name} className="flex items-center space-x-3">
                  <div 
                    className="w-4 h-4 rounded-full"
                    style={{ backgroundColor: `hsl(${(index * 137.5) % 360}, 70%, 50%)` }}
                  />
                  <div className="flex-1 flex items-center justify-between">
                    <span className="text-white font-medium">{item.name}</span>
                    <div className="text-right">
                      <div className="text-white">{formatCurrency(item.value)}</div>
                      <div className="text-gray-400 text-sm">{item.percentage.toFixed(1)}%</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </Card>

      {/* Add New Holding Form */}
      {isAdding && (
        <Card className="p-6 bg-blue-900/20 border-blue-500/30">
          <h3 className="text-lg font-semibold text-white mb-4">Add New Holding</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Ticker Symbol</label>
              <Input
                value={newHolding.ticker}
                onChange={(e) => setNewHolding(prev => ({ ...prev, ticker: e.target.value.toUpperCase() }))}
                placeholder="AAPL"
                className="bg-white/10 border-gray-600 text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Shares</label>
              <Input
                type="number"
                value={newHolding.shares}
                onChange={(e) => setNewHolding(prev => ({ ...prev, shares: e.target.value }))}
                placeholder="100"
                className="bg-white/10 border-gray-600 text-white"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">Average Cost</label>
              <Input
                type="number"
                step="0.01"
                value={newHolding.avgCost}
                onChange={(e) => setNewHolding(prev => ({ ...prev, avgCost: e.target.value }))}
                placeholder="150.00"
                className="bg-white/10 border-gray-600 text-white"
              />
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button onClick={addHolding} className="bg-green-600 hover:bg-green-700 text-white">
              <Save className="h-4 w-4 mr-1" />
              Add Holding
            </Button>
            <Button onClick={() => setIsAdding(false)} variant="outline" className="text-gray-300">
              <X className="h-4 w-4 mr-1" />
              Cancel
            </Button>
          </div>
        </Card>
      )}

      {/* Holdings List */}
      {holdings.length > 0 ? (
        <Card className="p-6">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-700">
                  <th className="text-left py-3 px-2 text-gray-300 font-medium">Symbol</th>
                  <th className="text-right py-3 px-2 text-gray-300 font-medium">Shares</th>
                  <th className="text-right py-3 px-2 text-gray-300 font-medium">Avg Cost</th>
                  <th className="text-right py-3 px-2 text-gray-300 font-medium">Current</th>
                  <th className="text-right py-3 px-2 text-gray-300 font-medium">Value</th>
                  <th className="text-right py-3 px-2 text-gray-300 font-medium">Gain/Loss</th>
                  <th className="text-center py-3 px-2 text-gray-300 font-medium">Actions</th>
                </tr>
              </thead>
              <tbody>
                {holdings.map((holding) => (
                  <tr key={holding.id} className="border-b border-gray-800 hover:bg-white/5">
                    <td className="py-3 px-2">
                      <div className="font-medium text-white">{holding.ticker}</div>
                    </td>
                    <td className="py-3 px-2 text-right text-white">
                      {isEditing === holding.id ? (
                        <Input
                          type="number"
                          value={editingHolding.shares}
                          onChange={(e) => setEditingHolding(prev => ({ ...prev, shares: e.target.value }))}
                          className="w-20 bg-white/10 border-gray-600 text-white text-right"
                        />
                      ) : (
                        holding.shares.toLocaleString()
                      )}
                    </td>
                    <td className="py-3 px-2 text-right text-white">
                      {isEditing === holding.id ? (
                        <Input
                          type="number"
                          step="0.01"
                          value={editingHolding.avgCost}
                          onChange={(e) => setEditingHolding(prev => ({ ...prev, avgCost: e.target.value }))}
                          className="w-24 bg-white/10 border-gray-600 text-white text-right"
                        />
                      ) : (
                        formatCurrency(holding.avgCost)
                      )}
                    </td>
                    <td className="py-3 px-2 text-right text-white">
                      {formatCurrency(holding.currentPrice)}
                    </td>
                    <td className="py-3 px-2 text-right text-white font-medium">
                      {formatCurrency(holding.totalValue)}
                    </td>
                    <td className="py-3 px-2 text-right">
                      <div className={`font-medium ${holding.gainLoss >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {formatCurrency(holding.gainLoss)}
                      </div>
                      <div className={`text-sm ${holding.gainLoss >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                        {formatPercent(holding.gainLossPercent)}
                      </div>
                    </td>
                    <td className="py-3 px-2 text-center">
                      <div className="flex items-center justify-center space-x-1">
                        {isEditing === holding.id ? (
                          <>
                            <Button
                              onClick={() => updateHolding(holding.id)}
                              size="sm"
                              className="bg-green-600 hover:bg-green-700 text-white p-1"
                            >
                              <CheckCircle className="h-3 w-3" />
                            </Button>
                            <Button
                              onClick={cancelEditing}
                              size="sm"
                              variant="outline"
                              className="text-gray-300 p-1"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </>
                        ) : (
                          <>
                            <Button
                              onClick={() => startEditing(holding)}
                              size="sm"
                              variant="outline"
                              className="text-gray-300 p-1"
                            >
                              <Edit3 className="h-3 w-3" />
                            </Button>
                            <Button
                              onClick={() => removeHolding(holding.id)}
                              size="sm"
                              variant="outline"
                              className="text-red-400 hover:text-red-300 p-1"
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </Card>
      ) : (
        <Card className="p-8 text-center">
          <div className="text-gray-400 mb-4">
            <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
            <h3 className="text-lg font-medium">No Holdings Yet</h3>
            <p className="text-sm">Add your first stock to start tracking your portfolio</p>
          </div>
          <Button
            onClick={() => setIsAdding(true)}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Your First Stock
          </Button>
        </Card>
      )}
    </div>
  );
};
