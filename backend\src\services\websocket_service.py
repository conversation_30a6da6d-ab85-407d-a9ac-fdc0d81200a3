"""
Real-Time WebSocket Service for FinanceGPT Pro
Handles live market data streaming, price updates, and real-time notifications
"""

import asyncio
import json
import logging
from typing import Dict, List, Set, Optional, Any
from datetime import datetime, timedelta
from fastapi import WebSocket, WebSocketDisconnect
from dataclasses import dataclass, asdict
import uuid
from enum import Enum

logger = logging.getLogger(__name__)

class MessageType(Enum):
    """WebSocket message types"""
    PRICE_UPDATE = "price_update"
    MARKET_STATUS = "market_status"
    NEWS_ALERT = "news_alert"
    ANALYSIS_COMPLETE = "analysis_complete"
    PORTFOLIO_UPDATE = "portfolio_update"
    SYSTEM_NOTIFICATION = "system_notification"
    HEARTBEAT = "heartbeat"
    SUBSCRIBE = "subscribe"
    UNSUBSCRIBE = "unsubscribe"

@dataclass
class WebSocketMessage:
    """Standard WebSocket message format"""
    type: str
    data: Dict[str, Any]
    timestamp: str
    message_id: str = None
    
    def __post_init__(self):
        if not self.message_id:
            self.message_id = str(uuid.uuid4())
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()

@dataclass
class PriceUpdate:
    """Real-time price update data"""
    ticker: str
    price: float
    change: float
    change_percent: float
    volume: int
    timestamp: str
    market_status: str = "OPEN"

@dataclass
class MarketStatus:
    """Market status information"""
    status: str  # OPEN, CLOSED, PRE_MARKET, AFTER_HOURS
    next_open: Optional[str]
    next_close: Optional[str]
    timezone: str = "US/Eastern"

class ConnectionManager:
    """Manages WebSocket connections and subscriptions"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_subscriptions: Dict[str, Set[str]] = {}  # user_id -> set of tickers
        self.ticker_subscribers: Dict[str, Set[str]] = {}  # ticker -> set of user_ids
        self.connection_metadata: Dict[str, Dict[str, Any]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str, client_info: Dict[str, Any] = None):
        """Accept new WebSocket connection"""
        await websocket.accept()
        self.active_connections[user_id] = websocket
        self.user_subscriptions[user_id] = set()
        self.connection_metadata[user_id] = {
            "connected_at": datetime.now().isoformat(),
            "client_info": client_info or {},
            "last_heartbeat": datetime.now().isoformat()
        }
        
        logger.info(f"WebSocket connected: user_id={user_id}")
        
        # Send welcome message
        welcome_msg = WebSocketMessage(
            type=MessageType.SYSTEM_NOTIFICATION.value,
            data={
                "message": "Connected to FinanceGPT Pro real-time data feed",
                "user_id": user_id,
                "features": ["live_prices", "market_status", "analysis_updates"]
            },
            timestamp=datetime.now().isoformat()
        )
        await self.send_personal_message(welcome_msg, user_id)
    
    def disconnect(self, user_id: str):
        """Handle WebSocket disconnection"""
        if user_id in self.active_connections:
            # Remove from ticker subscriptions
            for ticker in self.user_subscriptions.get(user_id, set()):
                if ticker in self.ticker_subscribers:
                    self.ticker_subscribers[ticker].discard(user_id)
                    if not self.ticker_subscribers[ticker]:
                        del self.ticker_subscribers[ticker]
            
            # Clean up user data
            del self.active_connections[user_id]
            del self.user_subscriptions[user_id]
            del self.connection_metadata[user_id]
            
            logger.info(f"WebSocket disconnected: user_id={user_id}")
    
    async def send_personal_message(self, message: WebSocketMessage, user_id: str):
        """Send message to specific user"""
        if user_id in self.active_connections:
            try:
                await self.active_connections[user_id].send_text(json.dumps(asdict(message)))
            except Exception as e:
                logger.error(f"Error sending message to {user_id}: {str(e)}")
                self.disconnect(user_id)
    
    async def broadcast_to_subscribers(self, message: WebSocketMessage, ticker: str):
        """Broadcast message to all subscribers of a ticker"""
        if ticker in self.ticker_subscribers:
            disconnected_users = []
            for user_id in self.ticker_subscribers[ticker]:
                try:
                    await self.send_personal_message(message, user_id)
                except Exception as e:
                    logger.error(f"Error broadcasting to {user_id}: {str(e)}")
                    disconnected_users.append(user_id)
            
            # Clean up disconnected users
            for user_id in disconnected_users:
                self.disconnect(user_id)
    
    async def broadcast_to_all(self, message: WebSocketMessage):
        """Broadcast message to all connected users"""
        disconnected_users = []
        for user_id in list(self.active_connections.keys()):
            try:
                await self.send_personal_message(message, user_id)
            except Exception as e:
                logger.error(f"Error broadcasting to {user_id}: {str(e)}")
                disconnected_users.append(user_id)
        
        # Clean up disconnected users
        for user_id in disconnected_users:
            self.disconnect(user_id)
    
    def subscribe_to_ticker(self, user_id: str, ticker: str):
        """Subscribe user to ticker updates"""
        if user_id in self.user_subscriptions:
            self.user_subscriptions[user_id].add(ticker)
            
            if ticker not in self.ticker_subscribers:
                self.ticker_subscribers[ticker] = set()
            self.ticker_subscribers[ticker].add(user_id)
            
            logger.info(f"User {user_id} subscribed to {ticker}")
            return True
        return False
    
    def unsubscribe_from_ticker(self, user_id: str, ticker: str):
        """Unsubscribe user from ticker updates"""
        if user_id in self.user_subscriptions:
            self.user_subscriptions[user_id].discard(ticker)
            
            if ticker in self.ticker_subscribers:
                self.ticker_subscribers[ticker].discard(user_id)
                if not self.ticker_subscribers[ticker]:
                    del self.ticker_subscribers[ticker]
            
            logger.info(f"User {user_id} unsubscribed from {ticker}")
            return True
        return False
    
    def get_user_subscriptions(self, user_id: str) -> Set[str]:
        """Get user's current subscriptions"""
        return self.user_subscriptions.get(user_id, set())
    
    def get_ticker_subscriber_count(self, ticker: str) -> int:
        """Get number of subscribers for a ticker"""
        return len(self.ticker_subscribers.get(ticker, set()))
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics"""
        return {
            "total_connections": len(self.active_connections),
            "total_subscriptions": sum(len(subs) for subs in self.user_subscriptions.values()),
            "active_tickers": len(self.ticker_subscribers),
            "most_popular_tickers": sorted(
                [(ticker, len(users)) for ticker, users in self.ticker_subscribers.items()],
                key=lambda x: x[1],
                reverse=True
            )[:10]
        }

class WebSocketService:
    """Main WebSocket service for real-time data"""
    
    def __init__(self):
        self.manager = ConnectionManager()
        self.market_data_cache: Dict[str, PriceUpdate] = {}
        self.market_status = MarketStatus(
            status="CLOSED",
            next_open=None,
            next_close=None
        )
        self.is_running = False
    
    async def handle_websocket(self, websocket: WebSocket, user_id: str):
        """Handle WebSocket connection lifecycle"""
        await self.manager.connect(websocket, user_id)
        
        try:
            while True:
                # Receive message from client
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                await self.handle_client_message(user_id, message_data)
                
        except WebSocketDisconnect:
            self.manager.disconnect(user_id)
        except Exception as e:
            logger.error(f"WebSocket error for user {user_id}: {str(e)}")
            self.manager.disconnect(user_id)
    
    async def handle_client_message(self, user_id: str, message_data: Dict[str, Any]):
        """Handle incoming client messages"""
        message_type = message_data.get("type")
        data = message_data.get("data", {})
        
        if message_type == MessageType.SUBSCRIBE.value:
            ticker = data.get("ticker", "").upper()
            if ticker:
                success = self.manager.subscribe_to_ticker(user_id, ticker)
                response = WebSocketMessage(
                    type=MessageType.SYSTEM_NOTIFICATION.value,
                    data={
                        "message": f"{'Subscribed to' if success else 'Failed to subscribe to'} {ticker}",
                        "ticker": ticker,
                        "subscribed": success
                    },
                    timestamp=datetime.now().isoformat()
                )
                await self.manager.send_personal_message(response, user_id)
                
                # Send current price if available
                if ticker in self.market_data_cache:
                    price_msg = WebSocketMessage(
                        type=MessageType.PRICE_UPDATE.value,
                        data=asdict(self.market_data_cache[ticker]),
                        timestamp=datetime.now().isoformat()
                    )
                    await self.manager.send_personal_message(price_msg, user_id)
        
        elif message_type == MessageType.UNSUBSCRIBE.value:
            ticker = data.get("ticker", "").upper()
            if ticker:
                success = self.manager.unsubscribe_from_ticker(user_id, ticker)
                response = WebSocketMessage(
                    type=MessageType.SYSTEM_NOTIFICATION.value,
                    data={
                        "message": f"{'Unsubscribed from' if success else 'Failed to unsubscribe from'} {ticker}",
                        "ticker": ticker,
                        "unsubscribed": success
                    },
                    timestamp=datetime.now().isoformat()
                )
                await self.manager.send_personal_message(response, user_id)
        
        elif message_type == MessageType.HEARTBEAT.value:
            # Update last heartbeat
            if user_id in self.manager.connection_metadata:
                self.manager.connection_metadata[user_id]["last_heartbeat"] = datetime.now().isoformat()
            
            # Send heartbeat response
            response = WebSocketMessage(
                type=MessageType.HEARTBEAT.value,
                data={"status": "alive", "server_time": datetime.now().isoformat()},
                timestamp=datetime.now().isoformat()
            )
            await self.manager.send_personal_message(response, user_id)
    
    async def broadcast_price_update(self, price_update: PriceUpdate):
        """Broadcast price update to subscribers"""
        self.market_data_cache[price_update.ticker] = price_update
        
        message = WebSocketMessage(
            type=MessageType.PRICE_UPDATE.value,
            data=asdict(price_update),
            timestamp=datetime.now().isoformat()
        )
        
        await self.manager.broadcast_to_subscribers(message, price_update.ticker)
    
    async def broadcast_market_status(self, market_status: MarketStatus):
        """Broadcast market status to all users"""
        self.market_status = market_status
        
        message = WebSocketMessage(
            type=MessageType.MARKET_STATUS.value,
            data=asdict(market_status),
            timestamp=datetime.now().isoformat()
        )
        
        await self.manager.broadcast_to_all(message)
    
    async def send_analysis_complete(self, user_id: str, analysis_data: Dict[str, Any]):
        """Send analysis completion notification"""
        message = WebSocketMessage(
            type=MessageType.ANALYSIS_COMPLETE.value,
            data=analysis_data,
            timestamp=datetime.now().isoformat()
        )
        
        await self.manager.send_personal_message(message, user_id)
    
    def get_stats(self) -> Dict[str, Any]:
        """Get service statistics"""
        return {
            "websocket_stats": self.manager.get_connection_stats(),
            "market_status": asdict(self.market_status),
            "cached_tickers": list(self.market_data_cache.keys()),
            "service_uptime": datetime.now().isoformat()
        }

# Global WebSocket service instance
websocket_service = WebSocketService()
