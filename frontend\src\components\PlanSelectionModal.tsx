import React from 'react';
import { Card } from './ui/card';
import { <PERSON><PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { X, Check, Star, Zap } from 'lucide-react';

interface PlanSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectBasic: () => void;
  onSelectTrialPro: () => void;
  onSelectPro: () => void;
  currentTier: string;
}

export const PlanSelectionModal: React.FC<PlanSelectionModalProps> = ({
  isOpen,
  onClose,
  onSelectBasic,
  onSelectTrialPro,
  onSelectPro,
  currentTier
}) => {
  if (!isOpen) return null;

  const plans = [
    {
      id: 'basic',
      name: 'FinanceGPT Basic',
      price: 'Free',
      description: 'Perfect for getting started',
      features: [
        '500 tokens/month',
        '25 daily queries',
        'Basic technical analysis',
        'Price alerts',
        'Community support'
      ],
      buttonText: currentTier === 'Basic' ? 'Current Plan' : 'Switch to Basic',
      buttonClass: 'bg-gray-600 hover:bg-gray-700',
      icon: <Check className="h-6 w-6" />,
      badge: currentTier === 'Basic' ? 'CURRENT' : null,
      badgeClass: 'bg-gray-500'
    },
    {
      id: 'trial',
      name: 'FinanceGPT Pro Trial',
      price: 'Free Trial',
      description: '7-day full access trial',
      features: [
        '10,000 tokens (7 days)',
        'Unlimited daily queries',
        'Advanced analytics',
        'Image analysis',
        'Real-time data',
        'Priority support',
        'All Pro features'
      ],
      buttonText: 'Start Free Trial',
      buttonClass: 'bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700',
      icon: <Zap className="h-6 w-6" />,
      badge: 'TRIAL',
      badgeClass: 'bg-gradient-to-r from-orange-500 to-red-500'
    },
    {
      id: 'pro',
      name: 'FinanceGPT Pro',
      price: '$29/month',
      description: 'Full professional access',
      features: [
        '10,000 tokens/month',
        'Unlimited daily queries',
        'Advanced analytics',
        'Image analysis',
        'Real-time data',
        'Priority support',
        'API access',
        'Custom alerts'
      ],
      buttonText: currentTier === 'Pro' ? 'Current Plan' : 'Upgrade to Pro',
      buttonClass: 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700',
      icon: <Star className="h-6 w-6" />,
      badge: currentTier === 'Pro' ? 'CURRENT' : 'POPULAR',
      badgeClass: currentTier === 'Pro' ? 'bg-green-500' : 'bg-gradient-to-r from-blue-500 to-purple-500'
    }
  ];

  const handlePlanSelect = (planId: string) => {
    switch (planId) {
      case 'basic':
        onSelectBasic();
        break;
      case 'trial':
        onSelectTrialPro();
        break;
      case 'pro':
        onSelectPro();
        break;
    }
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-gradient-to-br from-gray-900 to-gray-800 rounded-xl sm:rounded-2xl border border-white/20 max-w-6xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-white/20">
          <div className="min-w-0 flex-1 pr-4">
            <h2 className="text-xl sm:text-2xl font-bold text-white">Choose Your Plan</h2>
            <p className="text-gray-300 mt-1 text-sm sm:text-base">Select the perfect plan for your trading needs</p>
          </div>
          <Button
            onClick={onClose}
            className="bg-gray-700 hover:bg-gray-600 text-white p-2 shrink-0 touch-manipulation"
            size="sm"
          >
            <X className="h-4 w-4 sm:h-5 sm:w-5" />
          </Button>
        </div>

        {/* Plans Grid */}
        <div className="p-3 sm:p-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
            {plans.map((plan) => (
              <Card
                key={plan.id}
                className={`relative p-4 sm:p-6 bg-white/5 backdrop-blur-sm border transition-all duration-300 hover:scale-[1.02] sm:hover:scale-105 touch-manipulation ${
                  plan.id === 'pro'
                    ? 'border-blue-500/50 bg-gradient-to-br from-blue-600/10 to-purple-600/10'
                    : plan.id === 'trial'
                    ? 'border-orange-500/50 bg-gradient-to-br from-orange-600/10 to-red-600/10'
                    : 'border-white/20'
                }`}
              >
                {/* Badge */}
                {plan.badge && (
                  <div className="absolute -top-2 sm:-top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className={`${plan.badgeClass} text-white px-2 sm:px-3 py-1 text-xs font-bold`}>
                      {plan.badge}
                    </Badge>
                  </div>
                )}

                {/* Plan Header */}
                <div className="text-center mb-4 sm:mb-6">
                  <div className={`w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-3 sm:mb-4 rounded-full flex items-center justify-center ${
                    plan.id === 'pro'
                      ? 'bg-gradient-to-r from-blue-600 to-purple-600'
                      : plan.id === 'trial'
                      ? 'bg-gradient-to-r from-orange-600 to-red-600'
                      : 'bg-gray-600'
                  }`}>
                    <span className="text-white text-sm sm:text-base">{plan.icon}</span>
                  </div>
                  <h3 className="text-lg sm:text-xl font-bold text-white mb-2">{plan.name}</h3>
                  <div className="text-xl sm:text-2xl font-bold text-white mb-1">{plan.price}</div>
                  <p className="text-gray-300 text-xs sm:text-sm">{plan.description}</p>
                </div>

                {/* Features */}
                <div className="space-y-2 sm:space-y-3 mb-4 sm:mb-6 flex-1">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="flex items-center space-x-2 sm:space-x-3">
                      <Check className="h-3 w-3 sm:h-4 sm:w-4 text-green-400 flex-shrink-0" />
                      <span className="text-gray-300 text-xs sm:text-sm">{feature}</span>
                    </div>
                  ))}
                </div>

                {/* Action Button */}
                <Button
                  onClick={() => handlePlanSelect(plan.id)}
                  disabled={currentTier === plan.id && plan.id !== 'trial'}
                  className={`w-full ${plan.buttonClass} text-white py-2.5 sm:py-3 font-semibold text-sm sm:text-base touch-manipulation min-h-[44px]`}
                >
                  {plan.buttonText}
                </Button>
              </Card>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 sm:p-6 border-t border-white/20 bg-white/5">
          <div className="text-center text-gray-400 text-xs sm:text-sm">
            <p>✨ All plans include our core analysis features</p>
            <p className="mt-1">🔒 Cancel anytime • 💳 Secure payment • 🛡️ 30-day money-back guarantee</p>
          </div>
        </div>
      </div>
    </div>
  );
};
