#!/usr/bin/env python3
"""
Enhanced Error Handling Test Suite
Tests the improved error handling system throughout the application
"""

import sys
import os
import time
from datetime import datetime

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from utils.error_handler import (
    ErrorHandler, FinanceError, ErrorType, ErrorSeverity,
    handle_errors, validate_ticker, sanitize_user_input,
    api_circuit_breakers, create_safe_response
)
from agent.tools_and_schemas import get_stock_data, google_search, analyze_sentiment

def test_error_classification():
    """Test error classification system"""
    print("🧪 Testing Error Classification System")
    print("=" * 50)
    
    error_handler = ErrorHandler()
    test_cases = [
        (ValueError("Invalid data format"), ErrorType.DATA_ERROR),
        (KeyError("Missing key"), ErrorType.DATA_ERROR),
        (Exception("429 quota exceeded"), ErrorType.RATE_LIMIT_ERROR),
        (Exception("API key invalid"), ErrorType.AUTHENTICATION_ERROR),
        (Exception("Connection timeout"), ErrorType.TIMEOUT_ERROR),
    ]
    
    passed = 0
    for error, expected_type in test_cases:
        try:
            finance_error = error_handler._classify_error(error, "test_context")
            if finance_error.error_type == expected_type:
                print(f"✅ {error} -> {expected_type.value}")
                passed += 1
            else:
                print(f"❌ {error} -> {finance_error.error_type.value} (expected {expected_type.value})")
        except Exception as e:
            print(f"❌ Classification failed for {error}: {str(e)}")
    
    print(f"Classification tests: {passed}/{len(test_cases)} passed")
    return passed == len(test_cases)

def test_input_validation():
    """Test input validation functions"""
    print("\n🧪 Testing Input Validation")
    print("=" * 50)
    
    # Test ticker validation
    ticker_tests = [
        ("AAPL", True),
        ("MSFT", True),
        ("GOOGL", True),
        ("", False),
        ("TOOLONGTICKERHERE", False),
        ("123", True),
        ("A-B", True),
        ("A.B", True),
        ("INVALID@", False),
        (None, False),
    ]
    
    passed = 0
    for ticker, expected in ticker_tests:
        result = validate_ticker(ticker)
        if result == expected:
            print(f"✅ validate_ticker('{ticker}') -> {result}")
            passed += 1
        else:
            print(f"❌ validate_ticker('{ticker}') -> {result} (expected {expected})")
    
    # Test input sanitization
    sanitize_tests = [
        ("Normal input", "Normal input"),
        ("  Whitespace  ", "Whitespace"),
        ("A" * 2000, "A" * 1000),  # Should truncate
        ("", ""),
        (None, ""),
    ]
    
    for input_text, expected in sanitize_tests:
        result = sanitize_user_input(input_text)
        if (expected and result.startswith(expected[:50])) or (not expected and not result):
            print(f"✅ sanitize_user_input passed")
            passed += 1
        else:
            print(f"❌ sanitize_user_input failed")
    
    total_tests = len(ticker_tests) + len(sanitize_tests)
    print(f"Input validation tests: {passed}/{total_tests} passed")
    return passed == total_tests

def test_circuit_breaker():
    """Test circuit breaker functionality"""
    print("\n🧪 Testing Circuit Breaker")
    print("=" * 50)
    
    from utils.error_handler import CircuitBreaker
    
    # Create test circuit breaker
    cb = CircuitBreaker(failure_threshold=2, recovery_timeout=1)
    
    def failing_function():
        raise Exception("Test failure")
    
    def working_function():
        return "success"
    
    # Test failure accumulation
    failures = 0
    for i in range(3):
        try:
            cb.call(failing_function)
        except Exception:
            failures += 1
    
    # Circuit should be open now
    try:
        cb.call(working_function)
        print("❌ Circuit breaker should be open")
        return False
    except FinanceError as e:
        if "temporarily unavailable" in str(e):
            print("✅ Circuit breaker opened after failures")
        else:
            print(f"❌ Unexpected error: {str(e)}")
            return False
    
    # Wait for recovery timeout
    time.sleep(1.1)
    
    # Should allow one attempt now (half-open)
    try:
        result = cb.call(working_function)
        if result == "success":
            print("✅ Circuit breaker recovered and closed")
            return True
        else:
            print("❌ Circuit breaker recovery failed")
            return False
    except Exception as e:
        print(f"❌ Circuit breaker recovery error: {str(e)}")
        return False

def test_enhanced_tools():
    """Test enhanced tools with error handling"""
    print("\n🧪 Testing Enhanced Tools Error Handling")
    print("=" * 50)
    
    passed = 0
    total = 0
    
    # Test invalid ticker handling
    total += 1
    try:
        result = get_stock_data("INVALID@TICKER")
        if "error" in result and "Invalid ticker" in result["error"]:
            print("✅ get_stock_data handles invalid ticker")
            passed += 1
        else:
            print("❌ get_stock_data should reject invalid ticker")
    except Exception as e:
        print(f"❌ get_stock_data error handling failed: {str(e)}")
    
    # Test empty search query
    total += 1
    try:
        result = google_search("")
        if "error" in result and "Invalid or empty" in result["error"]:
            print("✅ google_search handles empty query")
            passed += 1
        else:
            print("❌ google_search should reject empty query")
    except Exception as e:
        print(f"❌ google_search error handling failed: {str(e)}")
    
    # Test sentiment analysis with invalid ticker
    total += 1
    try:
        result = analyze_sentiment("INVALID@")
        if "Invalid ticker" in result:
            print("✅ analyze_sentiment handles invalid ticker")
            passed += 1
        else:
            print("❌ analyze_sentiment should reject invalid ticker")
    except Exception as e:
        print(f"❌ analyze_sentiment error handling failed: {str(e)}")
    
    print(f"Enhanced tools tests: {passed}/{total} passed")
    return passed == total

def test_decorator_error_handling():
    """Test the error handling decorator"""
    print("\n🧪 Testing Error Handling Decorator")
    print("=" * 50)
    
    @handle_errors(fallback_data={"fallback": True}, context="test_function")
    def test_function(should_fail=False):
        if should_fail:
            raise ValueError("Test error")
        return {"success": True}
    
    # Test successful execution
    result = test_function(should_fail=False)
    if result.get("success"):
        print("✅ Decorator allows successful execution")
        success_test = True
    else:
        print("❌ Decorator interfered with successful execution")
        success_test = False
    
    # Test error handling
    result = test_function(should_fail=True)
    if result.get("fallback"):
        print("✅ Decorator provides fallback data on error")
        error_test = True
    else:
        print("❌ Decorator failed to provide fallback data")
        error_test = False
    
    return success_test and error_test

def main():
    """Run comprehensive error handling tests"""
    print("🚀 ENHANCED ERROR HANDLING TEST SUITE")
    print("=" * 80)
    print("Testing improved error handling system")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_functions = [
        test_error_classification,
        test_input_validation,
        test_circuit_breaker,
        test_enhanced_tools,
        test_decorator_error_handling,
    ]
    
    results = []
    for test_func in test_functions:
        try:
            result = test_func()
            results.append(result)
        except Exception as e:
            print(f"❌ Test {test_func.__name__} failed with exception: {str(e)}")
            results.append(False)
    
    # Summary
    passed = sum(results)
    total = len(results)
    success_rate = (passed / total) * 100
    
    print("\n" + "=" * 80)
    print("📊 ERROR HANDLING TEST SUMMARY")
    print("=" * 80)
    print(f"✅ Passed: {passed}/{total} test suites")
    print(f"📈 Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 EXCELLENT! Error handling system is robust and ready!")
    elif success_rate >= 60:
        print("👍 GOOD! Error handling system is working well.")
    else:
        print("⚠️ NEEDS IMPROVEMENT! Error handling system requires attention.")
    
    print("=" * 80)
    return success_rate >= 80

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
