"""
Portfolio API Endpoints for FinanceGPT Pro
Handles portfolio CRUD operations, performance analysis, and portfolio management
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import List, Dict, Any, Optional
import logging

from ..services.portfolio_service import PortfolioService
from ..middleware.token_middleware import get_current_user
from ..models.user_profile_models import user_profile_manager

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/portfolio", tags=["portfolio"])

# Initialize portfolio service
portfolio_service = PortfolioService()

# Pydantic models for request/response
class CreatePortfolioRequest(BaseModel):
    name: str = Field(..., description="Portfolio name", min_length=1, max_length=100)

class AddHoldingRequest(BaseModel):
    ticker: str = Field(..., description="Stock ticker symbol", min_length=1, max_length=10)
    shares: float = Field(..., description="Number of shares", gt=0)
    purchase_price: float = Field(..., description="Purchase price per share", gt=0)

class UpdateHoldingRequest(BaseModel):
    shares: float = Field(..., description="Updated number of shares", gt=0)
    purchase_price: float = Field(..., description="Updated purchase price per share", gt=0)

class PortfolioResponse(BaseModel):
    portfolio_id: str
    name: str
    total_value: float
    total_gain_loss: float
    total_gain_loss_percent: float
    holdings_count: int
    cash_balance: float
    created_date: str
    last_updated: str

class HoldingResponse(BaseModel):
    ticker: str
    company_name: str
    shares: float
    average_cost: float
    current_price: float
    market_value: float
    unrealized_gain_loss: float
    unrealized_gain_loss_percent: float
    sector: str

# Portfolio Management Endpoints
@router.get("/list")
async def get_user_portfolios(user = Depends(get_current_user)):
    """Get all portfolios for the current user"""
    try:
        portfolios = portfolio_service.get_user_portfolios(user.user_id)
        return {
            "success": True,
            "data": portfolios
        }
    except Exception as e:
        logger.error(f"Error getting user portfolios: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve portfolios")

@router.post("/create")
async def create_portfolio(
    request: CreatePortfolioRequest,
    user = Depends(get_current_user)
):
    """Create a new portfolio"""
    try:
        portfolio = portfolio_service.create_portfolio(user.user_id, request.name)
        return {
            "success": True,
            "message": "Portfolio created successfully",
            "data": {
                "portfolio_id": portfolio.portfolio_id,
                "name": portfolio.name,
                "created_date": portfolio.created_date.isoformat()
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error creating portfolio: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to create portfolio")

@router.get("/{portfolio_id}")
async def get_portfolio_details(
    portfolio_id: str,
    user = Depends(get_current_user)
):
    """Get detailed portfolio information"""
    try:
        # Get portfolio performance data
        performance = portfolio_service.get_portfolio_performance(user.user_id, portfolio_id)
        return {
            "success": True,
            "data": performance
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting portfolio details: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve portfolio details")

@router.delete("/{portfolio_id}")
async def delete_portfolio(
    portfolio_id: str,
    user = Depends(get_current_user)
):
    """Delete a portfolio"""
    try:
        success = portfolio_service.delete_portfolio(user.user_id, portfolio_id)
        if success:
            return {
                "success": True,
                "message": "Portfolio deleted successfully"
            }
        else:
            raise HTTPException(status_code=404, detail="Portfolio not found")
    except Exception as e:
        logger.error(f"Error deleting portfolio: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to delete portfolio")

# Holdings Management Endpoints
@router.post("/{portfolio_id}/holdings")
async def add_holding(
    portfolio_id: str,
    request: AddHoldingRequest,
    user = Depends(get_current_user)
):
    """Add a holding to portfolio"""
    try:
        holding = portfolio_service.add_holding(
            user.user_id,
            portfolio_id,
            request.ticker.upper(),
            request.shares,
            request.purchase_price
        )
        
        return {
            "success": True,
            "message": f"Added {request.shares} shares of {request.ticker.upper()}",
            "data": {
                "ticker": holding.ticker,
                "shares": holding.shares,
                "average_cost": holding.average_cost,
                "market_value": holding.market_value
            }
        }
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error adding holding: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to add holding")

@router.put("/{portfolio_id}/holdings/{ticker}")
async def update_holding(
    portfolio_id: str,
    ticker: str,
    request: UpdateHoldingRequest,
    user = Depends(get_current_user)
):
    """Update a holding in portfolio"""
    try:
        success = portfolio_service.update_holding(
            user.user_id,
            portfolio_id,
            ticker.upper(),
            request.shares,
            request.purchase_price
        )
        
        if success:
            return {
                "success": True,
                "message": f"Updated {ticker.upper()} holding"
            }
        else:
            raise HTTPException(status_code=404, detail="Holding not found")
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Error updating holding: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to update holding")

@router.delete("/{portfolio_id}/holdings/{ticker}")
async def remove_holding(
    portfolio_id: str,
    ticker: str,
    user = Depends(get_current_user)
):
    """Remove a holding from portfolio"""
    try:
        success = portfolio_service.remove_holding(user.user_id, portfolio_id, ticker.upper())
        if success:
            return {
                "success": True,
                "message": f"Removed {ticker.upper()} from portfolio"
            }
        else:
            raise HTTPException(status_code=404, detail="Holding not found")
    except Exception as e:
        logger.error(f"Error removing holding: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to remove holding")

# Performance Analysis Endpoints
@router.get("/{portfolio_id}/performance")
async def get_portfolio_performance(
    portfolio_id: str,
    user = Depends(get_current_user)
):
    """Get comprehensive portfolio performance analysis"""
    try:
        performance = portfolio_service.get_portfolio_performance(user.user_id, portfolio_id)
        return {
            "success": True,
            "data": performance
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting portfolio performance: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve performance data")

@router.get("/{portfolio_id}/comparison")
async def get_portfolio_comparison(
    portfolio_id: str,
    user = Depends(get_current_user)
):
    """Get portfolio comparison against benchmarks"""
    try:
        comparison = portfolio_service.get_portfolio_comparison(user.user_id, portfolio_id)
        return {
            "success": True,
            "data": comparison
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting portfolio comparison: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve comparison data")

@router.get("/{portfolio_id}/optimization")
async def get_portfolio_optimization(
    portfolio_id: str,
    user = Depends(get_current_user)
):
    """Get portfolio optimization suggestions"""
    try:
        optimization = portfolio_service.optimize_portfolio(user.user_id, portfolio_id)
        return {
            "success": True,
            "data": optimization
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting portfolio optimization: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve optimization suggestions")

# Risk Analysis Endpoints
@router.get("/{portfolio_id}/risk-analysis")
async def get_risk_analysis(
    portfolio_id: str,
    user = Depends(get_current_user)
):
    """Get comprehensive risk analysis for portfolio"""
    try:
        # This would typically cost tokens for Pro users
        risk_analysis = portfolio_service.get_risk_analysis(user.user_id, portfolio_id)
        return {
            "success": True,
            "data": risk_analysis,
            "tokens_used": 100  # Risk analysis costs 100 tokens
        }
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Error getting risk analysis: {str(e)}")
        raise HTTPException(status_code=500, detail="Failed to retrieve risk analysis")

# Health Check
@router.get("/health")
async def portfolio_health_check():
    """Health check for portfolio service"""
    return {
        "status": "healthy",
        "service": "portfolio",
        "version": "1.0.0"
    }
