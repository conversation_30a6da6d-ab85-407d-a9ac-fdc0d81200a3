"""
Advanced Stock Chart Image Analysis System
Implements computer vision and machine learning for technical analysis
"""

import cv2
import numpy as np
from PIL import Image
import pytesseract
from typing import Dict, List, Tuple, Optional
import json
from dataclasses import dataclass
from enum import Enum

class PatternType(Enum):
    HEAD_AND_SHOULDERS = "Head and Shoulders"
    DOUBLE_TOP = "Double Top"
    DOUBLE_BOTTOM = "Double Bottom"
    ASCENDING_TRIANGLE = "Ascending Triangle"
    DESCENDING_TRIANGLE = "Descending Triangle"
    BULL_FLAG = "Bull Flag"
    BEAR_FLAG = "Bear Flag"
    CUP_AND_HANDLE = "Cup and Handle"
    WEDGE_RISING = "Rising Wedge"
    WEDGE_FALLING = "Falling Wedge"

@dataclass
class ChartData:
    """Extracted chart data structure"""
    prices: List[float]
    volumes: List[float]
    timestamps: List[str]
    support_levels: List[float]
    resistance_levels: List[float]
    trend_lines: List[Dict]
    patterns: List[Dict]

@dataclass
class AnalysisResult:
    """Complete analysis result"""
    chart_data: ChartData
    technical_indicators: Dict
    patterns_detected: List[Dict]
    price_targets: Dict
    risk_levels: Dict
    confidence_score: float
    recommendations: Dict

class ChartImageAnalyzer:
    """Main chart analysis engine"""
    
    def __init__(self):
        self.min_confidence = 0.7
        self.pattern_templates = self._load_pattern_templates()
    
    def analyze_chart_image(self, image_path: str) -> AnalysisResult:
        """
        Main analysis pipeline for stock chart images
        """
        try:
            # 1. Load and preprocess image
            image = self._load_image(image_path)
            processed_image = self._preprocess_image(image)
            
            # 2. Extract chart components
            chart_area = self._extract_chart_area(processed_image)
            price_data = self._extract_price_data(chart_area)
            volume_data = self._extract_volume_data(chart_area)
            
            # 3. Identify key levels
            support_levels = self._find_support_levels(price_data)
            resistance_levels = self._find_resistance_levels(price_data)
            trend_lines = self._detect_trend_lines(price_data)
            
            # 4. Pattern recognition
            patterns = self._detect_patterns(price_data, chart_area)
            
            # 5. Technical analysis
            indicators = self._calculate_technical_indicators(price_data)
            
            # 6. Generate recommendations
            recommendations = self._generate_recommendations(
                price_data, patterns, indicators, support_levels, resistance_levels
            )
            
            # 7. Calculate confidence score
            confidence = self._calculate_confidence_score(
                price_data, patterns, indicators
            )
            
            # 8. Compile results
            chart_data = ChartData(
                prices=price_data,
                volumes=volume_data,
                timestamps=[],  # Would extract from chart if available
                support_levels=support_levels,
                resistance_levels=resistance_levels,
                trend_lines=trend_lines,
                patterns=patterns
            )
            
            return AnalysisResult(
                chart_data=chart_data,
                technical_indicators=indicators,
                patterns_detected=patterns,
                price_targets=self._calculate_price_targets(price_data, patterns),
                risk_levels=self._assess_risk_levels(price_data, support_levels),
                confidence_score=confidence,
                recommendations=recommendations
            )
            
        except Exception as e:
            raise Exception(f"Chart analysis failed: {str(e)}")
    
    def _load_image(self, image_path: str) -> np.ndarray:
        """Load and validate image"""
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError("Could not load image")
        return image
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """Preprocess image for analysis"""
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # Enhance contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        enhanced = clahe.apply(gray)
        
        # Noise reduction
        denoised = cv2.bilateralFilter(enhanced, 9, 75, 75)
        
        return denoised
    
    def _extract_chart_area(self, image: np.ndarray) -> np.ndarray:
        """Extract the main chart area from the image"""
        # Find chart boundaries using edge detection
        edges = cv2.Canny(image, 50, 150)
        
        # Find contours to identify chart area
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Find largest rectangular contour (likely the chart)
        largest_contour = max(contours, key=cv2.contourArea)
        x, y, w, h = cv2.boundingRect(largest_contour)
        
        # Extract chart area with some padding
        padding = 10
        chart_area = image[y+padding:y+h-padding, x+padding:x+w-padding]
        
        return chart_area
    
    def _extract_price_data(self, chart_area: np.ndarray) -> List[float]:
        """Extract price data points from chart"""
        # This is a simplified version - real implementation would use
        # more sophisticated computer vision techniques
        
        height, width = chart_area.shape
        
        # Find candlestick or line patterns
        price_points = []
        
        # Scan chart from left to right
        for x in range(0, width, 5):  # Sample every 5 pixels
            column = chart_area[:, x]
            
            # Find price level (highest point in column)
            non_zero_points = np.where(column < 200)[0]  # Assuming dark lines on light background
            
            if len(non_zero_points) > 0:
                # Convert pixel position to price (this would need calibration)
                price_level = height - np.min(non_zero_points)
                normalized_price = (price_level / height) * 100 + 100  # Scale to reasonable price range
                price_points.append(normalized_price)
        
        return price_points
    
    def _extract_volume_data(self, chart_area: np.ndarray) -> List[float]:
        """Extract volume data if present"""
        # Simplified volume extraction
        # In real implementation, would identify volume bars at bottom of chart
        return [100 + np.random.random() * 50 for _ in range(50)]  # Mock data
    
    def _find_support_levels(self, price_data: List[float]) -> List[float]:
        """Identify support levels"""
        if not price_data:
            return []
        
        # Find local minima
        support_levels = []
        window = 5
        
        for i in range(window, len(price_data) - window):
            if all(price_data[i] <= price_data[i-j] for j in range(1, window+1)) and \
               all(price_data[i] <= price_data[i+j] for j in range(1, window+1)):
                support_levels.append(price_data[i])
        
        # Remove duplicates and sort
        support_levels = sorted(list(set(support_levels)))
        
        return support_levels[:3]  # Return top 3 support levels
    
    def _find_resistance_levels(self, price_data: List[float]) -> List[float]:
        """Identify resistance levels"""
        if not price_data:
            return []
        
        # Find local maxima
        resistance_levels = []
        window = 5
        
        for i in range(window, len(price_data) - window):
            if all(price_data[i] >= price_data[i-j] for j in range(1, window+1)) and \
               all(price_data[i] >= price_data[i+j] for j in range(1, window+1)):
                resistance_levels.append(price_data[i])
        
        # Remove duplicates and sort
        resistance_levels = sorted(list(set(resistance_levels)), reverse=True)
        
        return resistance_levels[:3]  # Return top 3 resistance levels
    
    def _detect_trend_lines(self, price_data: List[float]) -> List[Dict]:
        """Detect trend lines"""
        # Simplified trend line detection
        if len(price_data) < 10:
            return []
        
        # Calculate moving averages for trend detection
        short_ma = np.convolve(price_data, np.ones(5)/5, mode='valid')
        long_ma = np.convolve(price_data, np.ones(20)/20, mode='valid')
        
        trend_lines = []
        
        if len(short_ma) > 0 and len(long_ma) > 0:
            if short_ma[-1] > long_ma[-1]:
                trend_lines.append({
                    'type': 'uptrend',
                    'strength': 'strong' if short_ma[-1] - long_ma[-1] > 2 else 'moderate',
                    'slope': (short_ma[-1] - short_ma[0]) / len(short_ma)
                })
            else:
                trend_lines.append({
                    'type': 'downtrend',
                    'strength': 'strong' if long_ma[-1] - short_ma[-1] > 2 else 'moderate',
                    'slope': (short_ma[-1] - short_ma[0]) / len(short_ma)
                })
        
        return trend_lines
    
    def _detect_patterns(self, price_data: List[float], chart_area: np.ndarray) -> List[Dict]:
        """Detect chart patterns"""
        patterns = []
        
        if len(price_data) < 20:
            return patterns
        
        # Simple pattern detection logic
        recent_prices = price_data[-20:]
        
        # Head and Shoulders detection (simplified)
        if self._is_head_and_shoulders(recent_prices):
            patterns.append({
                'type': PatternType.HEAD_AND_SHOULDERS.value,
                'confidence': 0.75,
                'bullish': False,
                'completion': 0.8
            })
        
        # Double Top detection
        if self._is_double_top(recent_prices):
            patterns.append({
                'type': PatternType.DOUBLE_TOP.value,
                'confidence': 0.68,
                'bullish': False,
                'completion': 0.9
            })
        
        # Ascending Triangle detection
        if self._is_ascending_triangle(recent_prices):
            patterns.append({
                'type': PatternType.ASCENDING_TRIANGLE.value,
                'confidence': 0.82,
                'bullish': True,
                'completion': 0.7
            })
        
        return patterns
    
    def _is_head_and_shoulders(self, prices: List[float]) -> bool:
        """Simplified Head and Shoulders detection"""
        if len(prices) < 15:
            return False
        
        # Find three peaks
        peaks = []
        for i in range(2, len(prices) - 2):
            if prices[i] > prices[i-1] and prices[i] > prices[i+1] and \
               prices[i] > prices[i-2] and prices[i] > prices[i+2]:
                peaks.append((i, prices[i]))
        
        if len(peaks) >= 3:
            # Check if middle peak is highest (head)
            peaks.sort(key=lambda x: x[1], reverse=True)
            if peaks[0][0] > peaks[1][0] and peaks[0][0] > peaks[2][0]:
                return True
        
        return False
    
    def _is_double_top(self, prices: List[float]) -> bool:
        """Simplified Double Top detection"""
        if len(prices) < 10:
            return False
        
        max_price = max(prices)
        max_indices = [i for i, p in enumerate(prices) if abs(p - max_price) < 0.5]
        
        return len(max_indices) >= 2 and max_indices[-1] - max_indices[0] > 5
    
    def _is_ascending_triangle(self, prices: List[float]) -> bool:
        """Simplified Ascending Triangle detection"""
        if len(prices) < 15:
            return False
        
        # Check for horizontal resistance and rising support
        recent_highs = [max(prices[i:i+3]) for i in range(0, len(prices)-2, 3)]
        recent_lows = [min(prices[i:i+3]) for i in range(0, len(prices)-2, 3)]
        
        # Horizontal resistance (highs relatively flat)
        high_variance = np.var(recent_highs[-3:]) if len(recent_highs) >= 3 else float('inf')
        
        # Rising support (lows trending up)
        low_trend = np.polyfit(range(len(recent_lows)), recent_lows, 1)[0] if len(recent_lows) >= 3 else 0
        
        return high_variance < 2.0 and low_trend > 0.1
    
    def _calculate_technical_indicators(self, price_data: List[float]) -> Dict:
        """Calculate technical indicators"""
        if len(price_data) < 14:
            return {}
        
        # RSI calculation (simplified)
        rsi = self._calculate_rsi(price_data)
        
        # Moving averages
        sma_5 = np.mean(price_data[-5:]) if len(price_data) >= 5 else price_data[-1]
        sma_20 = np.mean(price_data[-20:]) if len(price_data) >= 20 else price_data[-1]
        
        # MACD (simplified)
        macd = sma_5 - sma_20
        
        return {
            'rsi': rsi,
            'sma_5': sma_5,
            'sma_20': sma_20,
            'macd': macd,
            'current_price': price_data[-1] if price_data else 0
        }
    
    def _calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """Calculate RSI indicator"""
        if len(prices) < period + 1:
            return 50.0  # Neutral RSI
        
        deltas = [prices[i] - prices[i-1] for i in range(1, len(prices))]
        gains = [d if d > 0 else 0 for d in deltas]
        losses = [-d if d < 0 else 0 for d in deltas]
        
        avg_gain = np.mean(gains[-period:])
        avg_loss = np.mean(losses[-period:])
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        
        return round(rsi, 2)
    
    def _calculate_price_targets(self, price_data: List[float], patterns: List[Dict]) -> Dict:
        """Calculate price targets based on patterns"""
        if not price_data or not patterns:
            return {}
        
        current_price = price_data[-1]
        targets = {}
        
        for pattern in patterns:
            if pattern['bullish']:
                targets['upside_target'] = current_price * 1.08  # 8% upside
                targets['extended_target'] = current_price * 1.15  # 15% upside
            else:
                targets['downside_target'] = current_price * 0.92  # 8% downside
                targets['extended_target'] = current_price * 0.85  # 15% downside
        
        return targets
    
    def _assess_risk_levels(self, price_data: List[float], support_levels: List[float]) -> Dict:
        """Assess risk levels"""
        if not price_data:
            return {}
        
        current_price = price_data[-1]
        
        risk_levels = {
            'stop_loss': current_price * 0.95,  # 5% stop loss
            'risk_level': 'medium'
        }
        
        if support_levels:
            nearest_support = min(support_levels, key=lambda x: abs(x - current_price))
            risk_levels['stop_loss'] = nearest_support * 0.98  # Just below support
            
            distance_to_support = abs(current_price - nearest_support) / current_price
            if distance_to_support < 0.02:
                risk_levels['risk_level'] = 'low'
            elif distance_to_support > 0.05:
                risk_levels['risk_level'] = 'high'
        
        return risk_levels
    
    def _generate_recommendations(self, price_data: List[float], patterns: List[Dict], 
                                indicators: Dict, support_levels: List[float], 
                                resistance_levels: List[float]) -> Dict:
        """Generate trading recommendations"""
        if not price_data:
            return {}
        
        recommendations = {
            'action': 'HOLD',
            'confidence': 0.5,
            'reasoning': []
        }
        
        # Pattern-based recommendations
        bullish_patterns = [p for p in patterns if p.get('bullish', False)]
        bearish_patterns = [p for p in patterns if not p.get('bullish', True)]
        
        if bullish_patterns:
            recommendations['action'] = 'BUY'
            recommendations['confidence'] += 0.2
            recommendations['reasoning'].append('Bullish patterns detected')
        
        if bearish_patterns:
            recommendations['action'] = 'SELL'
            recommendations['confidence'] += 0.2
            recommendations['reasoning'].append('Bearish patterns detected')
        
        # Technical indicator recommendations
        if indicators.get('rsi', 50) < 30:
            recommendations['action'] = 'BUY'
            recommendations['confidence'] += 0.15
            recommendations['reasoning'].append('RSI oversold')
        elif indicators.get('rsi', 50) > 70:
            recommendations['action'] = 'SELL'
            recommendations['confidence'] += 0.15
            recommendations['reasoning'].append('RSI overbought')
        
        # MACD recommendations
        if indicators.get('macd', 0) > 0:
            recommendations['confidence'] += 0.1
            recommendations['reasoning'].append('MACD bullish crossover')
        
        recommendations['confidence'] = min(recommendations['confidence'], 1.0)
        
        return recommendations
    
    def _calculate_confidence_score(self, price_data: List[float], patterns: List[Dict], 
                                  indicators: Dict) -> float:
        """Calculate overall analysis confidence"""
        base_confidence = 0.6
        
        # More data points = higher confidence
        if len(price_data) > 50:
            base_confidence += 0.1
        
        # Pattern detection confidence
        if patterns:
            avg_pattern_confidence = np.mean([p.get('confidence', 0.5) for p in patterns])
            base_confidence += avg_pattern_confidence * 0.2
        
        # Technical indicators confidence
        if indicators:
            base_confidence += 0.1
        
        return min(base_confidence, 1.0)
    
    def _load_pattern_templates(self) -> Dict:
        """Load pattern templates for matching"""
        # In real implementation, would load ML models or template images
        return {}

# Usage example
if __name__ == "__main__":
    analyzer = ChartImageAnalyzer()
    
    try:
        result = analyzer.analyze_chart_image("sample_chart.png")
        print(f"Analysis completed with {result.confidence_score:.2%} confidence")
        print(f"Patterns detected: {len(result.patterns_detected)}")
        print(f"Recommendation: {result.recommendations.get('action', 'HOLD')}")
    except Exception as e:
        print(f"Analysis failed: {e}")
