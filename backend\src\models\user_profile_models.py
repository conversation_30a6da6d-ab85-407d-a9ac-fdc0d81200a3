"""
User Profile Models for FinanceGPT Pro
Handles user profiles, risk assessment, portfolio tracking, and personalized recommendations
"""

from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid
import json

class RiskTolerance(Enum):
    CONSERVATIVE = "conservative"
    MODERATE = "moderate"
    AGGRESSIVE = "aggressive"

class InvestmentGoal(Enum):
    RETIREMENT = "retirement"
    GROWTH = "growth"
    INCOME = "income"
    PRESERVATION = "preservation"
    SPECULATION = "speculation"

class TimeHorizon(Enum):
    SHORT_TERM = "short_term"  # < 2 years
    MEDIUM_TERM = "medium_term"  # 2-10 years
    LONG_TERM = "long_term"  # > 10 years

class PortfolioAllocation(Enum):
    CONSERVATIVE = {"stocks": 30, "bonds": 60, "cash": 10}
    MODERATE = {"stocks": 60, "bonds": 30, "cash": 10}
    AGGRESSIVE = {"stocks": 80, "bonds": 15, "cash": 5}

@dataclass
class RiskAssessmentQuestion:
    """Individual risk assessment question"""
    question_id: str
    question_text: str
    question_type: str  # "multiple_choice", "scale", "boolean"
    options: List[str] = field(default_factory=list)
    weight: float = 1.0
    category: str = "general"  # "experience", "goals", "timeline", "comfort"

# Risk Assessment Questions Database
RISK_ASSESSMENT_QUESTIONS = [
    RiskAssessmentQuestion(
        question_id="experience_years",
        question_text="How many years of investment experience do you have?",
        question_type="multiple_choice",
        options=["Less than 1 year", "1-3 years", "3-10 years", "More than 10 years"],
        weight=1.5,
        category="experience"
    ),
    RiskAssessmentQuestion(
        question_id="investment_knowledge",
        question_text="How would you rate your investment knowledge?",
        question_type="multiple_choice",
        options=["Beginner", "Some knowledge", "Knowledgeable", "Expert"],
        weight=1.2,
        category="experience"
    ),
    RiskAssessmentQuestion(
        question_id="primary_goal",
        question_text="What is your primary investment goal?",
        question_type="multiple_choice",
        options=["Capital preservation", "Income generation", "Balanced growth", "Aggressive growth"],
        weight=2.0,
        category="goals"
    ),
    RiskAssessmentQuestion(
        question_id="time_horizon",
        question_text="What is your investment time horizon?",
        question_type="multiple_choice",
        options=["Less than 2 years", "2-5 years", "5-10 years", "More than 10 years"],
        weight=1.8,
        category="timeline"
    ),
    RiskAssessmentQuestion(
        question_id="loss_comfort",
        question_text="How would you react to a 20% portfolio loss in one year?",
        question_type="multiple_choice",
        options=["Sell everything immediately", "Sell some investments", "Hold steady", "Buy more at lower prices"],
        weight=2.5,
        category="comfort"
    ),
    RiskAssessmentQuestion(
        question_id="income_stability",
        question_text="How stable is your current income?",
        question_type="multiple_choice",
        options=["Very unstable", "Somewhat unstable", "Stable", "Very stable"],
        weight=1.3,
        category="financial"
    ),
    RiskAssessmentQuestion(
        question_id="emergency_fund",
        question_text="Do you have an emergency fund covering 3-6 months of expenses?",
        question_type="boolean",
        options=["Yes", "No"],
        weight=1.0,
        category="financial"
    )
]

@dataclass
class RiskAssessmentResult:
    """Result of risk assessment"""
    user_id: str
    risk_score: float  # 0-100 scale
    risk_tolerance: RiskTolerance
    recommended_allocation: Dict[str, int]
    assessment_date: datetime = field(default_factory=datetime.now)
    answers: Dict[str, Any] = field(default_factory=dict)
    
    def get_risk_description(self) -> str:
        """Get description of risk tolerance"""
        descriptions = {
            RiskTolerance.CONSERVATIVE: "You prefer stability and capital preservation over high returns. You're comfortable with lower, more predictable returns.",
            RiskTolerance.MODERATE: "You seek a balance between growth and stability. You can tolerate some volatility for potentially higher returns.",
            RiskTolerance.AGGRESSIVE: "You're willing to accept high volatility and potential losses for the possibility of higher returns."
        }
        return descriptions.get(self.risk_tolerance, "Unknown risk tolerance")

@dataclass
class PortfolioHolding:
    """Individual stock/asset holding"""
    holding_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    ticker: str = ""
    company_name: str = ""
    shares: float = 0.0
    average_cost: float = 0.0
    current_price: float = 0.0
    sector: str = ""
    purchase_date: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    
    @property
    def market_value(self) -> float:
        """Current market value of holding"""
        return self.shares * self.current_price
    
    @property
    def cost_basis(self) -> float:
        """Total cost basis of holding"""
        return self.shares * self.average_cost
    
    @property
    def unrealized_gain_loss(self) -> float:
        """Unrealized gain/loss"""
        return self.market_value - self.cost_basis
    
    @property
    def unrealized_gain_loss_percent(self) -> float:
        """Unrealized gain/loss percentage"""
        if self.cost_basis == 0:
            return 0.0
        return (self.unrealized_gain_loss / self.cost_basis) * 100

@dataclass
class Portfolio:
    """User's investment portfolio"""
    portfolio_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    user_id: str = ""
    name: str = "My Portfolio"
    holdings: List[PortfolioHolding] = field(default_factory=list)
    cash_balance: float = 0.0
    created_date: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    
    @property
    def total_market_value(self) -> float:
        """Total portfolio market value"""
        return sum(holding.market_value for holding in self.holdings) + self.cash_balance
    
    @property
    def total_cost_basis(self) -> float:
        """Total portfolio cost basis"""
        return sum(holding.cost_basis for holding in self.holdings) + self.cash_balance
    
    @property
    def total_unrealized_gain_loss(self) -> float:
        """Total unrealized gain/loss"""
        return sum(holding.unrealized_gain_loss for holding in self.holdings)
    
    @property
    def total_unrealized_gain_loss_percent(self) -> float:
        """Total unrealized gain/loss percentage"""
        if self.total_cost_basis == 0:
            return 0.0
        return (self.total_unrealized_gain_loss / self.total_cost_basis) * 100
    
    def get_sector_allocation(self) -> Dict[str, float]:
        """Get portfolio allocation by sector"""
        sector_values = {}
        total_value = self.total_market_value
        
        if total_value == 0:
            return {}
        
        for holding in self.holdings:
            sector = holding.sector or "Unknown"
            if sector not in sector_values:
                sector_values[sector] = 0.0
            sector_values[sector] += holding.market_value
        
        # Convert to percentages
        return {sector: (value / total_value) * 100 for sector, value in sector_values.items()}
    
    def get_top_holdings(self, limit: int = 10) -> List[PortfolioHolding]:
        """Get top holdings by market value"""
        return sorted(self.holdings, key=lambda h: h.market_value, reverse=True)[:limit]
    
    def add_holding(self, ticker: str, shares: float, price: float, company_name: str = "", sector: str = ""):
        """Add a new holding to the portfolio"""
        holding = PortfolioHolding(
            ticker=ticker,
            company_name=company_name,
            shares=shares,
            average_cost=price,
            current_price=price,
            sector=sector
        )
        self.holdings.append(holding)
        self.last_updated = datetime.now()
    
    def update_holding_price(self, ticker: str, new_price: float):
        """Update the current price of a holding"""
        for holding in self.holdings:
            if holding.ticker == ticker:
                holding.current_price = new_price
                holding.last_updated = datetime.now()
        self.last_updated = datetime.now()

@dataclass
class UserProfile:
    """Comprehensive user profile"""
    user_id: str
    username: str
    email: str
    full_name: str = ""
    age: Optional[int] = None
    location: str = ""
    
    # Investment Profile
    risk_tolerance: Optional[RiskTolerance] = None
    investment_goals: List[InvestmentGoal] = field(default_factory=list)
    time_horizon: Optional[TimeHorizon] = None
    annual_income: Optional[float] = None
    net_worth: Optional[float] = None
    
    # Portfolio
    portfolios: List[Portfolio] = field(default_factory=list)
    
    # Preferences
    preferred_sectors: List[str] = field(default_factory=list)
    excluded_sectors: List[str] = field(default_factory=list)
    notification_preferences: Dict[str, bool] = field(default_factory=dict)
    
    # Assessment History
    risk_assessments: List[RiskAssessmentResult] = field(default_factory=list)
    
    # Metadata
    created_date: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)
    last_login: Optional[datetime] = None
    
    def get_primary_portfolio(self) -> Optional[Portfolio]:
        """Get the primary (first) portfolio"""
        return self.portfolios[0] if self.portfolios else None
    
    def get_latest_risk_assessment(self) -> Optional[RiskAssessmentResult]:
        """Get the most recent risk assessment"""
        if not self.risk_assessments:
            return None
        return max(self.risk_assessments, key=lambda x: x.assessment_date)
    
    def needs_risk_assessment(self) -> bool:
        """Check if user needs a new risk assessment"""
        latest = self.get_latest_risk_assessment()
        if not latest:
            return True
        
        # Require new assessment every 6 months
        return (datetime.now() - latest.assessment_date).days > 180
    
    def get_investment_summary(self) -> Dict[str, Any]:
        """Get comprehensive investment summary"""
        primary_portfolio = self.get_primary_portfolio()
        latest_assessment = self.get_latest_risk_assessment()
        
        summary = {
            "user_id": self.user_id,
            "risk_tolerance": self.risk_tolerance.value if self.risk_tolerance else None,
            "investment_goals": [goal.value for goal in self.investment_goals],
            "time_horizon": self.time_horizon.value if self.time_horizon else None,
            "needs_risk_assessment": self.needs_risk_assessment(),
            "portfolio_summary": None,
            "risk_assessment_summary": None
        }
        
        if primary_portfolio:
            summary["portfolio_summary"] = {
                "total_value": primary_portfolio.total_market_value,
                "total_gain_loss": primary_portfolio.total_unrealized_gain_loss,
                "total_gain_loss_percent": primary_portfolio.total_unrealized_gain_loss_percent,
                "holdings_count": len(primary_portfolio.holdings),
                "sector_allocation": primary_portfolio.get_sector_allocation(),
                "top_holdings": [
                    {
                        "ticker": h.ticker,
                        "value": h.market_value,
                        "gain_loss_percent": h.unrealized_gain_loss_percent
                    }
                    for h in primary_portfolio.get_top_holdings(5)
                ]
            }
        
        if latest_assessment:
            summary["risk_assessment_summary"] = {
                "risk_score": latest_assessment.risk_score,
                "risk_tolerance": latest_assessment.risk_tolerance.value,
                "assessment_date": latest_assessment.assessment_date.isoformat(),
                "recommended_allocation": latest_assessment.recommended_allocation
            }
        
        return summary

class UserProfileManager:
    """Manager for user profiles and related operations"""
    
    def __init__(self):
        self.profiles: Dict[str, UserProfile] = {}
    
    def create_profile(self, user_id: str, username: str, email: str, **kwargs) -> UserProfile:
        """Create a new user profile"""
        profile = UserProfile(
            user_id=user_id,
            username=username,
            email=email,
            **kwargs
        )
        
        # Create default portfolio
        default_portfolio = Portfolio(
            user_id=user_id,
            name="My Portfolio"
        )
        profile.portfolios.append(default_portfolio)
        
        self.profiles[user_id] = profile
        return profile
    
    def get_profile(self, user_id: str) -> Optional[UserProfile]:
        """Get user profile by ID"""
        return self.profiles.get(user_id)
    
    def update_profile(self, user_id: str, **updates) -> bool:
        """Update user profile"""
        profile = self.get_profile(user_id)
        if not profile:
            return False
        
        for key, value in updates.items():
            if hasattr(profile, key):
                setattr(profile, key, value)
        
        profile.last_updated = datetime.now()
        return True
    
    def delete_profile(self, user_id: str) -> bool:
        """Delete user profile"""
        if user_id in self.profiles:
            del self.profiles[user_id]
            return True
        return False

# Global user profile manager instance
user_profile_manager = UserProfileManager()
