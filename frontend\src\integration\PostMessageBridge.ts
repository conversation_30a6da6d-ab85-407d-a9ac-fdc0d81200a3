export interface WidgetMessage {
  type: string;
  data?: any;
  id?: string;
  timestamp?: number;
}

export interface WidgetEventHandlers {
  onReady?: () => void;
  onUserAction?: (action: string, data: any) => void;
  onPlanChange?: (newPlan: string) => void;
  onTokenUsage?: (tokensUsed: number) => void;
  onError?: (error: string) => void;
  onResize?: (dimensions: { width: number; height: number }) => void;
}

export class PostMessageBridge {
  private messageId = 0;
  private pendingMessages = new Map<string, (response: any) => void>();
  private eventHandlers: WidgetEventHandlers = {};
  private targetOrigin: string;
  private targetWindow: Window;

  constructor(targetWindow: Window = window.parent, targetOrigin: string = '*') {
    this.targetWindow = targetWindow;
    this.targetOrigin = targetOrigin;
    this.setupMessageListener();
  }

  private setupMessageListener(): void {
    window.addEventListener('message', (event) => {
      // Security check
      if (this.targetOrigin !== '*' && event.origin !== this.targetOrigin) {
        return;
      }

      this.handleMessage(event.data);
    });
  }

  private handleMessage(message: WidgetMessage): void {
    const { type, data, id } = message;

    // Handle responses to pending messages
    if (id && this.pendingMessages.has(id)) {
      const resolver = this.pendingMessages.get(id);
      resolver?.(data);
      this.pendingMessages.delete(id);
      return;
    }

    // Handle different message types
    switch (type) {
      case 'WIDGET_READY':
        this.eventHandlers.onReady?.();
        break;

      case 'USER_ACTION':
        this.eventHandlers.onUserAction?.(data.action, data.payload);
        break;

      case 'PLAN_CHANGE':
        this.eventHandlers.onPlanChange?.(data.newPlan);
        break;

      case 'TOKEN_USAGE':
        this.eventHandlers.onTokenUsage?.(data.tokensUsed);
        break;

      case 'ERROR':
        this.eventHandlers.onError?.(data.message);
        break;

      case 'RESIZE':
        this.eventHandlers.onResize?.(data.dimensions);
        break;

      case 'CONFIG_UPDATE':
        // Handle configuration updates from parent
        this.handleConfigUpdate(data);
        break;

      case 'PING':
        // Respond to ping with pong
        this.sendMessage('PONG', { timestamp: Date.now() });
        break;

      default:
        console.warn('Unknown message type:', type);
    }
  }

  private handleConfigUpdate(newConfig: any): void {
    // Dispatch custom event for config updates
    window.dispatchEvent(new CustomEvent('widget-config-update', {
      detail: newConfig
    }));
  }

  // Send message to parent/target window
  sendMessage(type: string, data?: any): Promise<any> {
    return new Promise((resolve) => {
      const id = `msg_${++this.messageId}`;
      const message: WidgetMessage = {
        type,
        data,
        id,
        timestamp: Date.now()
      };

      // Store resolver for response
      this.pendingMessages.set(id, resolve);

      // Send message
      this.targetWindow.postMessage(message, this.targetOrigin);

      // Timeout after 10 seconds
      setTimeout(() => {
        if (this.pendingMessages.has(id)) {
          this.pendingMessages.delete(id);
          resolve({ error: 'Message timeout' });
        }
      }, 10000);
    });
  }

  // Send message without expecting response
  sendNotification(type: string, data?: any): void {
    const message: WidgetMessage = {
      type,
      data,
      timestamp: Date.now()
    };

    this.targetWindow.postMessage(message, this.targetOrigin);
  }

  // Set event handlers
  setEventHandlers(handlers: WidgetEventHandlers): void {
    this.eventHandlers = { ...this.eventHandlers, ...handlers };
  }

  // Specific helper methods for common operations
  notifyReady(): void {
    this.sendNotification('WIDGET_READY');
  }

  notifyUserAction(action: string, payload: any): void {
    this.sendNotification('USER_ACTION', { action, payload });
  }

  notifyPlanChange(newPlan: string): void {
    this.sendNotification('PLAN_CHANGE', { newPlan });
  }

  notifyTokenUsage(tokensUsed: number): void {
    this.sendNotification('TOKEN_USAGE', { tokensUsed });
  }

  notifyError(message: string): void {
    this.sendNotification('ERROR', { message });
  }

  notifyResize(dimensions: { width: number; height: number }): void {
    this.sendNotification('RESIZE', { dimensions });
  }

  // Request configuration from parent
  async requestConfig(): Promise<any> {
    return this.sendMessage('REQUEST_CONFIG');
  }

  // Request user data from parent
  async requestUserData(): Promise<any> {
    return this.sendMessage('REQUEST_USER_DATA');
  }

  // Health check
  async ping(): Promise<boolean> {
    try {
      const response = await this.sendMessage('PING');
      return response.type === 'PONG';
    } catch {
      return false;
    }
  }

  // Cleanup
  destroy(): void {
    this.pendingMessages.clear();
    this.eventHandlers = {};
  }
}

// Auto-resize functionality for iframe widgets
export class AutoResizer {
  private bridge: PostMessageBridge;
  private observer?: ResizeObserver;
  private lastDimensions = { width: 0, height: 0 };

  constructor(bridge: PostMessageBridge) {
    this.bridge = bridge;
    this.setupAutoResize();
  }

  private setupAutoResize(): void {
    if (typeof ResizeObserver !== 'undefined') {
      this.observer = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const { width, height } = entry.contentRect;
          
          // Only notify if dimensions changed significantly
          if (
            Math.abs(width - this.lastDimensions.width) > 5 ||
            Math.abs(height - this.lastDimensions.height) > 5
          ) {
            this.lastDimensions = { width, height };
            this.bridge.notifyResize({ width, height });
          }
        }
      });

      // Observe document body
      this.observer.observe(document.body);
    } else {
      // Fallback for older browsers
      this.setupFallbackResize();
    }
  }

  private setupFallbackResize(): void {
    let lastWidth = window.innerWidth;
    let lastHeight = window.innerHeight;

    const checkResize = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;

      if (width !== lastWidth || height !== lastHeight) {
        lastWidth = width;
        lastHeight = height;
        this.bridge.notifyResize({ width, height });
      }
    };

    window.addEventListener('resize', checkResize);
    
    // Check periodically for content changes
    setInterval(checkResize, 1000);
  }

  destroy(): void {
    this.observer?.disconnect();
  }
}

// Export singleton instance for widget usage
export const widgetBridge = new PostMessageBridge();
export const autoResizer = new AutoResizer(widgetBridge);
