"""
Enhanced Error Handling System for FinanceGPT Pro
Provides centralized error handling, logging, and recovery mechanisms
"""

import logging
import traceback
from typing import Dict, Any, Optional, Callable, Union
from functools import wraps
from datetime import datetime
import requests
from enum import Enum

logger = logging.getLogger(__name__)

class ErrorType(Enum):
    """Classification of error types"""
    API_ERROR = "api_error"
    NETWORK_ERROR = "network_error"
    DATA_ERROR = "data_error"
    VALIDATION_ERROR = "validation_error"
    AUTHENTICATION_ERROR = "auth_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    TIMEOUT_ERROR = "timeout_error"
    SYSTEM_ERROR = "system_error"
    USER_ERROR = "user_error"

class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class FinanceError(Exception):
    """Custom exception class for finance-related errors"""
    
    def __init__(
        self, 
        message: str, 
        error_type: ErrorType = ErrorType.SYSTEM_ERROR,
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        details: Optional[Dict[str, Any]] = None,
        recoverable: bool = True
    ):
        super().__init__(message)
        self.message = message
        self.error_type = error_type
        self.severity = severity
        self.details = details or {}
        self.recoverable = recoverable
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert error to dictionary format"""
        return {
            "message": self.message,
            "error_type": self.error_type.value,
            "severity": self.severity.value,
            "details": self.details,
            "recoverable": self.recoverable,
            "timestamp": self.timestamp.isoformat()
        }

class ErrorHandler:
    """Centralized error handling and recovery system"""
    
    def __init__(self):
        self.error_counts = {}
        self.recovery_strategies = {}
        self._setup_default_strategies()
    
    def _setup_default_strategies(self):
        """Setup default recovery strategies"""
        self.recovery_strategies = {
            ErrorType.NETWORK_ERROR: self._handle_network_error,
            ErrorType.API_ERROR: self._handle_api_error,
            ErrorType.RATE_LIMIT_ERROR: self._handle_rate_limit_error,
            ErrorType.TIMEOUT_ERROR: self._handle_timeout_error,
            ErrorType.DATA_ERROR: self._handle_data_error,
        }
    
    def handle_error(
        self, 
        error: Exception, 
        context: str = "",
        fallback_data: Optional[Any] = None
    ) -> Dict[str, Any]:
        """
        Handle an error with appropriate logging and recovery
        
        Args:
            error: The exception that occurred
            context: Context information about where the error occurred
            fallback_data: Fallback data to return if recovery fails
            
        Returns:
            Dictionary with error information and recovery data
        """
        # Classify the error
        finance_error = self._classify_error(error, context)
        
        # Log the error
        self._log_error(finance_error, context)
        
        # Track error frequency
        self._track_error(finance_error)
        
        # Attempt recovery
        recovery_result = self._attempt_recovery(finance_error, fallback_data)
        
        return {
            "error": finance_error.to_dict(),
            "recovery": recovery_result,
            "context": context
        }
    
    def _classify_error(self, error: Exception, context: str) -> FinanceError:
        """Classify an error into appropriate type and severity"""
        
        if isinstance(error, FinanceError):
            return error
        
        error_msg = str(error)
        error_type = ErrorType.SYSTEM_ERROR
        severity = ErrorSeverity.MEDIUM
        recoverable = True
        details = {"original_error": error_msg, "context": context}
        
        # Network-related errors
        if isinstance(error, (requests.RequestException, requests.ConnectionError)):
            error_type = ErrorType.NETWORK_ERROR
            severity = ErrorSeverity.HIGH
        elif isinstance(error, requests.Timeout):
            error_type = ErrorType.TIMEOUT_ERROR
            severity = ErrorSeverity.MEDIUM
        elif isinstance(error, requests.HTTPError):
            if hasattr(error, 'response') and error.response:
                status_code = error.response.status_code
                if status_code == 429:
                    error_type = ErrorType.RATE_LIMIT_ERROR
                    severity = ErrorSeverity.HIGH
                elif status_code in [401, 403]:
                    error_type = ErrorType.AUTHENTICATION_ERROR
                    severity = ErrorSeverity.HIGH
                    recoverable = False
                elif status_code >= 500:
                    error_type = ErrorType.API_ERROR
                    severity = ErrorSeverity.HIGH
                else:
                    error_type = ErrorType.API_ERROR
                    severity = ErrorSeverity.MEDIUM
                details["status_code"] = status_code
        
        # Data-related errors
        elif isinstance(error, (ValueError, KeyError, TypeError)):
            error_type = ErrorType.DATA_ERROR
            severity = ErrorSeverity.MEDIUM
        
        # API-specific error patterns
        elif "quota" in error_msg.lower() or "limit" in error_msg.lower():
            error_type = ErrorType.RATE_LIMIT_ERROR
            severity = ErrorSeverity.HIGH
        elif "api" in error_msg.lower() and "key" in error_msg.lower():
            error_type = ErrorType.AUTHENTICATION_ERROR
            severity = ErrorSeverity.HIGH
            recoverable = False
        elif "timeout" in error_msg.lower():
            error_type = ErrorType.TIMEOUT_ERROR
            severity = ErrorSeverity.MEDIUM
        
        return FinanceError(
            message=error_msg,
            error_type=error_type,
            severity=severity,
            details=details,
            recoverable=recoverable
        )
    
    def _log_error(self, error: FinanceError, context: str):
        """Log error with appropriate level"""
        log_msg = f"[{error.error_type.value.upper()}] {error.message}"
        if context:
            log_msg += f" | Context: {context}"
        
        if error.severity == ErrorSeverity.CRITICAL:
            logger.critical(log_msg, extra={"error_details": error.details})
        elif error.severity == ErrorSeverity.HIGH:
            logger.error(log_msg, extra={"error_details": error.details})
        elif error.severity == ErrorSeverity.MEDIUM:
            logger.warning(log_msg, extra={"error_details": error.details})
        else:
            logger.info(log_msg, extra={"error_details": error.details})
    
    def _track_error(self, error: FinanceError):
        """Track error frequency for monitoring"""
        error_key = f"{error.error_type.value}:{error.message[:50]}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        
        # Alert if error frequency is high
        if self.error_counts[error_key] > 10:
            logger.critical(f"High frequency error detected: {error_key} ({self.error_counts[error_key]} occurrences)")
    
    def _attempt_recovery(self, error: FinanceError, fallback_data: Any) -> Dict[str, Any]:
        """Attempt to recover from the error"""
        if not error.recoverable:
            return {
                "success": False,
                "strategy": "none",
                "data": fallback_data,
                "message": "Error is not recoverable"
            }
        
        strategy_func = self.recovery_strategies.get(error.error_type)
        if strategy_func:
            try:
                return strategy_func(error, fallback_data)
            except Exception as recovery_error:
                logger.error(f"Recovery strategy failed: {str(recovery_error)}")
        
        return {
            "success": False,
            "strategy": "fallback",
            "data": fallback_data,
            "message": "Using fallback data"
        }
    
    def _handle_network_error(self, error: FinanceError, fallback_data: Any) -> Dict[str, Any]:
        """Handle network-related errors"""
        return {
            "success": True,
            "strategy": "fallback_data",
            "data": fallback_data,
            "message": "Using cached or mock data due to network issues"
        }
    
    def _handle_api_error(self, error: FinanceError, fallback_data: Any) -> Dict[str, Any]:
        """Handle API-related errors"""
        return {
            "success": True,
            "strategy": "fallback_data",
            "data": fallback_data,
            "message": "Using alternative data source due to API issues"
        }
    
    def _handle_rate_limit_error(self, error: FinanceError, fallback_data: Any) -> Dict[str, Any]:
        """Handle rate limiting errors"""
        return {
            "success": True,
            "strategy": "cached_data",
            "data": fallback_data,
            "message": "Using cached data due to rate limiting"
        }
    
    def _handle_timeout_error(self, error: FinanceError, fallback_data: Any) -> Dict[str, Any]:
        """Handle timeout errors"""
        return {
            "success": True,
            "strategy": "fallback_data",
            "data": fallback_data,
            "message": "Using fallback data due to timeout"
        }
    
    def _handle_data_error(self, error: FinanceError, fallback_data: Any) -> Dict[str, Any]:
        """Handle data processing errors"""
        return {
            "success": True,
            "strategy": "default_values",
            "data": fallback_data,
            "message": "Using default values due to data processing error"
        }

# Global error handler instance
error_handler = ErrorHandler()

def handle_errors(
    fallback_data: Any = None,
    context: str = "",
    reraise: bool = False
):
    """
    Decorator for automatic error handling

    Args:
        fallback_data: Data to return if error occurs
        context: Context information for logging
        reraise: Whether to reraise the exception after handling
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                func_context = context or f"{func.__module__}.{func.__name__}"
                error_result = error_handler.handle_error(e, func_context, fallback_data)

                if reraise:
                    raise

                # Return error information or fallback data
                if error_result["recovery"]["success"]:
                    return error_result["recovery"]["data"]
                else:
                    return {
                        "error": error_result["error"]["message"],
                        "error_type": error_result["error"]["error_type"],
                        "data": fallback_data
                    }

        return wrapper
    return decorator

def create_safe_response(
    data: Any = None,
    error_message: str = None,
    success: bool = True
) -> Dict[str, Any]:
    """Create a standardized response format"""
    return {
        "success": success,
        "data": data,
        "error": error_message,
        "timestamp": datetime.now().isoformat()
    }

def validate_ticker(ticker: str) -> bool:
    """Validate ticker symbol format"""
    if not ticker or not isinstance(ticker, str):
        return False

    # Basic ticker validation
    ticker = ticker.strip().upper()
    if len(ticker) < 1 or len(ticker) > 10:
        return False

    # Check for valid characters (letters and numbers only)
    if not ticker.replace('.', '').replace('-', '').isalnum():
        return False

    return True

def sanitize_user_input(user_input: str, max_length: int = 1000) -> str:
    """Sanitize user input to prevent issues"""
    if not user_input or not isinstance(user_input, str):
        return ""

    # Truncate if too long
    if len(user_input) > max_length:
        user_input = user_input[:max_length]

    # Remove potentially harmful characters
    sanitized = user_input.strip()

    return sanitized

class CircuitBreaker:
    """Circuit breaker pattern for API calls"""

    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == "OPEN":
            if self._should_attempt_reset():
                self.state = "HALF_OPEN"
            else:
                raise FinanceError(
                    "Service temporarily unavailable",
                    ErrorType.API_ERROR,
                    ErrorSeverity.HIGH,
                    recoverable=False
                )

        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise

    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        if self.last_failure_time is None:
            return True

        return (datetime.now() - self.last_failure_time).seconds >= self.recovery_timeout

    def _on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        self.state = "CLOSED"

    def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = datetime.now()

        if self.failure_count >= self.failure_threshold:
            self.state = "OPEN"

# Global circuit breakers for different services
api_circuit_breakers = {
    "alpha_vantage": CircuitBreaker(failure_threshold=3, recovery_timeout=300),
    "google_search": CircuitBreaker(failure_threshold=5, recovery_timeout=60),
    "gemini": CircuitBreaker(failure_threshold=10, recovery_timeout=120),
}
