"""
Advanced Analytics Service for FinanceGPT Pro
Provides sophisticated financial analysis, predictions, and insights
"""

import logging
import math
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import statistics

logger = logging.getLogger(__name__)

class AnalysisType(Enum):
    """Types of advanced analysis"""
    CORRELATION = "correlation"
    VOLATILITY = "volatility"
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    SECTOR_ANALYSIS = "sector_analysis"
    RISK_METRICS = "risk_metrics"

@dataclass
class CorrelationAnalysis:
    """Correlation analysis between stocks"""
    ticker1: str
    ticker2: str
    correlation_coefficient: float
    strength: str
    interpretation: str

@dataclass
class VolatilityAnalysis:
    """Volatility analysis for a stock"""
    ticker: str
    current_volatility: float
    historical_volatility: float
    volatility_percentile: float
    risk_level: str

@dataclass
class MomentumAnalysis:
    """Momentum analysis for a stock"""
    ticker: str
    short_term_momentum: float
    medium_term_momentum: float
    long_term_momentum: float
    momentum_score: float
    trend_strength: str

class AdvancedAnalyticsService:
    """Service for advanced financial analytics"""
    
    def __init__(self):
        self.sector_mappings = {
            "AAPL": "Technology",
            "MSFT": "Technology", 
            "GOOGL": "Technology",
            "TSLA": "Automotive",
            "AMZN": "E-commerce",
            "META": "Technology",
            "NVDA": "Technology",
            "JPM": "Financial",
            "JNJ": "Healthcare",
            "PG": "Consumer Goods"
        }
    
    def analyze_correlation(self, ticker1: str, ticker2: str, 
                          price_data1: List[float], price_data2: List[float]) -> CorrelationAnalysis:
        """Analyze correlation between two stocks"""
        try:
            if len(price_data1) != len(price_data2) or len(price_data1) < 2:
                raise ValueError("Invalid price data for correlation analysis")
            
            # Calculate returns
            returns1 = [(price_data1[i] - price_data1[i-1]) / price_data1[i-1] 
                       for i in range(1, len(price_data1))]
            returns2 = [(price_data2[i] - price_data2[i-1]) / price_data2[i-1] 
                       for i in range(1, len(price_data2))]
            
            # Calculate correlation coefficient
            correlation = self._calculate_correlation(returns1, returns2)
            
            # Interpret correlation strength
            if abs(correlation) >= 0.8:
                strength = "Very Strong"
            elif abs(correlation) >= 0.6:
                strength = "Strong"
            elif abs(correlation) >= 0.4:
                strength = "Moderate"
            elif abs(correlation) >= 0.2:
                strength = "Weak"
            else:
                strength = "Very Weak"
            
            # Generate interpretation
            direction = "positive" if correlation > 0 else "negative"
            interpretation = f"{ticker1} and {ticker2} show {strength.lower()} {direction} correlation ({correlation:.3f})"
            
            return CorrelationAnalysis(
                ticker1=ticker1,
                ticker2=ticker2,
                correlation_coefficient=correlation,
                strength=strength,
                interpretation=interpretation
            )
            
        except Exception as e:
            logger.error(f"Error in correlation analysis: {str(e)}")
            return CorrelationAnalysis(
                ticker1=ticker1,
                ticker2=ticker2,
                correlation_coefficient=0.0,
                strength="Unknown",
                interpretation=f"Analysis failed: {str(e)}"
            )
    
    def analyze_volatility(self, ticker: str, price_data: List[float], 
                          period_days: int = 30) -> VolatilityAnalysis:
        """Analyze volatility for a stock"""
        try:
            if len(price_data) < period_days:
                raise ValueError("Insufficient price data for volatility analysis")
            
            # Calculate returns
            returns = [(price_data[i] - price_data[i-1]) / price_data[i-1] 
                      for i in range(1, len(price_data))]
            
            # Current volatility (last 30 days)
            recent_returns = returns[-period_days:]
            current_volatility = statistics.stdev(recent_returns) * math.sqrt(252)  # Annualized
            
            # Historical volatility (full period)
            historical_volatility = statistics.stdev(returns) * math.sqrt(252)
            
            # Calculate percentile
            volatility_percentile = self._calculate_percentile(current_volatility, returns)
            
            # Determine risk level
            if current_volatility > 0.4:
                risk_level = "Very High"
            elif current_volatility > 0.3:
                risk_level = "High"
            elif current_volatility > 0.2:
                risk_level = "Medium"
            elif current_volatility > 0.1:
                risk_level = "Low"
            else:
                risk_level = "Very Low"
            
            return VolatilityAnalysis(
                ticker=ticker,
                current_volatility=current_volatility,
                historical_volatility=historical_volatility,
                volatility_percentile=volatility_percentile,
                risk_level=risk_level
            )
            
        except Exception as e:
            logger.error(f"Error in volatility analysis: {str(e)}")
            return VolatilityAnalysis(
                ticker=ticker,
                current_volatility=0.0,
                historical_volatility=0.0,
                volatility_percentile=0.0,
                risk_level="Unknown"
            )
    
    def analyze_momentum(self, ticker: str, price_data: List[float]) -> MomentumAnalysis:
        """Analyze momentum for a stock"""
        try:
            if len(price_data) < 60:  # Need at least 60 days for momentum analysis
                raise ValueError("Insufficient price data for momentum analysis")
            
            current_price = price_data[-1]
            
            # Short-term momentum (10 days)
            short_term_momentum = (current_price - price_data[-10]) / price_data[-10]
            
            # Medium-term momentum (30 days)
            medium_term_momentum = (current_price - price_data[-30]) / price_data[-30]
            
            # Long-term momentum (60 days)
            long_term_momentum = (current_price - price_data[-60]) / price_data[-60]
            
            # Calculate overall momentum score
            momentum_score = (short_term_momentum * 0.5 + 
                            medium_term_momentum * 0.3 + 
                            long_term_momentum * 0.2)
            
            # Determine trend strength
            if momentum_score > 0.1:
                trend_strength = "Strong Uptrend"
            elif momentum_score > 0.05:
                trend_strength = "Moderate Uptrend"
            elif momentum_score > -0.05:
                trend_strength = "Sideways"
            elif momentum_score > -0.1:
                trend_strength = "Moderate Downtrend"
            else:
                trend_strength = "Strong Downtrend"
            
            return MomentumAnalysis(
                ticker=ticker,
                short_term_momentum=short_term_momentum,
                medium_term_momentum=medium_term_momentum,
                long_term_momentum=long_term_momentum,
                momentum_score=momentum_score,
                trend_strength=trend_strength
            )
            
        except Exception as e:
            logger.error(f"Error in momentum analysis: {str(e)}")
            return MomentumAnalysis(
                ticker=ticker,
                short_term_momentum=0.0,
                medium_term_momentum=0.0,
                long_term_momentum=0.0,
                momentum_score=0.0,
                trend_strength="Unknown"
            )
    
    def analyze_sector_performance(self, tickers: List[str]) -> Dict[str, Any]:
        """Analyze performance by sector"""
        try:
            sector_performance = {}
            
            for ticker in tickers:
                sector = self.sector_mappings.get(ticker, "Unknown")
                if sector not in sector_performance:
                    sector_performance[sector] = {
                        "tickers": [],
                        "avg_performance": 0.0,
                        "best_performer": None,
                        "worst_performer": None
                    }
                
                # Mock performance data (in production, use real data)
                performance = self._get_mock_performance(ticker)
                sector_performance[sector]["tickers"].append({
                    "ticker": ticker,
                    "performance": performance
                })
            
            # Calculate sector averages and best/worst performers
            for sector, data in sector_performance.items():
                performances = [t["performance"] for t in data["tickers"]]
                data["avg_performance"] = statistics.mean(performances)
                
                best = max(data["tickers"], key=lambda x: x["performance"])
                worst = min(data["tickers"], key=lambda x: x["performance"])
                
                data["best_performer"] = best
                data["worst_performer"] = worst
            
            return sector_performance
            
        except Exception as e:
            logger.error(f"Error in sector analysis: {str(e)}")
            return {}
    
    def calculate_risk_metrics(self, ticker: str, price_data: List[float], 
                             benchmark_data: List[float] = None) -> Dict[str, Any]:
        """Calculate comprehensive risk metrics"""
        try:
            if len(price_data) < 30:
                raise ValueError("Insufficient data for risk metrics")
            
            # Calculate returns
            returns = [(price_data[i] - price_data[i-1]) / price_data[i-1] 
                      for i in range(1, len(price_data))]
            
            # Basic risk metrics
            volatility = statistics.stdev(returns) * math.sqrt(252)
            var_95 = self._calculate_var(returns, 0.95)
            max_drawdown = self._calculate_max_drawdown(price_data)
            
            risk_metrics = {
                "ticker": ticker,
                "volatility": volatility,
                "value_at_risk_95": var_95,
                "max_drawdown": max_drawdown,
                "sharpe_ratio": self._calculate_sharpe_ratio(returns),
                "sortino_ratio": self._calculate_sortino_ratio(returns)
            }
            
            # Add beta if benchmark data is provided
            if benchmark_data and len(benchmark_data) == len(price_data):
                benchmark_returns = [(benchmark_data[i] - benchmark_data[i-1]) / benchmark_data[i-1] 
                                   for i in range(1, len(benchmark_data))]
                risk_metrics["beta"] = self._calculate_beta(returns, benchmark_returns)
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {str(e)}")
            return {"error": str(e)}
    
    def _calculate_correlation(self, x: List[float], y: List[float]) -> float:
        """Calculate Pearson correlation coefficient"""
        n = len(x)
        if n != len(y) or n < 2:
            return 0.0
        
        mean_x = statistics.mean(x)
        mean_y = statistics.mean(y)
        
        numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
        sum_sq_x = sum((x[i] - mean_x) ** 2 for i in range(n))
        sum_sq_y = sum((y[i] - mean_y) ** 2 for i in range(n))
        
        denominator = math.sqrt(sum_sq_x * sum_sq_y)
        
        return numerator / denominator if denominator != 0 else 0.0
    
    def _calculate_percentile(self, value: float, data: List[float]) -> float:
        """Calculate percentile of value in data"""
        sorted_data = sorted(data)
        position = sum(1 for x in sorted_data if x <= value)
        return (position / len(sorted_data)) * 100
    
    def _get_mock_performance(self, ticker: str) -> float:
        """Get mock performance data (replace with real data in production)"""
        import random
        return random.uniform(-0.2, 0.3)  # Random performance between -20% and +30%
    
    def _calculate_var(self, returns: List[float], confidence: float) -> float:
        """Calculate Value at Risk"""
        sorted_returns = sorted(returns)
        index = int((1 - confidence) * len(sorted_returns))
        return sorted_returns[index] if index < len(sorted_returns) else 0.0
    
    def _calculate_max_drawdown(self, prices: List[float]) -> float:
        """Calculate maximum drawdown"""
        peak = prices[0]
        max_dd = 0.0
        
        for price in prices:
            if price > peak:
                peak = price
            drawdown = (peak - price) / peak
            max_dd = max(max_dd, drawdown)
        
        return max_dd
    
    def _calculate_sharpe_ratio(self, returns: List[float], risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio"""
        if not returns:
            return 0.0
        
        excess_returns = [r - risk_free_rate/252 for r in returns]  # Daily risk-free rate
        return statistics.mean(excess_returns) / statistics.stdev(excess_returns) if statistics.stdev(excess_returns) > 0 else 0.0
    
    def _calculate_sortino_ratio(self, returns: List[float], risk_free_rate: float = 0.02) -> float:
        """Calculate Sortino ratio"""
        if not returns:
            return 0.0
        
        excess_returns = [r - risk_free_rate/252 for r in returns]
        downside_returns = [r for r in excess_returns if r < 0]
        
        if not downside_returns:
            return float('inf')
        
        downside_deviation = math.sqrt(statistics.mean([r**2 for r in downside_returns]))
        return statistics.mean(excess_returns) / downside_deviation if downside_deviation > 0 else 0.0
    
    def _calculate_beta(self, stock_returns: List[float], market_returns: List[float]) -> float:
        """Calculate beta coefficient"""
        if len(stock_returns) != len(market_returns) or len(stock_returns) < 2:
            return 1.0
        
        covariance = statistics.covariance(stock_returns, market_returns)
        market_variance = statistics.variance(market_returns)
        
        return covariance / market_variance if market_variance > 0 else 1.0

# Global service instance
advanced_analytics_service = AdvancedAnalyticsService()
