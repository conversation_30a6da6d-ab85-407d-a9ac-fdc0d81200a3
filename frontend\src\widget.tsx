import React from 'react';
import ReactDOM from 'react-dom/client';
import { FinanceGPTWidget, FinanceGPTWidgetConfig } from './FinanceGPTWidget';
import './integration/WidgetIntegration';

// Widget standalone mode - for iframe integration
function initializeStandaloneWidget() {
  // Get configuration from URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const configParam = urlParams.get('config');
  const mode = urlParams.get('mode');

  let config: FinanceGPTWidgetConfig = {
    mode: 'full',
    theme: 'dark',
    height: '100vh',
    width: '100%'
  };

  // Parse configuration if provided
  if (configParam) {
    try {
      const decodedConfig = JSON.parse(atob(configParam));
      config = { ...config, ...decodedConfig };
    } catch (error) {
      console.warn('Failed to parse widget configuration:', error);
    }
  }

  // Override mode if specified in URL
  if (mode === 'widget') {
    config.mode = config.mode || 'full';
  }

  // Create root container
  const container = document.getElementById('finance-gpt-widget-root');
  if (!container) {
    console.error('Widget container not found');
    return;
  }

  // Initialize React app
  const root = ReactDOM.createRoot(container);
  
  // Enhanced config with postMessage communication
  const enhancedConfig: FinanceGPTWidgetConfig = {
    ...config,
    onUserAction: (action: string, data: any) => {
      // Send to parent window if in iframe
      if (window.parent !== window) {
        window.parent.postMessage({
          type: 'USER_ACTION',
          data: { action, payload: data }
        }, '*');
      }
      // Call original callback if provided
      config.onUserAction?.(action, data);
    },
    onPlanChange: (newPlan: string) => {
      if (window.parent !== window) {
        window.parent.postMessage({
          type: 'PLAN_CHANGE',
          data: { newPlan }
        }, '*');
      }
      config.onPlanChange?.(newPlan);
    },
    onTokenUsage: (tokensUsed: number) => {
      if (window.parent !== window) {
        window.parent.postMessage({
          type: 'TOKEN_USAGE',
          data: { tokensUsed }
        }, '*');
      }
      config.onTokenUsage?.(tokensUsed);
    }
  };

  // Render widget
  root.render(
    <React.StrictMode>
      <FinanceGPTWidget 
        config={enhancedConfig}
        className="standalone-widget"
      />
    </React.StrictMode>
  );

  // Listen for configuration updates from parent
  window.addEventListener('message', (event) => {
    if (event.data.type === 'CONFIG_UPDATE') {
      const newConfig = { ...enhancedConfig, ...event.data.config };
      root.render(
        <React.StrictMode>
          <FinanceGPTWidget 
            config={newConfig}
            className="standalone-widget"
          />
        </React.StrictMode>
      );
    }
  });

  // Notify parent that widget is ready
  if (window.parent !== window) {
    window.parent.postMessage({
      type: 'WIDGET_READY',
      data: { config: enhancedConfig }
    }, '*');
  }
}

// Initialize widget when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeStandaloneWidget);
} else {
  initializeStandaloneWidget();
}

// Export for module usage
export { FinanceGPTWidget, type FinanceGPTWidgetConfig };
export { FinanceGPTIntegration, FinanceGPTIframeIntegration } from './integration/WidgetIntegration';
