import React, { useState, useEffect } from 'react';
import './global.css';
import { PersonalizedChat } from './components/PersonalizedChat';
import { PortfolioTracker } from './components/PortfolioTrackerSimple';
import { AdvancedAnalytics } from './components/AdvancedAnalytics';
import { AuthProvider } from './contexts/AuthContext';

export interface FinanceGPTWidgetConfig {
  // Integration settings
  mode: 'chat' | 'portfolio' | 'analytics' | 'full';
  theme?: 'dark' | 'light' | 'auto';
  
  // API configuration
  apiEndpoint?: string;
  apiKey?: string;
  
  // Feature toggles
  features?: {
    chat?: boolean;
    portfolio?: boolean;
    analytics?: boolean;
    planSwitching?: boolean;
  };
  
  // Styling options
  styling?: {
    primaryColor?: string;
    backgroundColor?: string;
    borderRadius?: string;
    fontFamily?: string;
    compact?: boolean;
  };
  
  // User configuration
  user?: {
    id?: string;
    name?: string;
    tier?: 'free' | 'pro';
    tokens?: number;
  };
  
  // Event callbacks
  onUserAction?: (action: string, data: any) => void;
  onPlanChange?: (newPlan: string) => void;
  onTokenUsage?: (tokensUsed: number) => void;
  
  // Container settings
  height?: string;
  width?: string;
  maxHeight?: string;
  maxWidth?: string;
}

interface FinanceGPTWidgetProps {
  config: FinanceGPTWidgetConfig;
  className?: string;
}

export const FinanceGPTWidget: React.FC<FinanceGPTWidgetProps> = ({ 
  config, 
  className = '' 
}) => {
  const [userTokens, setUserTokens] = useState(config.user?.tokens || 500);
  const [userTier, setUserTier] = useState<'Basic' | 'Pro'>(
    config.user?.tier === 'pro' ? 'Pro' : 'Basic'
  );

  // Apply custom styling
  useEffect(() => {
    if (config.styling) {
      const root = document.documentElement;
      if (config.styling.primaryColor) {
        root.style.setProperty('--primary-color', config.styling.primaryColor);
      }
      if (config.styling.backgroundColor) {
        root.style.setProperty('--background-color', config.styling.backgroundColor);
      }
    }
  }, [config.styling]);

  // Handle token deduction
  const handleTokenDeduct = (amount: number) => {
    const newTokens = Math.max(0, userTokens - amount);
    setUserTokens(newTokens);
    config.onTokenUsage?.(amount);
    config.onUserAction?.('token_usage', { amount, remaining: newTokens });
  };

  // Handle plan changes
  const handlePlanChange = (newPlan: string) => {
    const newTier = newPlan === 'pro' ? 'Pro' : 'Basic';
    setUserTier(newTier);
    config.onPlanChange?.(newPlan);
    config.onUserAction?.('plan_change', { newPlan, newTier });
  };

  // Container styles
  const containerStyles: React.CSSProperties = {
    height: config.height || 'auto',
    width: config.width || '100%',
    maxHeight: config.maxHeight || 'none',
    maxWidth: config.maxWidth || 'none',
    borderRadius: config.styling?.borderRadius || '12px',
    fontFamily: config.styling?.fontFamily || 'inherit',
    background: config.styling?.backgroundColor || 'linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%)',
    overflow: 'hidden',
    position: 'relative'
  };

  // Render based on mode
  const renderContent = () => {
    switch (config.mode) {
      case 'chat':
        return (
          <div className="h-full">
            <PersonalizedChat
              userTokens={userTokens}
              userTier={userTier}
              onTokenDeduct={handleTokenDeduct}
            />
          </div>
        );

      case 'portfolio':
        return (
          <div className="h-full overflow-auto">
            <PortfolioTracker
              userTokens={userTokens}
              userTier={userTier}
              onTokenDeduct={handleTokenDeduct}
            />
          </div>
        );

      case 'analytics':
        return (
          <div className="h-full overflow-auto">
            <AdvancedAnalytics
              userTokens={userTokens}
              onTokenDeduct={handleTokenDeduct}
            />
          </div>
        );

      case 'full':
      default:
        return (
          <div className="h-full">
            <WidgetTabs
              config={config}
              userTokens={userTokens}
              userTier={userTier}
              onTokenDeduct={handleTokenDeduct}
              onPlanChange={handlePlanChange}
            />
          </div>
        );
    }
  };

  return (
    <AuthProvider>
      <div 
        className={`finance-gpt-widget ${className} ${config.styling?.compact ? 'compact' : ''}`}
        style={containerStyles}
      >
        {renderContent()}
      </div>
    </AuthProvider>
  );
};

// Widget-specific tabs component
interface WidgetTabsProps {
  config: FinanceGPTWidgetConfig;
  userTokens: number;
  userTier: 'Basic' | 'Pro';
  onTokenDeduct: (amount: number) => void;
  onPlanChange: (plan: string) => void;
}

const WidgetTabs: React.FC<WidgetTabsProps> = ({
  config,
  userTokens,
  userTier,
  onTokenDeduct,
  onPlanChange
}) => {
  const [activeTab, setActiveTab] = useState('chat');

  const availableTabs = [
    { id: 'chat', name: 'AI Chat', enabled: config.features?.chat !== false },
    { id: 'portfolio', name: 'Portfolio', enabled: config.features?.portfolio !== false },
    { id: 'analytics', name: 'Analytics', enabled: config.features?.analytics !== false }
  ].filter(tab => tab.enabled);

  const renderTabContent = () => {
    switch (activeTab) {
      case 'chat':
        return (
          <PersonalizedChat
            userTokens={userTokens}
            userTier={userTier}
            onTokenDeduct={onTokenDeduct}
          />
        );
      case 'portfolio':
        return (
          <PortfolioTracker
            userTokens={userTokens}
            userTier={userTier}
            onTokenDeduct={onTokenDeduct}
          />
        );
      case 'analytics':
        return (
          <AdvancedAnalytics
            userTokens={userTokens}
            onTokenDeduct={onTokenDeduct}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* Widget Header with Tabs */}
      <div className="bg-white/5 border-b border-white/20 p-2">
        <div className="flex space-x-1">
          {availableTabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`px-3 py-2 text-sm rounded transition-colors ${
                activeTab === tab.id
                  ? 'bg-blue-600 text-white'
                  : 'text-gray-300 hover:text-white hover:bg-white/10'
              }`}
            >
              {tab.name}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="flex-1 overflow-hidden">
        {renderTabContent()}
      </div>
    </div>
  );
};

// Export for standalone usage
export default FinanceGPTWidget;
