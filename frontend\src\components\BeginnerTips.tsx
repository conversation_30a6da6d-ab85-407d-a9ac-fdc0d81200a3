import React, { useState } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { 
  Lightbulb,
  X,
  BookOpen,
  TrendingUp,
  Shield,
  DollarSign,
  Target,
  ChevronRight
} from 'lucide-react';

interface BeginnerTipsProps {
  currentView: 'overview' | 'holdings' | 'performance';
  userProfile?: {
    experienceLevel: 'beginner' | 'intermediate' | 'advanced';
    riskTolerance: 'conservative' | 'moderate' | 'aggressive';
    investmentGoals: string[];
  } | null;
}

export const BeginnerTips: React.FC<BeginnerTipsProps> = ({ 
  currentView, 
  userProfile 
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [currentTip, setCurrentTip] = useState(0);

  if (!userProfile || userProfile.experienceLevel !== 'beginner') {
    return null;
  }

  const getTipsForView = () => {
    switch (currentView) {
      case 'overview':
        return [
          {
            icon: DollarSign,
            title: "Understanding Portfolio Value",
            content: "Your total portfolio value is the current worth of all your investments plus any cash. This number will fluctuate daily with market movements - that's completely normal!",
            action: "Don't check it obsessively - focus on long-term trends."
          },
          {
            icon: TrendingUp,
            title: "Gains and Losses Explained",
            content: "Green numbers mean your investments are worth more than you paid. Red numbers mean they're worth less. Remember: you only realize gains/losses when you sell!",
            action: "Stay calm during market volatility - it's part of investing."
          },
          {
            icon: Target,
            title: "Diversification Basics",
            content: "Don't put all your money in one stock or sector. Spread your investments across different companies and industries to reduce risk.",
            action: "Consider broad market ETFs for instant diversification."
          }
        ];
      
      case 'holdings':
        return [
          {
            icon: BookOpen,
            title: "What Are Holdings?",
            content: "Holdings are the individual stocks, ETFs, or funds you own. Each holding shows how many shares you have and what you paid for them.",
            action: "Start with 3-5 different holdings to keep it simple."
          },
          {
            icon: Shield,
            title: "ETFs vs Individual Stocks",
            content: "ETFs (Exchange Traded Funds) are like baskets containing many stocks. They're safer for beginners than picking individual companies.",
            action: "Consider starting with broad market ETFs like VTI or SPY."
          },
          {
            icon: DollarSign,
            title: "Dollar-Cost Averaging",
            content: "Instead of investing a lump sum, invest the same amount regularly (like monthly). This helps smooth out market ups and downs.",
            action: "Set up automatic investments to make it easier."
          }
        ];
      
      case 'performance':
        return [
          {
            icon: TrendingUp,
            title: "Reading Performance Charts",
            content: "Performance charts show how your investments have grown over time. Focus on long-term trends rather than daily fluctuations.",
            action: "Compare your performance to broad market indexes like the S&P 500."
          },
          {
            icon: Target,
            title: "Realistic Expectations",
            content: "The stock market historically returns about 10% annually over long periods, but expect lots of ups and downs along the way.",
            action: "Don't expect to get rich quick - successful investing takes time."
          },
          {
            icon: Shield,
            title: "Risk vs Return",
            content: "Higher potential returns usually come with higher risk. Your risk tolerance should match your investment timeline and goals.",
            action: "Young investors can typically take more risk than those near retirement."
          }
        ];
      
      default:
        return [];
    }
  };

  const tips = getTipsForView();
  
  if (tips.length === 0) return null;

  const currentTipData = tips[currentTip];
  const IconComponent = currentTipData.icon;

  return (
    <Card className="bg-gradient-to-br from-blue-900/20 to-purple-900/20 border-blue-500/30 overflow-hidden">
      <div className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <div className="p-1.5 bg-blue-600 rounded-lg">
              <Lightbulb className="h-4 w-4 text-white" />
            </div>
            <h3 className="text-white font-semibold text-sm">Beginner Tips</h3>
          </div>
          <Button
            onClick={() => setIsExpanded(!isExpanded)}
            variant="ghost"
            size="sm"
            className="text-gray-400 hover:text-white p-1"
          >
            {isExpanded ? <X className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
          </Button>
        </div>

        {!isExpanded ? (
          <div className="text-xs text-gray-300">
            Click to see helpful tips for {currentView}
          </div>
        ) : (
          <div className="space-y-4">
            {/* Tip Navigation */}
            <div className="flex space-x-1">
              {tips.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTip(index)}
                  className={`h-2 flex-1 rounded-full transition-colors ${
                    index === currentTip ? 'bg-blue-500' : 'bg-gray-600'
                  }`}
                />
              ))}
            </div>

            {/* Current Tip */}
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <div className="p-2 bg-blue-600/20 rounded-lg">
                  <IconComponent className="h-5 w-5 text-blue-400" />
                </div>
                <div className="flex-1">
                  <h4 className="text-white font-medium text-sm mb-1">
                    {currentTipData.title}
                  </h4>
                  <p className="text-gray-300 text-xs leading-relaxed mb-2">
                    {currentTipData.content}
                  </p>
                  <div className="p-2 bg-green-900/20 border border-green-500/30 rounded-lg">
                    <p className="text-green-200 text-xs">
                      <strong>Action:</strong> {currentTipData.action}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex justify-between items-center">
              <Button
                onClick={() => setCurrentTip(Math.max(0, currentTip - 1))}
                disabled={currentTip === 0}
                variant="outline"
                size="sm"
                className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700 disabled:opacity-50"
              >
                Previous
              </Button>
              
              <span className="text-xs text-gray-400">
                {currentTip + 1} of {tips.length}
              </span>
              
              <Button
                onClick={() => setCurrentTip(Math.min(tips.length - 1, currentTip + 1))}
                disabled={currentTip === tips.length - 1}
                variant="outline"
                size="sm"
                className="text-xs border-gray-600 text-gray-300 hover:bg-gray-700 disabled:opacity-50"
              >
                Next
              </Button>
            </div>

            {/* Personalized Tip Based on Profile */}
            {userProfile.riskTolerance === 'conservative' && currentView === 'overview' && (
              <div className="mt-3 p-2 bg-yellow-900/20 border border-yellow-500/30 rounded-lg">
                <p className="text-yellow-200 text-xs">
                  <strong>For Conservative Investors:</strong> Consider bond ETFs and dividend-paying stocks 
                  for more stable returns that match your risk tolerance.
                </p>
              </div>
            )}

            {userProfile.investmentGoals.includes('retirement') && currentView === 'performance' && (
              <div className="mt-3 p-2 bg-purple-900/20 border border-purple-500/30 rounded-lg">
                <p className="text-purple-200 text-xs">
                  <strong>Retirement Planning:</strong> Focus on long-term growth and consider 
                  tax-advantaged accounts like 401(k) or IRA for better returns.
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};
