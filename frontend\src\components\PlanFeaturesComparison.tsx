import React from 'react';
import { Card } from './ui/card';
import { <PERSON><PERSON> } from './ui/button';
import { Badge } from './ui/badge';
import { 
  Check, 
  X, 
  Star, 
  Zap, 
  Shield, 
  TrendingUp,
  BarChart3,
  <PERSON><PERSON>hart,
  Brain,
  FileText,
  Headphones,
  Clock,
  Infinity
} from 'lucide-react';

interface PlanFeaturesProps {
  onSelectPlan?: (planId: string) => void;
  currentTier?: string;
  showActions?: boolean;
}

export const PlanFeaturesComparison: React.FC<PlanFeaturesProps> = ({
  onSelectPlan,
  currentTier = 'free',
  showActions = true
}) => {
  const plans = [
    {
      id: 'free',
      name: 'FinanceGPT Basic',
      price: 'Free',
      priceValue: 0,
      description: 'Perfect for getting started with investment analysis',
      badge: null,
      badgeColor: '',
      cardStyle: 'border-gray-600 bg-gray-900/20',
      buttonStyle: 'bg-gray-600 hover:bg-gray-700',
      icon: <Shield className="h-6 w-6" />,
      features: {
        // Core Analysis
        basicAnalysis: true,
        technicalAnalysis: true,
        fundamentalAnalysis: true,
        sentimentAnalysis: true,
        
        // Advanced Features
        personalizedAdvice: true,
        portfolioTracking: false,
        imageAnalysis: false,
        comprehensiveReports: false,
        exportReports: false,
        
        // AI Features
        aiChatbot: true,
        marketInsights: false,
        riskAssessment: true,
        
        // Support & Limits
        prioritySupport: false,
        dailyQueries: 25,
        monthlyTokens: 500,
        imageAnalysisPerDay: 0,
        reportsPerMonth: 0
      }
    },
    {
      id: 'trial',
      name: 'FinanceGPT Pro Trial',
      price: 'Free Trial',
      priceValue: 0,
      description: '7-day trial with full Pro features',
      badge: 'TRIAL',
      badgeColor: 'bg-orange-500',
      cardStyle: 'border-orange-500/50 bg-gradient-to-br from-orange-600/10 to-red-600/10',
      buttonStyle: 'bg-orange-600 hover:bg-orange-700',
      icon: <Clock className="h-6 w-6" />,
      features: {
        // Core Analysis
        basicAnalysis: true,
        technicalAnalysis: true,
        fundamentalAnalysis: true,
        sentimentAnalysis: true,
        
        // Advanced Features
        personalizedAdvice: true,
        portfolioTracking: true,
        imageAnalysis: true,
        comprehensiveReports: true,
        exportReports: true,
        
        // AI Features
        aiChatbot: true,
        marketInsights: true,
        riskAssessment: true,
        
        // Support & Limits
        prioritySupport: true,
        dailyQueries: 'Unlimited',
        monthlyTokens: 10000,
        imageAnalysisPerDay: 50,
        reportsPerMonth: 100
      }
    },
    {
      id: 'pro',
      name: 'FinanceGPT Pro',
      price: '$29.99',
      priceValue: 29.99,
      description: 'Complete investment analysis suite for serious investors',
      badge: 'MOST POPULAR',
      badgeColor: 'bg-gradient-to-r from-blue-600 to-purple-600',
      cardStyle: 'border-blue-500/50 bg-gradient-to-br from-blue-600/10 to-purple-600/10',
      buttonStyle: 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700',
      icon: <Star className="h-6 w-6" />,
      features: {
        // Core Analysis
        basicAnalysis: true,
        technicalAnalysis: true,
        fundamentalAnalysis: true,
        sentimentAnalysis: true,
        
        // Advanced Features
        personalizedAdvice: true,
        portfolioTracking: true,
        imageAnalysis: true,
        comprehensiveReports: true,
        exportReports: true,
        
        // AI Features
        aiChatbot: true,
        marketInsights: true,
        riskAssessment: true,
        
        // Support & Limits
        prioritySupport: true,
        dailyQueries: 'Unlimited',
        monthlyTokens: 10000,
        imageAnalysisPerDay: 50,
        reportsPerMonth: 100
      }
    }
  ];

  const featureCategories = [
    {
      name: 'Core Analysis Features',
      icon: <BarChart3 className="h-5 w-5" />,
      features: [
        { key: 'basicAnalysis', name: 'Basic Stock Analysis', description: 'Price, volume, and basic metrics' },
        { key: 'technicalAnalysis', name: 'Technical Analysis', description: 'RSI, MACD, SMA, EMA, Bollinger Bands' },
        { key: 'fundamentalAnalysis', name: 'Fundamental Analysis', description: 'P/E ratio, market cap, financial metrics' },
        { key: 'sentimentAnalysis', name: 'Sentiment Analysis', description: 'News sentiment and market mood analysis' }
      ]
    },
    {
      name: 'Advanced Features',
      icon: <Zap className="h-5 w-5" />,
      features: [
        { key: 'personalizedAdvice', name: 'Personalized Investment Advice', description: 'AI recommendations based on your profile' },
        { key: 'portfolioTracking', name: 'Portfolio Tracking', description: 'Track and optimize your investments' },
        { key: 'imageAnalysis', name: 'Chart Image Analysis', description: 'Upload and analyze chart images' },
        { key: 'comprehensiveReports', name: 'Comprehensive Reports', description: 'Detailed analysis reports' },
        { key: 'exportReports', name: 'Export Reports', description: 'PDF and CSV export capabilities' }
      ]
    },
    {
      name: 'AI-Powered Features',
      icon: <Brain className="h-5 w-5" />,
      features: [
        { key: 'aiChatbot', name: 'AI Investment Chatbot', description: 'Chat with AI for investment guidance' },
        { key: 'marketInsights', name: 'Advanced Market Insights', description: 'Deep market analysis and trends' },
        { key: 'riskAssessment', name: 'Risk Assessment', description: 'Personalized risk analysis' }
      ]
    },
    {
      name: 'Usage Limits & Support',
      icon: <Headphones className="h-5 w-5" />,
      features: [
        { key: 'dailyQueries', name: 'Daily Queries', description: 'Number of analyses per day' },
        { key: 'monthlyTokens', name: 'Monthly Tokens', description: 'AI processing capacity per month' },
        { key: 'imageAnalysisPerDay', name: 'Image Analysis', description: 'Chart image analyses per day' },
        { key: 'reportsPerMonth', name: 'Reports per Month', description: 'Downloadable reports limit' },
        { key: 'prioritySupport', name: 'Priority Support', description: '24/7 priority customer support' }
      ]
    }
  ];

  const getFeatureValue = (plan: any, featureKey: string) => {
    const value = plan.features[featureKey];
    
    if (typeof value === 'boolean') {
      return value ? <Check className="h-4 w-4 text-green-400" /> : <X className="h-4 w-4 text-gray-500" />;
    }
    
    if (featureKey === 'dailyQueries' && value === 'Unlimited') {
      return <div className="flex items-center gap-1"><Infinity className="h-4 w-4 text-blue-400" /><span className="text-blue-400 text-sm">Unlimited</span></div>;
    }
    
    if (typeof value === 'number') {
      if (value === 0) {
        return <X className="h-4 w-4 text-gray-500" />;
      }
      return <span className="text-green-400 text-sm font-medium">{value.toLocaleString()}</span>;
    }
    
    return <span className="text-green-400 text-sm font-medium">{value}</span>;
  };

  const handlePlanSelect = (planId: string) => {
    if (onSelectPlan) {
      onSelectPlan(planId);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-white mb-4">Choose Your Plan</h2>
        <p className="text-gray-300 text-lg max-w-2xl mx-auto">
          Compare all available features and find the perfect plan for your investment journey
        </p>
      </div>

      {/* Plans Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {plans.map((plan) => (
          <Card key={plan.id} className={`relative p-6 ${plan.cardStyle} transition-all duration-300 hover:scale-105`}>
            {/* Badge */}
            {plan.badge && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <Badge className={`${plan.badgeColor} text-white px-3 py-1 text-xs font-bold`}>
                  {plan.badge}
                </Badge>
              </div>
            )}

            <div className="text-center">
              <div className="flex justify-center mb-4">
                <div className="p-3 bg-white/10 rounded-full">
                  {plan.icon}
                </div>
              </div>
              
              <h3 className="text-xl font-bold text-white mb-2">{plan.name}</h3>
              <div className="text-3xl font-bold text-white mb-2">
                {plan.price}
                {plan.priceValue > 0 && <span className="text-lg font-normal text-gray-400">/month</span>}
              </div>
              <p className="text-gray-300 text-sm mb-6">{plan.description}</p>

              {showActions && (
                <Button
                  onClick={() => handlePlanSelect(plan.id)}
                  disabled={currentTier === plan.id}
                  className={`w-full ${plan.buttonStyle} text-white py-3 font-semibold`}
                >
                  {currentTier === plan.id ? 'Current Plan' : 
                   plan.id === 'trial' ? 'Start Free Trial' :
                   plan.id === 'free' ? 'Switch to Basic' : 'Upgrade to Pro'}
                </Button>
              )}
            </div>
          </Card>
        ))}
      </div>

      {/* Detailed Features Comparison */}
      <Card className="p-6 bg-white/5 backdrop-blur-sm border border-white/20">
        <h3 className="text-2xl font-bold text-white mb-6 text-center">Detailed Features Comparison</h3>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="border-b border-white/20">
                <th className="text-left py-4 px-4 text-white font-semibold">Features</th>
                {plans.map((plan) => (
                  <th key={plan.id} className="text-center py-4 px-4 text-white font-semibold min-w-[120px]">
                    {plan.name}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {featureCategories.map((category) => (
                <React.Fragment key={category.name}>
                  <tr className="border-b border-white/10">
                    <td colSpan={plans.length + 1} className="py-4 px-4">
                      <div className="flex items-center gap-2 text-blue-400 font-semibold">
                        {category.icon}
                        <span>{category.name}</span>
                      </div>
                    </td>
                  </tr>
                  {category.features.map((feature) => (
                    <tr key={feature.key} className="border-b border-white/5 hover:bg-white/5">
                      <td className="py-3 px-4">
                        <div>
                          <div className="text-white font-medium">{feature.name}</div>
                          <div className="text-gray-400 text-sm">{feature.description}</div>
                        </div>
                      </td>
                      {plans.map((plan) => (
                        <td key={plan.id} className="py-3 px-4 text-center">
                          {getFeatureValue(plan, feature.key)}
                        </td>
                      ))}
                    </tr>
                  ))}
                </React.Fragment>
              ))}
            </tbody>
          </table>
        </div>
      </Card>

      {/* Call to Action */}
      {showActions && (
        <div className="text-center">
          <p className="text-gray-300 mb-4">
            Ready to supercharge your investment analysis?
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={() => handlePlanSelect('trial')}
              className="bg-orange-600 hover:bg-orange-700 text-white px-8 py-3"
            >
              Start 7-Day Free Trial
            </Button>
            <Button
              onClick={() => handlePlanSelect('pro')}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-3"
            >
              Upgrade to Pro Now
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
