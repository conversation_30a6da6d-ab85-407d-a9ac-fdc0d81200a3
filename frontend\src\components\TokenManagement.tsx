import React, { useState } from 'react';
import { Card } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';

interface TokenPackage {
  id: string;
  name: string;
  tokens: number;
  price: number;
  bonus: number;
  popular?: boolean;
}

interface TokenManagementProps {
  userTokens: number;
  onTokenPurchase: (packageId: string) => void;
  onClose: () => void;
  isOpen: boolean;
}

const tokenPackages: TokenPackage[] = [
  {
    id: 'starter',
    name: 'Starter Pack',
    tokens: 500,
    price: 4.99,
    bonus: 0
  },
  {
    id: 'professional',
    name: 'Professional',
    tokens: 2000,
    price: 14.99,
    bonus: 200,
    popular: true
  },
  {
    id: 'enterprise',
    name: 'Enterprise',
    tokens: 5000,
    price: 29.99,
    bonus: 1000
  },
  {
    id: 'unlimited',
    name: 'Unlimited Monthly',
    tokens: 999999,
    price: 99.99,
    bonus: 0
  }
];

export const TokenManagement: React.FC<TokenManagementProps> = ({
  userTokens,
  onTokenPurchase,
  onClose,
  isOpen
}) => {
  const [selectedPackage, setSelectedPackage] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);

  if (!isOpen) return null;

  const handlePurchase = async (packageId: string) => {
    setIsProcessing(true);
    setSelectedPackage(packageId);
    
    try {
      await onTokenPurchase(packageId);
    } finally {
      setIsProcessing(false);
      setSelectedPackage(null);
    }
  };

  const getTokenStatus = () => {
    if (userTokens > 1000) return { status: 'Excellent', color: 'text-green-600', bg: 'bg-green-100' };
    if (userTokens > 500) return { status: 'Good', color: 'text-blue-600', bg: 'bg-blue-100' };
    if (userTokens > 100) return { status: 'Low', color: 'text-yellow-600', bg: 'bg-yellow-100' };
    return { status: 'Critical', color: 'text-red-600', bg: 'bg-red-100' };
  };

  const tokenStatus = getTokenStatus();

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-lg max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-white/20">
        {/* Header */}
        <div className="p-6 border-b border-white/20">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold text-white">Token Management</h2>
              <p className="text-gray-300 mt-1">Purchase tokens to access premium analysis features</p>
            </div>
            <Button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
              size="sm"
            >
              ✕
            </Button>
          </div>

          {/* Current Balance */}
          <div className="mt-4 p-4 rounded-lg border-2 border-dashed border-white/30">
            <div className="flex items-center justify-between">
              <div>
                <span className="text-sm text-gray-300">Current Balance:</span>
                <div className="flex items-center space-x-2 mt-1">
                  <span className="text-2xl font-bold text-white">
                    {userTokens.toLocaleString()}
                  </span>
                  <span className="text-gray-400">tokens</span>
                </div>
              </div>
              <div className={`px-3 py-1 rounded-full ${tokenStatus.bg}`}>
                <span className={`text-sm font-medium ${tokenStatus.color}`}>
                  {tokenStatus.status}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Token Packages */}
        <div className="p-6">
          <h3 className="text-lg font-semibold text-white mb-4">Choose a Token Package</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {tokenPackages.map((pkg) => (
              <Card
                key={pkg.id}
                className={`relative p-6 cursor-pointer transition-all duration-200 hover:shadow-lg border-2 ${
                  pkg.popular 
                    ? 'border-blue-500 bg-blue-50' 
                    : 'border-gray-200 hover:border-blue-300'
                }`}
              >
                {/* Popular Badge */}
                {pkg.popular && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-600 text-white px-3 py-1">
                      Most Popular
                    </Badge>
                  </div>
                )}

                <div className="text-center">
                  <h4 className="text-xl font-bold text-white mb-2">{pkg.name}</h4>
                  
                  <div className="mb-4">
                    <span className="text-3xl font-bold text-blue-600">
                      ${pkg.price}
                    </span>
                    {pkg.id === 'unlimited' && (
                      <span className="text-gray-500 text-sm">/month</span>
                    )}
                  </div>

                  <div className="space-y-2 mb-6">
                    <div className="flex items-center justify-center space-x-2">
                      <span className="text-2xl font-bold text-white">
                        {pkg.tokens === 999999 ? '∞' : pkg.tokens.toLocaleString()}
                      </span>
                      <span className="text-gray-400">tokens</span>
                    </div>
                    
                    {pkg.bonus > 0 && (
                      <div className="text-green-600 font-medium">
                        + {pkg.bonus} bonus tokens!
                      </div>
                    )}
                    
                    <div className="text-sm text-gray-500">
                      {pkg.tokens === 999999 
                        ? 'Unlimited analyses per month'
                        : `~${Math.floor(pkg.tokens / 50)} comprehensive analyses`
                      }
                    </div>
                  </div>

                  <Button
                    onClick={() => handlePurchase(pkg.id)}
                    disabled={isProcessing}
                    className={`w-full ${
                      pkg.popular
                        ? 'bg-blue-600 hover:bg-blue-700 text-white'
                        : 'bg-gray-600 hover:bg-gray-700 text-white'
                    }`}
                  >
                    {isProcessing && selectedPackage === pkg.id ? (
                      <div className="flex items-center space-x-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        <span>Processing...</span>
                      </div>
                    ) : (
                      `Purchase ${pkg.name}`
                    )}
                  </Button>
                </div>
              </Card>
            ))}
          </div>

          {/* Usage Guide */}
          <div className="mt-8 p-4 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
            <h4 className="font-semibold text-white mb-3">Token Usage Guide</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-gray-300">
              <div>
                <div className="flex justify-between py-1">
                  <span>Price Check:</span>
                  <span className="font-medium text-white">10 tokens</span>
                </div>
                <div className="flex justify-between py-1">
                  <span>Sentiment Analysis:</span>
                  <span className="font-medium text-white">40 tokens</span>
                </div>
                <div className="flex justify-between py-1">
                  <span>Technical Analysis:</span>
                  <span className="font-medium text-white">50 tokens</span>
                </div>
              </div>
              <div>
                <div className="flex justify-between py-1">
                  <span>Fundamental Analysis:</span>
                  <span className="font-medium text-white">60 tokens</span>
                </div>
                <div className="flex justify-between py-1">
                  <span>Investment Recommendation:</span>
                  <span className="font-medium text-white">75 tokens</span>
                </div>
                <div className="flex justify-between py-1">
                  <span>Comprehensive Report:</span>
                  <span className="font-medium text-white">150 tokens</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
