# API Keys for Stock Analysis Agent
# Copy this file to .env and fill in your actual API keys

# Required: Google Gemini API Key
# Get from: https://ai.google.dev/
GEMINI_API_KEY=your_gemini_api_key_here

# Optional: Google Search API (for sentiment analysis and research)
# Get from: https://developers.google.com/custom-search/v1/introduction
GOOGLE_SEARCH_API_KEY=your_google_search_api_key_here
GOOGLE_SEARCH_ENGINE_ID=your_custom_search_engine_id_here

# Optional: Alpha Vantage API (for stock data)
# Get from: https://www.alphavantage.co/support/#api-key
STOCK_API_KEY=your_alpha_vantage_api_key_here

# Optional: LangSmith API Key (for monitoring and debugging)
# Get from: https://smith.langchain.com/
LANGSMITH_API_KEY=your_langsmith_api_key_here

# LangGraph Configuration
LANGGRAPH_URL=http://localhost:2024

# Database Configuration (for Docker)
POSTGRES_DB=langgraph
POSTGRES_USER=langgraph
POSTGRES_PASSWORD=langgraph
POSTGRES_HOST=langgraph-postgres
POSTGRES_PORT=5432

# Redis Configuration (for Docker)
REDIS_HOST=langgraph-redis
REDIS_PORT=6379
